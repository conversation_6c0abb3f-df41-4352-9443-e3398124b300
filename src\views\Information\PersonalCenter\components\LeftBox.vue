<template>
  <div class="left">
    <div class="avatar-box">
      <div class="flex-box">
        <el-avatar>
          <!-- 这里的图片要占满整个头像 ,平铺-->
          <el-image :class="{ 'c-customize-img': customizeClass }" :src="circleUrl" @error="handleImgError" lazy />
        </el-avatar>
        <svg-icon class="el-icon--left" icon-class="upload-new" @click="showUpdateAvater"></svg-icon>
      </div>
      <div class="flex-box info">
        <span style="color: #262626;font-size: 18px;font-weight: 550">{{ form.name }}</span>
        <span style="color: #727272;font-size: 14px;margin-top: 12px">{{ form.userType | serviceProvideFilter }}</span>
      </div>
    </div>
    <div class="split"></div>
    <el-menu default-active="1" class="el-menu-vertical-demo" @select="handleSelectMenu">
      <el-menu-item index="1">
        <div slot="title" class="title-slot">
          <span v-show="curIndex == '1'" class="decorate"></span>
          <span>{{ $t('Q30JLZF3GiZdfWq1MaCR4') }}</span>
        </div>
      </el-menu-item>
      <el-menu-item index="2">
        <div slot="title" class="title-slot">
          <span v-show="curIndex == '2'" class="decorate"></span>
          <span>{{ $t('lg.limits.change_Password') }}</span>
        </div>
      </el-menu-item>
      <el-menu-item index="3" v-if="[8].indexOf(+userType) !== -1 && currencyParams == 'CNY'">
        <div slot="title" class="title-slot">
          <span v-show="curIndex == '3'" class="decorate"></span>
          <span>{{ $t('L6xr2tfBSDAIDoOf-18gW') }}</span>
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 226px">{{ $t('oQ34kNi30enS9BhzsG0H-') }}</div>
            <img style="margin-left: 5px" src="@/assets/img/auth-tip.png" alt="" />
          </el-tooltip>
        </div>
      </el-menu-item>
      <el-menu-item index="4">
        <div slot="title" class="title-slot">
          <span v-show="curIndex == '4'" class="decorate"></span>
          <span>{{ $t('KP0yfMnOJWx1FBWS-opEk') }}</span>
        </div>
      </el-menu-item>
      <el-menu-item index="5">
        <div slot="title" class="title-slot">
          <span v-show="curIndex == '5'" class="decorate"></span>
          <span>{{ $t('DeCU3YJEQZ4m6WTKYOimH') }}</span>
        </div>
      </el-menu-item>
    </el-menu>
    <UpdateAvater ref="UpdateAvater" :visible.sync="updateAvaterVisible" @success="updateAvaterSuccess" />
  </div>
</template>

<script>
import userAvaterMixins from '@/mixins/userAvaterMixins'
import { mapGetters } from 'vuex'
import UpdateAvater from './UpdateAvater.vue'
const isDev = process.env.NODE_ENV === 'development'
let _api = ''
if (isDev) {
  _api = '/api'
}
export default {
  mixins: [userAvaterMixins],
  name: 'PersonalCenter',
  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },
  components: { UpdateAvater },
  data() {
    return {
      updateAvaterVisible: false, // 更换头像弹窗
      curIndex: 1, // 当前菜单
      form: {},
      service: {},
      headers: {
        token: this.$cookies.get('token')
      },
      customizeClass: false,
      defaultImage: require('@/assets/img/login/here/logo.png')
    }
  },
  computed: {
    ...mapGetters(['userType', 'currencyParams']),
    //  获取头像，用于拼接
    getAvatar() {
      let _url = `../..${_api}/image/getImage.do`
      return _url
    },

    circleUrl() {
      if (this.form.imageURL) {
        const url = this.getAvatar + '?imageId=' + this.form.imageURL + '&token=' + this.$cookies.get('token')
        return url
      } else if (!this.avatar || this.userPicStr) {
        return this.userPicStr
      } else {
        return this.defaultImage
      }
    }
  },
  watch: {
    info: {
      handler(val) {
        this.form = { ...val } || {}
      }
    }
  },
  created() {
    this.$emit('select', '1')
  },
  methods: {
    handleImgError(err) {
      err.target.src = this.defaultImage
    },
    // 打开更新头像弹窗
    showUpdateAvater() {
      this.updateAvaterVisible = true
    },
    // 选择菜单
    handleSelectMenu(index) {
      this.curIndex = index
      this.$emit('select', index)
    },
    //  上传头像前
    handleBefore(file) {
      const size = file.size / 1024 / 1024
      if (size > 3) {
        this.$message.error(this.$t('lg.uploadAvatar') + '！')
        return false
      }
    },
    //  上传头像成功时
    handleSuccess(response, file, fileList) {
      console.log(response)
      if (response.data) {
        this.form.imageURL = response.data
        this.$store.commit('user/SET_AVATAR', response.data)
        this.$message.success(this.$t('IokO2UlgeBdvUDTLyX6ln') + '！')
        return
      }
      switch (response.code) {
        case '-102':
          this.$message.error(this.$t('8E_5DReq4p9vShgeUzwDv'))
          break
        default:
          this.$message.error(this.$t('lg.fail'))
      }
    },
    // 更新头像成功
    updateAvaterSuccess({ imageId }) {
      this.updateAvaterVisible = false
      this.form.imageURL = imageId
    }
  }
}
</script>

<style lang="scss" scoped>
.left {
  width: 335px;
  height: 100%;
  border-right: 1px solid #efefef;
  .avatar-box {
    height: 175px;
    display: flex;
    justify-content: center;
    align-items: center;
    .flex-box {
      position: relative;
      .el-avatar {
        width: 94px;
        height: 94px;
        .el-image {
          width: 100%;
          height: 100%;
        }
      }
      .el-icon--left {
        position: absolute;
        font-size: 26px;
        left: 60px;
        top: 69px;
        cursor: pointer;
      }
      // .upload {
      //   margin-left: 30px;
      //   line-height: 40px;
      //   position: relative;
      //   .el-icon--left {
      //     position: absolute;
      //     font-size: 26px;
      //     left: -60px;
      //     top: 42px;
      //   }
      // }
    }
    .info {
      display: flex;
      justify-content: flex-start;
      flex-direction: column;
      margin-left: 10px;
    }
  }
  .split {
    height: 1px;
    background-color: #efefef;
    width: 80%;
    margin: auto;
  }
  .el-menu {
    border-right: none;
    .el-menu-item:hover {
      background-color: #fff;
      color: #3370ff;
    }
    .el-menu-item.is-active {
      background-color: #fff;
    }
  }
  .title-slot {
    display: flex;
    align-items: center;
    padding-left: 53px;
    position: relative;
    .decorate {
      position: absolute;
      width: 2px;
      height: 18px;
      border-radius: 2px;
      background-color: #3370ff;
      left: 40px;
    }
  }
}
.c-customize-img {
  background-color: #fbfbfb;
}
</style>
