import { i18n } from '@/i18n'

/**
 * 按系列分组处理设备列表（用于多选，不包含特殊选项）
 * @param {Array} devices - 设备列表
 * @returns {Object} - 分组后的设备选项和设备列表
 */
export const processDeviceListForMultiSelect = devices => {
  if (!Array.isArray(devices) || devices.length === 0) {
    return { options: [], deviceList: [], allDeviceIds: [] }
  }

  // 按设备系列分组
  const groupedDevices = {}
  devices.forEach(device => {
    // 提取系列名称，假设系列名称是型号的第一部分，支持大小写
    const seriesMatch = device.machineTypeName.match(/^([A-Za-z]+)/)
    const series = seriesMatch ? seriesMatch[0].toUpperCase() : i18n.t('lg.logDict.other')

    if (!groupedDevices[series]) {
      groupedDevices[series] = []
    }

    groupedDevices[series].push({
      ...device,
      disabled: false
    })
  })

  // 转换为el-select组需要的格式，并按A-Z排序
  const options = Object.keys(groupedDevices)
    .sort((a, b) => a.localeCompare(b)) // 按字母顺序排序
    .map(series => ({
      label: series,
      options: groupedDevices[series]
    }))

  // 获取所有设备ID列表
  const allDeviceIds = devices.map(device => device.machineTypeId)
  const deviceList = [...allDeviceIds]

  return { options, deviceList, allDeviceIds }
}

/**
 * 检查是否选中了所有设备
 * @param {Array} selectedDevices - 已选中的设备ID数组
 * @param {Array} allDeviceIds - 所有设备ID数组
 * @returns {boolean} - 是否全选
 */
export const isAllDevicesSelected = (selectedDevices, allDeviceIds) => {
  if (!Array.isArray(selectedDevices) || !Array.isArray(allDeviceIds)) {
    return false
  }
  return selectedDevices.length === allDeviceIds.length && allDeviceIds.every(id => selectedDevices.includes(id))
}
