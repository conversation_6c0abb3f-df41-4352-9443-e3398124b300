<template>
  <div class="edit">
    <base-dialog
      @cancel="handleClose"
      @ok="handleConfirm"
      :title="title"
      :visible.sync="dialogVisible"
      width="1054px"
      top="5%"
      :before-close="handleClose"
      @open="handleOpen"
    >
      <div class="form-container">
        <!-- 左侧语言切换Tab-->
        <el-tabs v-if="isAbroad()" v-model="currentLang" tab-position="left" class="lang-tabs" :before-leave="handleBeforeLeave">
          <el-tab-pane v-for="lang in languageOptions" :key="lang.value" :label="lang.label" :name="lang.value"> </el-tab-pane>
        </el-tabs>

        <div class="right-form">
          <el-form ref="form" :rules="rules" :model="editForm" :label-width="isCNLang ? '100px' : '150px'">
            <!-- 左侧tab栏切换后，这里的每一个item可编辑 -->
            <div class="editable-info">
              <el-form-item :label="$t('uigvo2391weT6pPqyfxYg') + '：'" :prop="`langData.${currentLang}.packName`">
                <el-input class="width-240" v-model="editForm.langData[currentLang].packName" :disabled="disabled"></el-input>
              </el-form-item>
              <el-form-item :label="$t('EoOkU9Au2oolyDpxoABEO') + '：'" class="form-desc">
                <!-- <el-input type="textarea" v-model="editForm.packContent" :rows="6" :disabled="disabled"></el-input> -->
                <Tinymce ref="tinymceRef" v-model="editForm.langData[currentLang].packContent" v-if="dialogVisible" :disabled="disabled" :height="200" />
              </el-form-item>
            </div>
            <!--左侧tab栏切换后，以下的每一个item不可编辑 -->
            <el-form-item :label="$t('Cc1uwqoUoGjbyyWsaRu8q') + '：'" v-if="type === 0">
              <CopyPackage v-show="!disabled && onDefaultTab" class="width-240" :disabled="disabled" ref="CopyPackage" @select="handleCopyPackage" />
              <el-input class="width-240" v-show="disabled || !onDefaultTab" disabled v-model="editForm.copyPackageName"></el-input>
            </el-form-item>
            <!-- 场景选择框 -->
            <el-form-item :label="$t('场景') + '：'" prop="sceneId">
              <el-select class="width-240" v-model="editForm.sceneId" :disabled="disabled || !onDefaultTab" @change="handleSceneChange">
                <el-option v-for="item in sceneOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
            <!-- 套餐类型选择框 -->
            <el-form-item ref="packageTypeFormItemRef" :label="$t('qzwEjrvjWERzbbwcvI5rK') + '：'" prop="packType">
              <el-select class="width-240" v-model="editForm.packType" :disabled="disabled || !onDefaultTab || !editForm.sceneId">
                <el-option v-for="item in packageTypeList" :key="item.packType" :label="item.packTypeName" :value="Number(item.packType)"> </el-option>
              </el-select>
            </el-form-item>
            <!-- 套餐性质 -->
            <el-form-item :label="$t('套餐性质') + '：'" prop="packageNature">
              <el-select class="width-240" v-model="editForm.packageNature" :disabled="disabled || !onDefaultTab">
                <el-option v-for="item in packNatureOptions" :key="item.value" :label="item.label" :value="Number(item.value)"> </el-option>
              </el-select>
            </el-form-item>

            <!-- 权益等级 -->
            <el-form-item :label="$t('权益等级') + '：'" prop="level">
              <el-select class="width-240" v-model="editForm.level" :disabled="disabled || !onDefaultTab">
                <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="Number(item.value)"> </el-option>
              </el-select>
            </el-form-item>

            <el-form-item :label="$t('试用套餐') + '：'" prop="isTrial">
              <el-select class="width-240" v-model="editForm.isTrial" :disabled="disabled || !onDefaultTab">
                <el-option v-for="item in isTrialOptions" :key="item.value" :label="item.label" :value="Number(item.value)"> </el-option>
              </el-select>
            </el-form-item>

            <!-- 2025-06-16 v2.9.0 适用类型选择框隐藏，默认值为2，即全部设备 -->
            <!--  <el-form-item :label="$t('2247BJLuV1ixtjGA1O7gV') + '：'" prop="wirelessType">
              <el-select class="width-240" v-model="editForm.wirelessType" :disabled="disabled || !onDefaultTab">
                <el-option v-for="item in applyOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item> -->
            <!--   2025-06-16 v2.9.0 默认值为是，即上架所有套餐默认代理给全部服务商，并且选项入口隐藏 -->
            <!-- <el-form-item :label="$t('vF3kO35MlJqcY7R7BojED') + '：'" prop="universal">
              <el-select class="width-240" v-model="editForm.universal" :disabled="disabled || !onDefaultTab">
                <el-option v-for="item in commonOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item> -->
            <!-- 2025-06-16 v2.9.0 默认值为空，即不选择标准套餐 -->
            <!-- <el-form-item :label="$t('0gTRzqUCLGERAfFhL-8y3') + '：'" prop="standard">
              <el-select class="width-240" v-model="editForm.standard" :disabled="disabled || !onDefaultTab">
                <el-option v-for="item in standardOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item :label="$t('vl7XPni0Dn5MHXeBkgUvR') + '：'" prop="primePrice" v-if="showCost">
              <el-input class="width-240" v-model="editForm.primePrice" :disabled="true"></el-input>
              <svg-icon icon-class="tip3" :title="$t('N5usFqbTBj_x-AGRsPR8s')" style="margin-left:6px">{{ $t('lg.limits.Add_customer') }}</svg-icon>
            </el-form-item>
            <el-form-item :label="$t('wgxerTVV3ubJSwGQbIRXK') + '：'" prop="guidingPrice">
              <el-input class="width-240" v-model="editForm.guidingPrice" :disabled="disabled || !onDefaultTab || guidingPriceDisabled"></el-input>
            </el-form-item>
            <el-form-item :label="$t('Zbkw-Z-GkLdk92q2GqQb9') + '：'" prop="startTime">
              <el-date-picker
                class="width-240"
                v-model="editForm.startTime"
                :disabled="disabled || !onDefaultTab"
                type="datetime"
                format="yyyy-MM-dd HH:mm:ss"
                default-time="00:00:00"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item :label="$t('tjh3v6c9w4iWKRhEsxCel') + '：'" prop="endTime">
              <el-date-picker
                class="width-240"
                v-model="editForm.endTime"
                :disabled="disabled || !onDefaultTab"
                type="datetime"
                format="yyyy-MM-dd HH:mm:ss"
                default-time="23:59:59"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item :label="$t('ZEGgtLzv8abXPKrlPdzUC') + '：'" prop="renewType">
              <el-select class="width-240" v-model="editForm.renewType" :disabled="editDisabled || !onDefaultTab">
                <el-option v-for="item in renewTypeOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('9FY6SGcGYrCwFEcACJ5BV') + '：'" prop="isDailyPrice">
              <el-select class="width-240" v-model="editForm.isDailyPrice" :disabled="disabled || !onDefaultTab">
                <el-option v-for="item in commonOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
              <svg-icon icon-class="tip3" :title="'套餐价格 / 套餐时长；例如 0.53/天'" style="margin-left:6px" />
            </el-form-item>
            <el-form-item :label="$t('m8MBU7gaNdNDQorXi7_92') + '：'" prop="additionalDesc">
              <el-input class="width-240" v-model="editForm.additionalDesc" maxlength="30" :disabled="disabled || !onDefaultTab"></el-input>
              <svg-icon icon-class="tip3" :title="'该描述会展示在App端套餐下方'" style="margin-left:6px" />
            </el-form-item>
          </el-form>
          <div class="table">
            <AddIncrement
              ref="AddIncrement"
              :type="type"
              :disabled="disabled"
              :onDefaultTab="onDefaultTab"
              :packTypeId="packTypeId"
              :data="incrementData"
              @addRow="handleAddRow"
              @getPrimePrice="getPrimePrice"
            />
          </div>
        </div>
      </div>
    </base-dialog>
  </div>
</template>

<script>
import { _getPackageDetailInfo, _addPackageSetting, _editPackageSetting, _getHistoryPackageDetail } from '@/api/order.js'
import { DateFromLocalToUTC } from '@/utils/common.js'
import AddIncrement from './AddIncrement.vue'
import CopyPackage from './CopyPackage.vue'
import Tinymce from '@/components/Tinymce/index.vue'
import { isAbroad } from '@/utils/judge.js'
import { getOptionsByDictCode } from '@/utils/dict'
import store from '@/store'
import { _getPackTypeListByScene } from '@/api/packageType'
import { _fetchSceneList } from '@/api/scene'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {}
    },
    type: {
      // 操作类型 0-新增 1-详情 2-编辑
      type: [Number, String],
      default: 1
    },
    showCost: {
      // 是否显示成本价
      type: Boolean,
      default: true
    },
    id: {
      type: [Number, String],
      default: undefined
    },
    searchType: {
      // 查询类型 1 实时数据 2 历史数据
      type: [Number, String],
      default: 1
    }
  },
  components: {
    AddIncrement,
    CopyPackage,
    Tinymce
  },
  data() {
    const checkGuidingPrice = (rule, value, callback) => {
      if (value === '' || value === null || value === undefined) {
        return callback(new Error(this.$t('hMbxn5NOJ97gDiey6VkNK')))
      } else if (!/^\d+(\.\d{1,2})?$/.test(+value)) {
        return callback(new Error(this.$t('M_FhAqKNUJRMUlAI_5Vg7')))
      } else {
        callback()
      }
    }
    return {
      currentLang: this.isAbroad() ? 'en' : 'cn', // 后续初始化时会用 defaultLang 替代
      sceneOptions: [],
      packNatureOptions: [],
      levelOptions: [],
      languageOptions: [
        { label: '英语', value: 'en' },
        { label: '简体中文', value: 'cn' },
        { label: '繁体中文', value: 'hk' },
        { label: '西班牙语', value: 'es' },
        { label: '葡萄牙语', value: 'pt' },
        { label: '俄语', value: 'ru' },
        { label: '德语', value: 'de' },
        { label: '法语', value: 'fr' }
      ],
      packId: undefined,
      packInfo: {},
      incrementData: [], //增值功能数据
      editForm: {
        serviceProviderId: this.$cookies.get('bisUserId'),
        langData: {
          en: { packName: '', packContent: '', nameId: undefined, contentId: undefined },
          cn: { packName: '', packContent: '', nameId: undefined, contentId: undefined },
          hk: { packName: '', packContent: '', nameId: undefined, contentId: undefined },
          es: { packName: '', packContent: '', nameId: undefined, contentId: undefined },
          pt: { packName: '', packContent: '', nameId: undefined, contentId: undefined },
          ru: { packName: '', packContent: '', nameId: undefined, contentId: undefined },
          de: { packName: '', packContent: '', nameId: undefined, contentId: undefined },
          fr: { packName: '', packContent: '', nameId: undefined, contentId: undefined }
        },
        packType: undefined,
        wirelessType: 2,
        universal: 1, //  2025-06-16 v2.9.0 默认值为是，即上架所有套餐默认代理给全部服务商，并且选项入口隐藏
        standard: '',
        primePrice: '',
        guidingPrice: '',
        startTime: undefined,
        endTime: undefined,
        renewType: 0,
        isDailyPrice: 0,
        additionalDesc: '',

        packId: undefined
      },
      disabled: false,
      guidingPriceDisabled: false, //手机定位类-9，好友增加类-12，禁用输入指导价
      packTypeOptions: [
        { label: this.$t('-sHvr4dqzhBg8zhkGisc4'), value: 1 },
        { label: this.$t('WNFXoZ2k0AQRuxAeHiePH'), value: 2 },
        { label: this.$t('WYZoPhp_aCouZhtTNRx_v'), value: 3 },
        { label: this.$t('ulJ0IYNw9yhO3xtOejMkq'), value: 4 },
        { label: this.$t('BxnsRGI8yOGF3AIgmEDPA'), value: 5 },
        { label: this.$t('S5BLPa5cR1lfkPtX8C9ra'), value: 6 },
        { label: this.$t('ZVF3com1sFG-XrZC-livm'), value: 7 },
        { label: this.$t('by_j84zg2KXDekxc0ORKK'), value: 9 },
        { label: this.$t('gi1Rx6yaRf1mw_Ma10Og7'), value: 10 },
        { label: this.$t('5l9_lUSa6C_lFR_eESOlJ'), value: 11 },
        { label: this.$t('PjztEjunq2DdH8CUhaTom'), value: 12 },
        { label: this.$t('unLEXxQ5Se8zqvgBrB8Qc'), value: 13 },
        { label: this.$t('p0b0S03PgYUBMU8N6cMsY'), value: 8 }
      ],
      applyOptions: [
        { label: this.$t('iqmW63hnN6IZmBcr4Bx_N'), value: 2 },
        { label: this.$t('rY_cnjuM0KhLnfuxRdY7Y'), value: 0 },
        { label: this.$t('VLVXHDOnSopPJux0MHDsU'), value: 1 }
      ],
      isTrialOptions: [
        { label: this.$t('SrGP45dYF4TfIMabJ8GI0'), value: 1 },
        { label: this.$t('PSJif2BD7ijRnlbhUCzbL'), value: 0 }
      ],
      commonOptions: [
        { label: this.$t('SrGP45dYF4TfIMabJ8GI0'), value: 1 },
        { label: this.$t('PSJif2BD7ijRnlbhUCzbL'), value: 0 }
      ],
      standardOptions: [
        { label: this.$t('SrGP45dYF4TfIMabJ8GI0'), value: 1 },
        { label: this.$t('PSJif2BD7ijRnlbhUCzbL'), value: 0 }
      ],
      renewTypeOptions: [
        { label: this.$t('PSJif2BD7ijRnlbhUCzbL'), value: 0 },
        { label: this.$t('lg._month'), value: 1 },
        { label: this.$t('sfwA5_FWQER4kGGM23f08'), value: 2 },
        { label: this.$t('lg._year'), value: 3 }
      ],
      rules: {
        'langData.en.packName': [
          {
            required: true,
            message: this.$t('RB650X0GgnbB2n4QFIvyn'),
            trigger: 'blur'
          }
        ],
        sceneId: [
          {
            required: true,
            message: '请选择场景',
            trigger: 'blur'
          }
        ],
        packType: [
          {
            required: true,
            message: this.$t('DFz6LZXDUzGC9h1Hnul2u'),
            trigger: 'blur'
          }
        ],
        packageNature: [
          {
            required: true,
            message: '请选择套餐性质',
            trigger: 'blur'
          }
        ],
        level: [
          {
            required: true,
            message: '请选择权益等级',
            trigger: 'blur'
          }
        ],
        isTrial: [
          {
            required: true,
            message: '请选择是否为试用套餐',
            trigger: 'blur'
          }
        ],
        guidingPrice: [{ required: true, validator: checkGuidingPrice, trigger: 'blur' }],
        startTime: [
          {
            required: true,
            message: this.$t('lg.selectStartTime'),
            trigger: 'blur'
          }
        ],
        endTime: [
          {
            required: true,
            message: this.$t('lg.selectEndTime'),
            trigger: 'blur'
          }
        ]
      },
      packageTypeList: [],
      editDisabled: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val) // openCardDialog改变的时候通知父组件
      }
    },
    title() {
      let str = ''
      switch (Number(this.type)) {
        case 0:
          str = this.$t('a9_Kks1xUEIPs5523_ebG')
          break
        case 1:
          str = this.$t('LTht5hSzFVGNGPsD-aWdO')
          break
        case 2:
          str = this.$t('CTqygomw_FwACb306Vgef')
          break
        default:
          break
      }
      return str
    },
    isCNLang() {
      const lang = store.getters['lang']
      return lang === 'cn'
    },
    defaultLang() {
      return this.isAbroad() ? 'en' : 'cn'
    },
    onDefaultTab() {
      return this.currentLang === this.defaultLang
    },
    packTypeId() {
      //v2.9.0 后端屎山代码改不动，保留了历史的packType，新增套餐类型管理id字段名的packTypeId，需要兼容处理一下
      //这个packTypeId 主要是传参获取增值功能选项用
      //这里以后可能会有坑，两个字段功能是一样的，注意区分好并且不要混用，跟后端沟通吧。。。心累
      return this.packageTypeList.find(item => item.packType === this.editForm.packType)?.packTypeId
    }
  },
  watch: {
    id: {
      immediate: true,
      handler(val) {
        this.packId = val || undefined
      }
    },
    'editForm.sceneId': {
      immediate: true,
      handler(val) {
        this.getPackTypeListByScene()
      }
    }
  },
  methods: {
    isAbroad,
    getOptionsByDictCode,
    handleSceneChange(val) {
      this.getPackTypeListByScene()
    },
    handleAddRow() {
      if (this.editForm.packType === undefined) {
        this.$message.warning('请先选择套餐类型')
        this.$nextTick(() => {
          this.$refs.AddIncrement.clearRow()
        })
        return
      }
    },
    handleBeforeLeave(tab, oldTab) {
      if (
        this.editForm.langData[tab]?.packContent === '' ||
        this.editForm.langData[tab]?.packContent === null ||
        this.editForm.langData[tab]?.packContent === undefined
      ) {
        this.$refs.tinymceRef.setContent('')
      }
      if (oldTab === 'en') {
        this.$refs.form.validate(async valid => {
          if (!valid) {
            this.$message.error(this.$t('FIgAdMCZbTjUXRp-BVY-9'))
            this.$nextTick(() => {
              this.currentLang = oldTab
            })
            return false
          }
        })
      }
    },

    // 获取成本价
    getPrimePrice(price) {
      this.editForm.primePrice = price
      if ([9, 12].includes(this.editForm.packType)) {
        //手机定位类-9，好友增加类-12，禁用输入指导价，指导价由成本价格*2得到
        this.editForm.guidingPrice = this.editForm.primePrice * 2
      }
    },
    // 复制套餐
    handleCopyPackage(info) {
      if (info) {
        // this.packInfo = info || {}
        this.packId = info.packId || undefined
        this.getPackageDetailInfo()
      } else {
        this.resetForm()
      }
    },
    // 查询套餐详情
    async getPackageDetailInfo() {
      try {
        let res
        if (this.searchType == 1) {
          res = await _getPackageDetailInfo({ packId: this.packId })
        } else if (this.searchType === 2) {
          res = await _getHistoryPackageDetail({ packLogId: this.packId })
        }
        if (res.ret === 1 && res.data) {
          this.setDetailDataToForm(res)
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        console.error('Error fetching package detail:', error)
      }
    },
    setDetailDataToForm(res) {
      // 处理多语言数据，将 packageInternationals 数组转换为 langData 对象结构
      res.data.renewType = res.data.renewType ? res.data.renewType : 0
      // 初始化所有支持的语言
      const langData = {}
      this.languageOptions.forEach(lang => {
        langData[lang.value] = { packName: '', packContent: '', nameId: undefined, contentId: undefined }
      })
      // 填充从 API 获取的多语言数据
      if (res.data.packageInternationals && res.data.packageInternationals.length > 0) {
        res.data.packageInternationals.forEach(item => {
          // 使用新的API结构，通过businessType和lang来区分
          const lang = item.lang || 'en' // 默认英文
          if (langData[lang]) {
            if (item.businessType === 1) {
              // 套餐名称
              langData[lang].packName = item.content || ''
              langData[lang].nameId = item.id // 保存名称ID
            } else if (item.businessType === 2) {
              // 套餐内容
              langData[lang].packContent = item.content || ''
              langData[lang].contentId = item.id // 保存内容ID
            }
          }
        })
      }
      // 确保默认语言内容填充到 langData，API返回的顶层默认是默认语言内容
      if (langData[this.defaultLang].packName === '' && res.data.packName) {
        langData[this.defaultLang].packName = res.data.packName
      }
      if (langData[this.defaultLang].packContent === '' && res.data.packContent) {
        langData[this.defaultLang].packContent = res.data.packContent
      }

      const copyPackageName = res.data.packName
      this.editForm = {
        ...res.data,
        langData, // 使用转换后的 langData
        copyPackageName
      }
      // 删除 API 返回中可能不需要直接绑定的字段
      delete this.editForm.packageInternationals
      delete this.editForm.createTime
      delete this.editForm.updateTime

      this.incrementData = res.data.functionRules || [] // 从 functionRules 获取增值功能数据
      // 需要确保 incrementData 中的每个对象包含 ruleId (如果存在) 和 packId (如果存在)

      if ([9, 12].includes(this.editForm.packType)) {
        //手机定位类-9，好友增加类-12，禁用输入指导价，指导价由成本价格*2得到
        this.guidingPriceDisabled = true
      } else if ([0, 2].includes(Number(this.type))) {
        // 新增或编辑时根据 packType 动态设置 guidingPriceDisabled
        this.guidingPriceDisabled = false
      }
    },
    //v2.9.0 没有看见方法调用，先注释
    // packTypeChange({ code, packClassify }) {
    //   if ([9, 12].includes(code)) {
    //     //手机定位类-9，好友增加类-12，禁用输入指导价，由成本价格*2得到
    //     this.guidingPriceDisabled = true
    //     // 如果packType变更为9或12，且已经有成本价，则计算指导价
    //     if (this.editForm.primePrice !== '') {
    //       this.editForm.guidingPrice = this.editForm.primePrice * 2
    //     } else {
    //       this.editForm.guidingPrice = '' // 如果没有成本价，清空指导价
    //     }
    //   } else if ([0, 2].includes(Number(this.type))) {
    //     this.guidingPriceDisabled = false
    //     // packType 变更后，如果不再是9或12，且不是详情模式，则保留指导价
    //   }
    //   this.editForm.packClassify = packClassify
    //   // 零时处理方案
    //   packClassify && this.$refs.packageTypeFormItemRef.clearValidate()
    // },
    getSelectOperateUser(user) {
      this.searchParams.searchId = user.id
    },
    resetForm() {
      this.editForm = {
        serviceProviderId: this.$cookies.get('bisUserId'),
        langData: {
          en: { packName: '', packContent: '', nameId: undefined, contentId: undefined },
          cn: { packName: '', packContent: '', nameId: undefined, contentId: undefined },
          hk: { packName: '', packContent: '', nameId: undefined, contentId: undefined },
          es: { packName: '', packContent: '', nameId: undefined, contentId: undefined },
          pt: { packName: '', packContent: '', nameId: undefined, contentId: undefined },
          ru: { packName: '', packContent: '', nameId: undefined, contentId: undefined },
          de: { packName: '', packContent: '', nameId: undefined, contentId: undefined },
          fr: { packName: '', packContent: '', nameId: undefined, contentId: undefined }
        },
        packType: undefined,
        wirelessType: 2,
        universal: 1, // 默认值
        standard: '',
        primePrice: '',
        guidingPrice: '',
        startTime: undefined,
        endTime: undefined,
        renewType: 0,
        isDailyPrice: 0,
        additionalDesc: '',

        packId: undefined // 新增 packId 字段用于编辑和 functionRules
      }
      if (this.type === 0) {
        this.$refs.CopyPackage && (this.$refs.CopyPackage.packName = '')
      }
      this.$refs.form && this.$refs.form.resetFields()
      this.incrementData = []
      this.currentLang = this.defaultLang // 使用计算属性重置
      this.guidingPriceDisabled = false // 重置指导价禁用状态
    },
    handleClose() {
      this.resetForm()
      this.$emit('update:visible', false)
    },
    handleOpen() {
      if ([0, 2].includes(Number(this.type))) {
        //新增，编辑
        this.disabled = false
        // this.guidingPriceDisabled = false // 在 getPackageDetailInfo 或 packTypeChange 中设置
      } else {
        //详情禁用
        this.disabled = true
        this.guidingPriceDisabled = true // 详情模式禁用指导价
      }
      if ([1, 2].includes(Number(this.type))) {
        //编辑和详情的disabled的字段
        this.editDisabled = true
      } else {
        this.editDisabled = false
      }
      if (this.type !== 0 && this.packId !== undefined) {
        // 确保有 packId 才去查询
        this.getPackageDetailInfo()
      } else if (this.type === 0) {
        // 新增模式，确保表单是重置状态
        this.resetForm()
      }
      // 在 handleOpen 结束时根据当前 packType 确定 guidingPriceDisabled 状态，尤其对于新增场景从resetForm后 packType 是 undefined
      if (this.editForm.packType !== undefined) {
        this.guidingPriceDisabled = [9, 12].includes(this.editForm.packType)
      } else {
        this.guidingPriceDisabled = false // 如果 packType 还没有值，则不禁用
      }
    },
    // 点击确定
    async handleConfirm() {
      if (this.type === 1) {
        this.handleClose()
        return
      }
      // 价格和时间校验...
      if (+this.editForm.primePrice > +this.editForm.guidingPrice) {
        this.$message.warning(this.$t('AsjA-juIl1c-vrm5bkDZe'))
        return
      }
      if (new Date(this.editForm.endTime) < new Date(this.editForm.startTime)) {
        this.$message.warning(this.$t('Kwn53CWjiBzlFcnJJJGx0'))
        return
      }

      this.$refs['form'].validate(async valid => {
        if (valid) {
          this.editForm.serviceProviderId = this.$cookies.get('bisUserId')
          // 构建符合API结构的参数
          let params = {
            ...this.editForm,
            startTime: this.editForm.startTime ? DateFromLocalToUTC(new Date(this.editForm.startTime)) : undefined, // 确保时间字段有值才转换
            endTime: this.editForm.endTime ? DateFromLocalToUTC(new Date(this.editForm.endTime)) : undefined, // 确保时间字段有值才转换
            // 将 langData 对象转换为新的 packageInternationals 数组格式
            packageInternationals: Object.keys(this.editForm.langData).flatMap(lang => {
              const result = []
              const langItem = this.editForm.langData[lang]
              // 新增时，只传有内容的条目
              if (this.type === 0) {
                if (langItem.packName) {
                  result.push({
                    businessType: 1,
                    content: langItem.packName,
                    lang: lang
                  })
                }
                if (langItem.packContent) {
                  const content = langItem.packContent?.replaceAll('pt;', 'px;') || ''
                  const formattedContent = content
                  result.push({
                    businessType: 2,
                    content: formattedContent,
                    lang: lang
                  })
                }
              } else {
                // 编辑时，有内容的条目，或无内容但有id的条目都要传递
                // 套餐名称
                if (langItem.packName || langItem.nameId) {
                  result.push({
                    businessType: 1,
                    content: langItem.packName || '',
                    lang: lang,
                    id: langItem.nameId
                  })
                }
                // 套餐内容
                if (langItem.packContent || langItem.contentId) {
                  const content = langItem.packContent?.replaceAll('pt;', 'px;') || ''
                  const formattedContent = content || ''
                  result.push({
                    businessType: 2,
                    content: formattedContent,
                    lang: lang,
                    id: langItem.contentId
                  })
                }
              }
              return result
            })
          }
          let functionRulesVal = this.$refs.AddIncrement.getRuleParams()
          if (functionRulesVal === false) return
          params.functionRules = functionRulesVal || []

          if (params.functionRules === false) {
            console.error('Invalid function rules data.') // 添加错误日志
            return
          }

          // 设置默认语言值到外层
          params.packName = this.editForm.langData[this.defaultLang].packName || ''
          const defaultContent = this.editForm.langData[this.defaultLang].packContent?.replaceAll('pt;', 'px;') || ''
          params.packContent = defaultContent
          //修复移动端webview 2025.05.30 移除这段代码，安卓端出现body,html,p{ margin: 0; }内容
          // params.packContent = `<style> body,html,p{ margin: 0; } </style>${defaultContent}`
          // 对于新增操作，移除 packId 字段
          if (this.type === 0) {
            delete params.packId // 新增时不发送 packId
          }

          // 清理不需要提交的字段
          delete params.copyPackageName
          delete params.langData // 已经转换到 packageInternationals

          // 移除时间戳字段，后端处理
          delete params.createTime
          delete params.updateTime
          // 移除 functionRules 内部的时间戳字段，后端处理
          if (params.functionRules && params.functionRules.length > 0) {
            params.functionRules = params.functionRules.map(rule => {
              delete rule.createTime
              delete rule.updateTime
              return rule
            })
          }

          let res
          try {
            if (this.type === 0) {
              res = await _addPackageSetting(params)
            } else if (this.type === 2) {
              res = await _editPackageSetting(params)
            }
            if (res.ret === 1) {
              this.$message.success(this.$t('lg.success'))
              this.handleClose()
              this.$emit('success')
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('提交套餐信息失败')
            throw error
          }
        } else {
          console.warn('Form validation failed.')
        }
      })
    },
    // 翻译函数
    te(arg) {
      const hasKey = this.$t(arg)
      if (hasKey) {
        const result = this.$t(arg)
        return result
      }
      return arg
    },
    async getPackTypeListByScene() {
      const result = await _getPackTypeListByScene({ sceneId: this.editForm.sceneId })
      if (result.ret === 1 && result.data) {
        this.packageTypeList = result.data
      } else {
        console.error('Failed to get package classify list:', result.msg)
      }
    },

    async getLevelList() {
      const result = await this.getOptionsByDictCode('RIGHTS_GRADE')
      if (result.length > 0) {
        this.levelOptions = result
      } else {
        this.levelOptions = []
      }
    },
    async getPackNatureOptions() {
      const result = await this.getOptionsByDictCode('PACK_NATURE')
      if (result.length > 0) {
        this.packNatureOptions = result
      } else {
        this.packNatureOptions = []
      }
    },
    async fetchSceneOptions() {
      try {
        const res = await _fetchSceneList({})
        const data = res.data.records || []
        this.sceneOptions = []
        data.forEach(item => {
          const sceneId = item.sceneId
          const sceneName = item.sceneInternationals.find(i => i.lang === this.defaultLang && i.businessType === 7)?.content || '--'
          this.sceneOptions.push({
            value: sceneId,
            label: sceneName
          })
        })
      } catch (error) {
        console.error('获取场景列表失败:', error)
        this.$message.error('获取场景列表失败，请刷新重试')
      }
    }
  },
  created() {
    this.currentLang = this.defaultLang
    if (!this.isAbroad()) {
      //国内环境当前显示的内容为中文，加一个中文的必填校验，国外环境只有英文是必填项
      this.rules = {
        ...this.rules,
        'langData.cn.packName': {
          required: true,
          message: this.$t('RB650X0GgnbB2n4QFIvyn')
        }
      }
    }
  },
  mounted() {
    this.getLevelList()
    this.getPackNatureOptions()
    this.fetchSceneOptions()
  }
}
</script>

<style lang="scss" scoped>
.edit {
  ::v-deep .el-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .el-form-item {
      width: 45%;
    }
    .form-desc {
      width: 900px;
    }
  }
  ::v-deep .base-dialog__body {
    overflow-x: hidden;
  }
}
.width-240 {
  width: 240px;
}

.lang-tabs {
  ::v-deep .el-tabs__header .el-tabs__nav-scroll .el-tabs__item {
    text-align: center;
    height: auto;
    padding: 5px 10px 5px 0px;
    &:nth-child(2) {
      margin-left: 0px !important;
    }
    &.is-active {
      font-weight: normal;
    }
  }
}

.form-container {
  display: flex;
  .right-form {
    flex: 1;
  }
  ::v-deep .el-tabs--left,
  .el-tabs--right {
    overflow: visible;
  }
}
</style>
