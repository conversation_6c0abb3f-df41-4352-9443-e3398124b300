<template>
  <div class="scene-manage">
    <search-form @search="handleSearch" @reset="handleReset" />
    <Table ref="sceneTable" @edit="handleEdit" />
    <EditSceneDialog v-if="dialogVisible" :visible.sync="dialogVisible" :editData="currentRecord" @success="handleSuccess" />
  </div>
</template>

<script>
import { _fetchSceneList } from '@/api/scene'
import { _getMachineType } from '@/api/packageType'
import Table from './components/Table.vue'
import EditSceneDialog from './components/EditSceneDialog.vue'
import SearchForm from './components/SearchForm.vue'
import _ from 'lodash'

export default {
  name: 'SceneManage',
  components: {
    Table,
    EditSceneDialog,
    SearchForm
  },
  data() {
    return {
      searchConditions: {
        sceneName: '',
        machineTypeId: '',
        enableStatus: ''
      },
      sceneOptions: [],
      deviceOptions: [],
      dialogVisible: false,
      currentRecord: {}
    }
  },
  async created() {
    await this.loadOptions()
  },
  methods: {
    /**
     * 加载下拉选项数据
     */
    async loadOptions() {
      try {
        // 加载场景选项
        const sceneRes = await _fetchSceneList({})
        this.sceneOptions = sceneRes.data || []

        // 加载设备型号选项
        const deviceRes = await _getMachineType({ type: 1 })
        this.deviceOptions = deviceRes.data || []
      } catch (error) {
        console.error('加载选项数据失败:', error)
        this.$message.error('加载选项数据失败')
      }
    },

    /**
     * 处理搜索按钮点击
     */
    handleSearch(searchConditions) {
      this.searchConditions = searchConditions || this.searchConditions
      // 直接调用表格的刷新方法，传入搜索条件
      this.$refs.sceneTable.refresh(this.searchConditions)
    },

    /**
     * 处理重置按钮点击
     */
    handleReset() {
      // 调用表格的重置方法，让各组件自己清空条件
      this.$refs.sceneTable.reset()
    },

    /**
     * 处理编辑操作
     * @param {Object} record - 要编辑的记录
     */
    handleEdit(record) {
      this.currentRecord = _.cloneDeep(record)
      this.dialogVisible = true
    },

    /**
     * 处理编辑成功
     */
    handleSuccess() {
      this.$refs.sceneTable.refresh()
    }
  }
}
</script>

<style lang="scss" scoped>
.scene-manage {
  padding: 0 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
