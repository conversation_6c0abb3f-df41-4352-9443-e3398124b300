{"language": "葡萄牙语（此字段不使用，备注）", "lg": {"sun": "Dom.", "mon": "Seg.", "tue": "<PERSON>r.", "wed": "Qua.", "thu": "Qui.", "fri": "Sex.", "sat": "Sab.", "loginTimeout": "Tempo de login expirou. Tente novamente", "_year": "A", "_month": "M", "_day": "D", "_hour": "H", "_minute": "M", "_second": "S", "_yes": "sim", "_no": "não", "error503": "Operação frequente, tente novamente mais tarde!", "carlogremark1": "Pontos de importação comuns recém-gerados para", "carlogremark2": "Pontos de entrada vitalícios recém-gerados para", "carlogremark3": "<PERSON><PERSON><PERSON> <PERSON> anual para", "carlogremark4": "Crie um cartão vitalício para", "carlogremark5": "Importar um ano de equipamento para", "carlogremark6": "Importar equipamento vitalício para", "carlogremark7": "Renove o cartão anual para", "carlogremark8": "Renove o cartão vitalício para", "carlogremark9": "Transferir pontos de entrada comuns para", "carlogremark10": "Transfira o ponto de indução vitalício para", "carlogremark11": "Transferir cartão anual para", "carlogremark12": "Transferir cartão vitalício para", "carlogremark13": "Ponto de entrada comum recém-gerado", "carlogremark14": "Ponto de entrada vitalício recém-gerado", "carlogremark15": "<PERSON><PERSON><PERSON> <PERSON>", "carlogremark16": "Crie um cartão vitalício", "carlogremark17": "Ponto de entrada comum de transferência", "carlogremark18": "Ponto de indução ao longo da vida de transferência", "carlogremark19": "Cartão de transferência anual", "carlogremark20": "Cartão de transferência vitalício", "inputContent": "Por favor, insira o conteúdo", "card": "Cartão", "customer": "Cliente", "importPoint": "Cartão de Importação", "renew": "<PERSON><PERSON>", "cardgenerate": "<PERSON><PERSON><PERSON>", "cardtrans": "Transferência de cartão", "cardback": "Pegue o cartão de volta", "superior": "Superior subordinado", "commonImportPoint": "Cartão de importação", "lifetimeImportPoint": "Cartão de importação para a vida", "annualcard": "Cartão anual", "lifelongcard": "Cartão vitalício", "cardtype": "Tipo de <PERSON>ão", "incomeexpend": "declaração de receitas e despesas", "inandouttype": "Tipo de receita / despesa", "balance": "<PERSON><PERSON>", "carbalance": "Saldo do cartão", "chosetarget": "Escolha o cliente-alvo", "generateIsZero": "O número de cartões gerados é 0!", "transIsZero": "O número de cartões transferidos é 0!", "recycleIsZero": "O número de cartões reciclados é 0!", "transoneyeartip": "O número de cartões de importação transferidos em um ano não pode ser maior que o saldo!", "translifelongtip": "O número de cartão de importação para a vida inteira não pode ser maior que o saldo!", "transannualcardtip": "O número de cartões anuais transferidos não pode ser maior que o saldo!", "translifetimecardtip": "O número de cartões vitalícios transferidos não pode ser maior que o saldo!", "recycleoneyeartip": "O número de cartões de importação reciclados em um ano não pode ser maior que o saldo!", "recyclelifelongtip": "O número de cartões de importação recuperadas não pode ser maior que o saldo!", "recycleannualcardtip": "O número de cartões anuais reciclados não deve exceder o saldo!", "recyclelifetimecardtip": "O número de cartões ao longo da vida não deve exceder o saldo!", "yearCard": "Cartão Anual", "lifetimeOfCard": "Cartão vitalício", "generatesuccess": "Gerado com sucesso por $", "transsuccess": "Transferido com sucesso para $", "recyclesuccess": "Reciclado com sucesso por $", "saleBatch": "Lote de vendas", "operateAccount": "A conta operacional", "targetAccount": "Conta alvo", "saleTime": "Tempo de venda", "transNo": "Lote de transferência", "operatebyself": "<PERSON>ão pode operar sobre si mesmo", "importRecord": "Registro de importação", "saleRecord": "Registro de vendas", "transRecord": "Registro de transferência", "importBatch": "Lote de importação", "operateUser": "O usuário operacional", "targetUser": "Cliente alvo", "batchNo": "Número do lote", "importTotal": "número total de importação", "checkdetails": "Verifique os de<PERSON>", "query": "Inquérito", "saleTotal": "número total de vendas", "selectStartTime": "Selecione um horário de início", "selectEndTime": "Selecione um horário de término", "totalDevice": "O número total de dispositivos", "originaluser": "Cliente original", "originalaccount": "Conta original do cliente", "enterRole": "Por favor, insira o nome da função", "inputuser": "Insira o nome de usuário", "details": "<PERSON><PERSON><PERSON>", "reqresult": "Solicitar resultado", "logDetail": "Detalhes de registro", "logNumber": "Número de registro", "uName": "Nome do usuário", "uType": "Tipo de usuário", "reqmethod": "Método de solicitação", "reqparam": "Parâmetro de solicitação", "month": "<PERSON><PERSON><PERSON>", "oneyear": "<PERSON><PERSON>", "lifetime": "<PERSON><PERSON><PERSON><PERSON>", "income": "Renda", "pay": "Despesas", "imei": "IMEI", "machineType": "<PERSON><PERSON>", "machineName": "Alvo", "imeiOrClientOrAccount": "IMEI/nome/conta", "imeiOrUserEmpty": "IMEI ou usuário vazio!", "device": "Dispositivo", "user": "Do utilizador", "delAccount": "Deletar conta", "resetPswFailure": "Falha na senha de descanso", "notification": "Notificação", "isResetPsw_a": "Tem certeza de redefinir a senha {0}?", "resetPsw": "<PERSON><PERSON><PERSON><PERSON>", "virtualAccountTipsText": "Ao criar uma conta virtual, é a conta de alias da conta do revendedor atualmente logado. Você pode definir permissões para a conta virtual. Para criar uma conta virtual para um usuário final, você pode primeiro alterar o tipo de usuário final para um revendedor, depois fazer login com esse revendedor, criar uma conta virtual e, em seguida, alterar o tipo de cesso para o usuário final.", "password": "<PERSON><PERSON>", "oldPsw": "<PERSON><PERSON> antiga", "newPsw": "Nova senha", "confirmPsw": "Confirme a senha", "pswNoSame": "Senha não é a mesma", "pswUpdateSuccess": "Sucesso de atualização de senha!", "oldPwdWarn": "Por favor insira uma senha antiga", "newPwdWarn": "Por favor insira uma nova senha", "pwdConfirmWarn": "Por favor, confirme a nova senha", "pswCheckTip": "A sugestão é uma combinação de 6 a 20 dígitos, letras e símbolos", "pwdCheckTips1": "Sugestões são 6-20 letras, números ou símbolos", "pwdCheckTips2": "A senha digitada tem intensidade Muito fraca", "pwdCheckTips3": "Sua senha poderia ser mais segura.", "pwdCheckTips4": "A SUA senha é Segura", "pwdLevel1": "Fraco", "pwdLevel2": "Médio", "pwdLevel3": "Forte", "clientName": "Nome do cliente", "loginAccount": "Conta", "status": "estado", "showAll": "Mostrar tudo", "startUsing": "Habilitar", "stopUsing": "Desabilitar", "search": "Procurar", "reset": "<PERSON><PERSON><PERSON><PERSON>", "newAdd": "Novo", "viewLimitConf": "Ver configurações de permissão", "viewLimit": "<PERSON>er permis<PERSON>", "newSysAcc": "Nova conta do sistema", "editSysAcc": "Editar conta de permissão", "virtualAcc": "Conta virtual", "oriVirtualAcc": "Conta virtual original", "virtualTip": "O módulo de conta virtual foi atualizado para um módulo de conta do sistema, crie uma nova conta do sistema", "view": "Visão", "delete": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "remark": "Observação", "createTime": "Tempo de criação", "modifyTime": "Tempo de modificação", "confirm": "Confirme", "serial": "NO.", "title": "<PERSON><PERSON><PERSON><PERSON>", "upload": "<PERSON><PERSON>", "all": "Todos", "unread": "Não lida", "readed": "Lido", "type": "Tipos de", "feedback": "Observaçãos", "bulletin": "<PERSON><PERSON><PERSON>orm<PERSON>", "about": "Sobre ", "reply": "Resposta", "date": "Data / hora", "setRead": "Marque lido", "setAllRead": "<PERSON>do lido", "messageCenter": "Centro de Mensagens", "backpage": "Voltar ao anterior", "from": "De", "gpsnow": "WhatsGPS", "pleaseChoose": "por favor escolha", "success": "Sucesso", "fail": "<PERSON><PERSON><PERSON>", "transfer": "Transferir", "newGeneration": "Novo", "consume": "<PERSON><PERSON><PERSON>", "give": "Dar", "limitconfig": "Perfil de Direitos", "role": "Funções", "rolename": "Nome da Função", "addRole": "Nova  Função", "editRole": "<PERSON><PERSON>", "deleteRole": "Excluir  Função", "delRoleTip": "Tem certeza de que deseja excluir esta função?", "delAccountTip": "Tem certeza de que deseja excluir esta conta?", "newAccountTip1": "A conta de autoridade é semelhante à conta virtual antiga e é a subconta do administrador. Os administradores podem criar contas com privilégios e atribuir funções diferentes a contas com privilégios para que contas diferentes possam ver diferentes conteúdos e operações na plataforma.", "newAccountTip2": "Processo de criação de uma conta de permissão:", "newAccountTip31": "1. Na página de gerenciamento de funções,", "newAccountTip32": "Criar nova  Função", "newAccountTip33": ", E configurar permissões para a função;", "newAccountTip4": "2. Na página de gerenciamento de conta de autoridade, crie uma nova conta de autoridade e atribua funções à conta.", "newRoleTip1": "Os administradores podem criar funções e configurar diferentes permissões de operação para diferentes funções para atender às necessidades de negócios em diferentes cenários.", "newRoleTip2": "Por exemplo, configure se uma função financeira tem permissão para localizar e monitorar, se tem permissão para adicionar clientes, se tem permissão para modificar informações do dispositivo, etc.", "runtime": "Tempo de execução", "locTime": "Tempo GPS", "speedNum": "Rapidez(km/h)", "barCode": "Código de <PERSON>", "belongCustom": "Pertencer ao cliente", "sweepCodeTime": "Tempo de leitura do código de barras", "startLoc": "Localização inicial", "endLoc": "Localização final", "totalMileage": "Total Mileage", "totalOverSpeed": "Excesso de velocidade total (vezes)", "totalStop": "Total de paradas (vezes)", "totalOil": "óleo total", "mileageNum": "Quilometragem(Km)", "stopTimes": "Estacionamento(vezes)", "offlineTime": "Alcance", "averageSpeed": "Velocidade média", "averageOil": "Consumo médio de combustível", "fuelTimes": "Abastecimento (vezes)", "fuelTotal": "Combustível Total", "fuelDate": "Data Combustível", "refuelingTime": "Tempo de reabastecimento", "choseDate": "Selecione a data", "noData": "Sem dados", "machineCount": "Quantidade do dispositivo", "openAccQuery": "Consulta de ACC", "temperature1": "Temperatura1", "temperature2": "Temperatura2", "temperature3": "Temperatura3", "run": "Viagem", "selected": "Selecionado", "sets": "conjuntos", "directionarray": {"0": "Ao North", "1": "Nordeste", "2": "<PERSON><PERSON>", "3": "Sudeste", "4": "Ao Sul", "5": "Sudoeste", "6": "Ao Oeste", "7": "Noroeste"}, "pointedarray": {"0": "Indefinido", "1": "GPS", "2": "LAC", "3": "Localização LAC", "4": "Localização WIFI", "5": "Posicionamento diferencial"}, "pointType": {"0": "Tipo de ponto", "1": "Localização de satélite", "2": "Localização da bússola", "3": "Localização LBS", "4": "Localização WIFI", "5": "Posicionamento diferencial"}, "alarmType": {"0": "Tipo de Alarme", "1": "Alarme de vibração", "2": "Desligue o alarme", "3": "Alarme de bateria fraca", "4": "Alarme SOS", "5": "Alarme de sobrevelocidade", "6": "Alarme de saída da restrição Geo-cerca", "7": "Alarme de deslocamento", "8": "Alarme de bateria fraca", "9": "Alarme fora da área", "10": "Desmontar Alarme", "11": "Desmontar Alarme", "12": "Alarme de detecção magnética", "13": "Desmontar Alarme", "14": "Al<PERSON>e <PERSON>", "15": "Alarme de proteção de sinal", "16": "Falso Alarme de Estação Base", "17": "Alarme de entrada da restrição Geo-cerca", "18": "Alarme de entrada da restrição Geo-cerca", "19": "Alarme de saída da restrição Geo-cerca", "20": "Alarme de porta aberta", "21": "Alerta de fadiga ao conduzir", "22": "Ponto de hipoteca de entrada", "23": "Saída do Ponto de Hipoteca", "24": "Estacionamento em pontos de hipoteca", "25": "Terminal Offline", "26": "Alarme de entrada da restrição Geo-cerca", "27": "Alarme de saída da restrição Geo-cerca", "28": "Alarme de entrada da restrição Geo-cerca", "29": "Alarme de saída da restrição Geo-cerca", "30": "Alarme de combustível", "31": "Alarme ACC ON", "32": "Alarme ACC OFF", "33": "Alarme de colisão", "34": "Alarme Atrasado para o Trabalho", "35": "Alarme para sair do trabalho mais cedo", "36": "<PERSON><PERSON><PERSON><PERSON>", "37": "Alarme de inclinação", "40": "Alarme Alta Temperatura", "45": "Alarme baixa temperatura", "50": "Alarme de sobretensão", "55": "alerta de baixa <PERSON>", "60": "Alarme de estacionamento", "70": "Alarme de aceleração rápida", "71": "Alarme de desaceleração rápida", "72": "Alarme de curva fechada", "73": "Alarme fora da rota", "74": "Alarme de excesso de velocidade da linha", "75": "Alarme de horas extras de estacionamento", "76": "Alarme de voz", "77": "Alarme de retirada do terminal", "78": "Alarme de inserção de terminal", "79": "Alarme de reboque", "80": "atender o telefone", "81": "Alarme de fumo", "82": "condução distraída", "83": "condutor anormal", "84": "alerta de colisão frontal", "85": "<PERSON><PERSON> de faixa", "86": "muito perto para a distância do carro", "87": "perda de sinal de vídeo", "88": "oclusão de quadro de vídeo", "89": "falha na unidade de armazenamento", "90": "Alerta de fadiga ao conduzir", "91": "Alerta de Colisão frontal", "92": "AC em alarme", "93": "Alarme AC off", "94": "Alarme de porta do carro aberta", "95": "Alarme de fechamento da porta do carro", "96": "Alerta de não uso de cinto de segurança", "97": "Alarme de marcha lenta", "98": "Desconexão por Bluetooth", "99": "Assistência urgente", "101": "Aviso de condução restrita", "102": "Alarme de vedação normal", "107": "Alarme de aproximação traseira", "108": "Alarme de proximidade traseiro esquerdo", "109": "Alarme de proximidade traseiro direito", "110": "Aviso de bateria P1", "111": "Alarme anti-interferência", "114": "Alarme de velocidade excessiva da cerca", "116": "Alerta de alta temperatura da subetiqueta", "117": "Alerta de subetiqueta de baixa temperatura", "118": "Alerta de sobreposição", "119": "Aviso de entrada", "120": "Aviso de partida", "121": "Alerta de combustível insuficiente", "122": "Alerta de colisão ligeira", "123": "Alerta de colisão média", "124": "Alerta de colisão grave", "125": "Alarme do roubo do tanque secundário"}, "bsLogType": {"车辆管理": "Gestão de frota", "打卡管理": "Gestão de Presença", "用户管理": "Gestão de clientes", "菜单管理": "Gestão do menu", "角色管理": "Gestão de funções", "设备管理": "Dispositivos", "录音管理": "Gestão de gravação", "围栏管理": "Gerenciamento de cercas", "轨迹管理": "Reprodução de histórico", "告警管理": "Gestão de alertas", "用户卡券": "Cartão", "远程控制": "controle remoto", "指令管理": "Gestão de comandos", "电子围栏": "Geofence", "消息提醒": "Notas", "虚拟用户": "Usuário <PERSON>", "公告管理": "Gestão de anúncios", "热点管理": "<PERSON><PERSON><PERSON> frequentes", "角色模块": "Módulo de função", "登录": "Registro de login"}, "cardType": {"0": "Tipo", "1": "Novo cartão", "2": "'Novo cartão Vitalício", "3": "Novo cartão", "4": "Cartão vitalício"}, "userType": {"0": "Administrador", "1": "Distribuidor", "2": "Usuário final", "5": "<PERSON><PERSON><PERSON><PERSON>"}, "cardState": {"0": "Normal", "1": "Número vazio", "2": "<PERSON><PERSON><PERSON> de<PERSON>", "3": "Tempo de inatividade excessivo", "4": "Pacote não aberto", "5": "<PERSON><PERSON><PERSON>o pela m<PERSON>a", "6": "GPRS a ser aberto"}, "errorCode": {"error90010": "Il dispositivo non è online e non è stato possibile inviare il comando personalizzato!", "error70003": "Il valore del controllo remoto non può essere vuoto", "error70006": "Non supporta o non ha l'autorità per emettere questo comando", "error20001": "L'ID del veicolo non può essere vuoto"}, "logDict": {"other": "de <PERSON>ros", "insert": "inserir", "update": "atual<PERSON>r", "save": "salvar", "delete": "excluir", "grant": "conceder", "export": "exportar", "import": "importar", "select": "selecionar", "trans": "trans", "sale": "venda", "renew": "renovar", "control": "ao controle", "login": "Conecte-se"}, "limits": {"appdownload": "Here", "ACC_statistics": "ACC estatística", "Account_Home": "Visão geral", "Add": "Novo", "Add_POI": "Adicionar POI", "Add_customer": "<PERSON><PERSON><PERSON><PERSON>", "Add_device_group": "Adicionar grupo de dispositivos", "Add_fence": "Adicionar cerca", "Add_sharing_track": "Adicionar tril<PERSON> de compartil<PERSON>nto", "Add_system_account": "Nova permissão de conta", "Alarm_details": "Detalhes do alarme", "Alarm_message": "Mensagem de alarme", "Alarm_overview": "Visão geral do alarme", "Alarm_statistics": "Relatório de alarme", "All_news": "Todos", "activity_board": "próximos eventos", "Associated_equipment": "Associar dispositivo", "Available_points": "Cartão disponível", "Barcode_statistics": "Estatísticas do código de barras", "Batch_Import": "Importação em lote", "Batch_renewal": "Lote renovar", "Batch_reset": "Redefinição em massa e volume", "Bulk_sales": "Vendas a granel", "Call_the_police": "Alarme", "Customer_details": "Detalhes do cliente", "Customer_transfer": "Mover <PERSON><PERSON><PERSON><PERSON>", "Delete_POI": "Apagar POI", "Delete_account": "Deletar conta", "Delete_customer": "Deletar usuá<PERSON>", "Delete_device": "Apagar dispositivo", "Delete_device_group": "Excluir grupo de dispositivos", "Delete_fence": "Excluir cerca", "Delete_role": "Excluir func<PERSON>", "Device_List": "Lista de dispositivos", "Device_grouping": "Grupo de dispositivos", "Device_transfer": "Transferência de dispositivo", "Due_reminder": "Recordação Da Expiração", "Edit_details": "<PERSON><PERSON>", "Equipment_management": "Dispositivo", "My_clinet": "Dispositivo", "Fence": "GeoFence", "Fence_management": "Gerenciamento de cercas geográficas", "Generate": "<PERSON><PERSON><PERSON>", "Generate_lead-in_points": "Criar novo cartão", "Generate_renewal_points": "Criar cartão de renovação", "Have_read": "Marque lido", "Idle_speed_statistics": "Estatísticas de velocidade ociosa", "Import": "Importar", "Import_Device": "Adicionar <PERSON>ositi<PERSON>", "Industry_Statistics": "Estatística Industrial", "Location_monitoring": "Monitor", "Log_management": "Registro", "Mark_read": "Marque lido", "Menu_management": "<PERSON><PERSON>", "config_management": "Gerenciamento de configuração", "custom_model": "Personalize o modelo", "Message_Center": "Centro de notificações", "Mileage_statistics": "Relatório de Quilometragem", "Modify_POI": "Modificar POI", "Modify_device_details": "Modificar detalhes do dispositivo", "Modify_device_group": "Modificar grupo de dispositivos", "Modify_role": "Modificar função", "Modify_sharing_track": "Modificar faixa de compartilhamento", "Modify_user_expiration": "A modificação em lote dos usuários expira", "More": "<PERSON><PERSON>", "My_business": "O negócio", "My_client": "Meu cliente", "New_role": "Criar nova  Função", "New_users": "<PERSON><PERSON><PERSON><PERSON>", "Oil_statistics": "Estatísticas de consumo de combustível", "POI_management": "Gestão POI", "Points_record": "Histórico do cartão", "Push": "Notificação", "Quick_sale": "<PERSON><PERSON><PERSON>", "Renew": "<PERSON><PERSON>", "Replay": "Reprodução", "Role_management": "Função", "Run_overview": "Operações estatísticas", "Running_statistics": "Operações estatísticas", "Sales_equipment": "Vender dispositivo", "Set_expiration_reminder": "Gestão de funções", "Share_track": "Compartilhar faixa", "regional_statistic": "Estatísticas Regionais", "Sharing_management": "Compartir Administrar", "User_management": "Gerenciamento de usuários", "Speeding_detailed_list": "Lista de velocidade", "Statistical_report": "Relat<PERSON><PERSON>", "Stay_detailed_list": "Lista de estacionamento", "System_account_management": "Conta de autoridade", "Temperature_statistics": "Estatística de Temperatura", "Transfer": "Mover <PERSON><PERSON><PERSON><PERSON>", "Transfer_group": "Grupo de transferência", "Transfer_point": "Transferir novo cartão", "Transfer_renewal_point": "Transferir cartão de renovação", "Trip_statistics": "Relatório de viagem", "Status_statistics": "Statistiche di stato", "Static_statistics": "Statistiche statiche", "Driver_alarm": "Alarme de comportamento do motorista", "Driver_analysis": "<PERSON><PERSON><PERSON><PERSON> driver", "Unlink": "Desvin<PERSON>", "View": "Visão", "View_POI": "Ver POI", "View_device_group": "Ver grupo de dispositivos", "View_fence": "Verifique a cerca", "View_role": "Ver função", "View_sharing_track": "Ver trilha de compartilhamento", "Virtual_account": "Conta virtual", "Voltage_analysis": "<PERSON><PERSON><PERSON>", "Voltage_statistics": "Estatística de Tensão", "batch_deletion": "Apagar lote", "change_Password": "Trocar Senha", "delete": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "instruction": "Comand<PERSON>", "login": "Conecte-se", "modify": "<PERSON><PERSON><PERSON><PERSON>", "monitor": "Monitor", "my_account": "Minha conta", "reset_Password": "<PERSON><PERSON><PERSON><PERSON>", "share_it": "Compartilhar localizacão", "sub_user": "Sub conta", "sub_customers": "Clientes mais baixos", "track": "Rastreamento", "Custom_Order": "Comando personalizado", "GeoKey": "Geo-Chave", "GeoKey_Update": "modificar", "GeoKey_Delete": "excluir", "GeoKey_Add": "adicionar <PERSON>", "GeoKey_View": "Visão", "feedback_manager": "Observaçãos", "feedback_list": "Visão", "feedback_handle": "Processando feedback", "proclamat_manager": "<PERSON><PERSON><PERSON>", "proclamat_manager_list": "<PERSON><PERSON>", "proclamat_manager_update": "Modificação do anúncio", "proclamat_manager_delete": "<PERSON>cluir <PERSON>", "proclamat_manager_save": "Novo anúncio", "device_update_batch_model": "Modelo de dispositivo de modificação em lote", "login_manager": "Gerenciamento de login", "asset_manager": "Ativo", "package_manager": "Gerenciador de <PERSON>otes", "order_manager": "Gerenciador de pedidos", "financial_management": "Gestão financeira", "finance_center": "Centro Financeiro", "device_record": "Registro", "auth_manager": "Autoridade", "system_manager": "Sistema", "point_generate": "<PERSON><PERSON><PERSON>", "point_recovery": "Pegue o cartão de volta", "point_transfer": "Transferência de cartão", "device_transfer_record": "Registro de transferência", "device_sale_record": "Registro de vendas", "device_import_record": "Registro de importação", "user_point_list": "Cartão", "Info_Instruction": "Instruções", "Info_Question": "Perguntas freqüentes", "Info_AllCenter": "Centro de Notificação", "user_point_log_list": "Verifique os de<PERSON>", "car_maintain_list": "Manutenção", "REPLACE": "Substituir IMEI", "punch_record_list": "Gestão de Presença", "Station_message": "Notícias da Estação.", "Update_bulletin": "<PERSON><PERSON><PERSON><PERSON>", "Personal_Center": "Centro pessoal", "task_list": "Exportação de dados.", "Write_Account": "Preencha a conta", "Find_Method": "Recupera<PERSON> m<PERSON>", "Contact_Provider": "<PERSON><PERSON>or de contato", "device_report_offline": "Estatísticas offline", "Select_Equipment": "Selecione o equipamento", "Offline_statistics": "Estatísticas offline", "Driving_Behavior": "Comportamento da condução", "drive_analysis": "Comportamento da condução", "Fence_statistics": "Estatísticas de cerca", "line_manage": "Gerenciamento de linha", "Line_statistics": "Estatísticas de linha", "machine_type_manage": "Gerenciamento de dispositivos", "carfince_first": "Finanças de automóveis residenciais", "carfince_risk_control": "Centro de controle de risco", "carfince_monitor": "Plataforma de Monitoramento", "carfince_two_bet_manager": "Gestão do Segundo Depósito", "carfince_tow_bet_point_setting": "Segunda definição de ponto de aposta", "carfince_tow_bet_alarm": "Segunda chamada para a Polícia", "carfince_permanent_manager": "Gerenciamento de Residentes", "carfince_permanent_verify": "Auditoria de Gestão de Residentes", "carfince_permanent_statistics": "Estatísticas de Residentes", "carfince_permanent_alarm": "Alarme <PERSON>e", "carfince_abnormal_aggregation": "Agregação anormal", "carfince_parking_timeout": "Tempo limite de estacionamento", "carfince_fence_alarm": "Alarme de cerca", "carfince_my_client": "Meu Negócio Auto Financiamento", "carfince_customer_management": "Gestão de Clientes", "topic_list": "Gerenciamento de Problemas", "topic_save": "Adicionar uma pergunta", "topic_update": "Modifique a pergunta", "topic_delete": "Excluir pergunta", "Find_Password": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>a", "Video_Monitor": "Vídeo", "Time_Video": "Vídeo ao vivo", "Playback_Video": "Reprodução de vídeo", "Video_Evidence": "Provas em vídeo", "Video_Task": "Tarefas de vídeo", "EventTask": "Tarefas de Eventos", "bigFullScreen": "<PERSON><PERSON>", "Capture_Center": "Centro de captura", "Event_Center": "centro de eventos", "package_list": "Lista de Pacotes", "package_proxy": "Agente de pacote", "package_setting": "Configurações do pacote", "charge_stat": "Estatísticas de carga", "person_punch": "Soco de pessoal", "driver_punch": "Punção do driver", "report_task": "Reportar tarefa", "package_edit": "Modificação do pacote", "device_import": "Importação de dispositivo", "device_reset": "Reinicialização do dispositivo", "income_detail": "declaração de receitas e despesas", "withrawal_detail": "<PERSON><PERSON><PERSON> da retirada", "bill_detail": "Detalhes da conta contábil", "order_manage": "Gerenciador de pedidos", "service_order": "Pedido do provedor de serviços", "gift_record": "Registro de presente", "import_record": "Registro de importação", "provider_manage": "Gerenciamento do provedor de serviços", "amazon_order": "Número de rastreamento da Amazon", "withrawal_verify": "Revisão de pagamento", "device_info": "Informazioni sul dispositivo", "app_update": "Gestão de apps", "car_manager_upgrade": "Upgrade em lote", "car_CarUpgrade_upgrade": "Registro de atualização", "Channel_Manage": "Gestão de canais", "operation_manager": "Gestão de operações", "ad_manager": "Gestão de publicidade"}, "cancel": "<PERSON><PERSON><PERSON>", "cancelSpace": "<PERSON><PERSON><PERSON>", "submit": "Enviar", "submitSpace": "Enviar", "resetSpace": "<PERSON><PERSON><PERSON><PERSON>", "uploadType": "Faça upload de arquivos do tipo .jpg .png .jpeg .gif", "uploadSize": "O arquivo de upload não pode ser maior que 3M", "uploadAvatar": "Carregar avatar não pode ser maior que 1M", "submitfail": "O envio falhou", "fbUploadTip": "Selecione o tipo de feedback", "alerttitle": "O título não pode ficar em branco!", "alertcontent": "O feedback não pode estar vazio!", "submitsuccess": "Presentado com sucesso! Processaremos seus Observaçãos o mais rápido possível ~", "contact": "Informações de Contato", "contact_p": "Preencha seu telefone ou e-mail", "uploadImg": "Enviar Imagem", "fbcontent_p": "Descreva resumidamente as perguntas e Observaçãos que deseja enviar, usaremos para melhorar nosso serviço", "fbcontent": "Dúvidas e opiniões", "fbType": "Tipo de <PERSON>", "fbType1": "A<PERSON>selhame<PERSON>", "fbType2": "Funcionamento defeituoso", "fbType3": "Experiência de usuário", "fbType4": "Novas sugestões de recursos", "fbType5": "Outros", "fbtitle": "Questão e título de opinião", "serviceProvide": "Fornecedor de serviço", "linkMan": "Contato", "linkPhone": "telefone", "email": "O email", "address": "Endereço", "commonProblem": "<PERSON><PERSON><PERSON> frequentes", "instructions": "Instruções", "feeds": "Observaçãos", "fotbidPassword": "<PERSON><PERSON>", "exitStytem": "<PERSON><PERSON><PERSON>", "account": "Conta", "operaTime": "Tempo operacional", "ipaddr": "endereço de IP", "businessType": "Tipo de Negócio", "operateType": "Tipo de operação", "operator": "Operar", "time": "Tempo", "primaryKind": {"1": "Alarme", "2": "Lembretes personalizados", "3": "Lembrete de manutenção", "4": "Aviso de renovação", "5": "Lembrete de expiração", "6": "Observaçãos", "7": "<PERSON><PERSON><PERSON>"}, "Monday": "Segunda-feira", "Tuesday": "Terça-feira", "Wednesday": "Quarta-feira", "Thursday": "Quin<PERSON>-f<PERSON>", "Friday": "Sexta-feira", "alarmType[119]": "Entrar na notificação"}, "remind": "<PERSON><PERSON><PERSON>", "importRemind": "O equipamento está sendo inicializado. Aguarde dez minutos antes de operar este lote de equipamento para evitar anormalidades no equipamento.", "importRemindFile": "Lista de equipamentos com falha para doação", "idleSpeedInputError": "Erro de entrada do valor do alarme inativo", "batchImportTemplate": "Modelo de importação em massa para gerenciamento de motoristas", "batchImportTip": "Você pode importar as informações do motorista em massa carregando um formulário. O arquivo do formulário deve ser inserido de acordo com o formato do cabeçalho do arquivo do modelo. Ao mesmo tempo, certifique-se de que o número do cartão do motorista esteja correto e tenha sido definido para o lado do dispositivo através da instrução;", "batchAlarmSettings": "Configuração do alarme em lote", "pay": "pagar", "batchInstructions": "<PERSON><PERSON> do Comando em Lote", "generalFunctions": "Funções comuns", "offlineJudgment": "Julgamento offline", "offlineJudgmentTipOne": "Suporta a configuração do tempo de determinação offline do dispositivo. Depois que o dispositivo é desconectado da plataforma por um tempo predefinido, a plataforma determina que o dispositivo está offline.", "offlineJudgmentTipTwo": "Se não houver configuração, por padrão o dispositivo com fio será considerado offline se nenhum pacote de posicionamento de pulsação for carregado em 10 minutos; o dispositivo sem fio será considerado offline se nenhum pacote de posicionamento de pulsação for carregado em 30 minutos .", "expandFunctionality": "Expandir funções", "expandFunctionalityTip": "Antes de configurar, confirme se o dispositivo suporta este tipo de função estendida.", "resultErrorTip": "{0} dispositivos foram configurados com sucesso, mas {1} dispositivos não foram configurados pelos seguintes motivos.", "scheduledTime": "Tempo de planejamento", "plannedMileage": "<PERSON><PERSON><PERSON>", "maintenanceStatus": "Status de manutenção", "comprehensiveMaintenance": "Manutenção abrangente", "mileageMaintenance": "Manutenção de quilometragem", "dateMaintenance": "Manutenção de data", "warned": "<PERSON><PERSON> a<PERSON>", "endUser": "Usuário final", "maintenanceType": "Tipo de manutenção", "alertDate": "Data do aviso", "scheduledDate": "Data planejada", "offlineInputError": "Julgamento offline de erro de entrada de valor de alarme", "oilOutage": "Falta de petróleo", "serviceNumberOrder": "Ordem do Número de Serviço", "purchaseQuantity": "Quantidade de compra", "iWYURze5gw6IuatRIrbE9": "Falha ao carregar", "u8042nAxHpYgsu7a58sQ6": "Transferir", "O3NI5gV9UN7-mT9gi-KHC": "Crio", "elr13plxYiqSiRBQuS4As": "Retornar", "_c1C4zcPDX-ZWed2eRbdA": "Vender", "TlgmpSebIew_qw82Z7273": "IMEI", "pp7ggW3s8XBX7RhFkk1_v": "Prazo do usuário", "12YEhb_OD-ZHwKO7vWITk": "Cliente", "7fJLSw_PZ8o4rUM051emO": "<PERSON><PERSON>", "xe_DoA8ge_gmef88_vh4p": "Plataforma Vencida", "fEKIzjDvXOhTv3JkjXuKc": "Adicionar dispositivo", "NEfgPcoBVZIP3f585kbxV": "Tipo de renovação", "DrMjlHHpaxp_yFcEvOcQi": "Dicas de operação: após a redefinição, os dados de teste, como tempo de ativação do dispositivo e trajetória, serão apagados e o status do dispositivo online ou offline será redefinido para inativo.", "DsMzz4HiiZh_zjcZCg7sv": "Quantidade", "grwMQULKZ0LrPztl-Z7wr": "Total", "6VQ3tYGZgExw1rZnn8Cbj": "Estoque", "PfpLQ5G3zI6xZpZlialvg": "Mundo", "zj98NBvqfCTJqrwUaGhji": "Implementação de dispositivos", "Z8MQX4KbfoflrmZEy5Vj6": "Incluir subordinados", "JH4HcH8lq4j75he7Raack": "Análise do dispositivo", "fCdSYzcuCLxP0iO32z_7j": "subconta", "pKGbb1Y6o7498BtbB9dWf": "Análise do dispositivo", "d4TPm1qg_yKyoFhGhc49y": "Ativação total", "zMqE-woZfLlDun2A-ZmhU": "nova ativação semanal", "ZiuUTHENQjPPZw4IEUVOK": "Anúncio de atualização de versão", "QoEq85osl9LN1AmO58BiM": "Gráfico de tendência do dispositivo online", "YI-CDEbvHMaK4Qo-P0C54": "<PERSON><PERSON> mês", "mngS5pANeQga3_X0KfIkm": "Este trimestre", "3QA6lHuTpmOtPdjicAlLd": "<PERSON><PERSON>", "OfQYeUUlRnfql7Er4dYXT": "Vendas top10", "0cT7QoohrYcbqyDW-BGwX": "7 dias", "TImMByZR7gX-T5SE4wW_C": "7 dias", "8XStVR83GO-UD-vuXceyN": "30 dias", "0gU5oOZxYWY5c8diw5GGt": "Ontem", "w6SqBL1DnKWu5v2QWD_Qd": "Adicionar em lote", "8XaFdQXQQQExEY6VjBIoV": "Atualmente {0}", "QHewpO-a1QEPaAwvF6Xzr": "<PERSON><PERSON><PERSON><PERSON>", "1NfCtPMIHa5qxyRCS84S1": "Por favor, digite o número IMEI, atualmente: {0}", "iAVxi7DIE0592dotpSI-W": "Digite o número IMEI", "Qd6gmq65WiHFshYKYFr4F": "Insira o número IMEI de 15 dígitos!", "h89Z-UD7uIkZ6egxwKDSM": "3 meses", "8yaI2Muj-dOcsIgudaSgP": "12 meses", "Sg3DiQowXqC8D35OICtD8": "Tem certeza de que deseja vender {0} dispositivos para {1}?", "DBBhTdEjXuithUnfBj4uF": "Digite o dispositivo", "MRqCFQeb7tjZ4vzCQR9Rn": "Razão", "dGiezzpH2ifwm00fwd6Ou": "Falha na renovação", "RnDDKP0_zNeMqMvt83zyQ": "O {0} atual é inferior a {1}, insuficiente para pagar! Exclua algum dispositivo ou adicione um cartão!", "jVw447QvAeRR4NrTNp5wI": "{0} renovação do dispositivo com sucesso", "HahpWiGqvHlj74U8VvXkS": "{0} venda do dispositivo bem-sucedida, {1} venda do dispositivo falhou. Os motivos específicos são os seguintes:", "SwtmYZpc1RnK8sKvik1ND": "Tem certeza de que deseja redefinir este {0} dispositivo?", "2gK1OvSK1N490nqe6BmML": "Reinicialização bem-sucedida", "mAyV3e2y7beLqKbttMEyP": "Tem certeza de que deseja importar {1} dispositivos do modelo {2} para {0}?", "ZrFQNTbU10MZPjYNkDZ0c": "{0} dados foram importados com sucesso, mas {1} falhou ao importar os dados. Os motivos específicos são os seguintes:", "ZvBsO_CTD_qH1mggQdOEz": "Ativo", "Fjjy4njX1Z-5GjVk2Ry8x": "Estatísticas do modelo do dispositivo", "V1DtS-jLSC38n1jMuvxaX": "Prazo de validade após renovação", "jhzwcMuCQSBt0s7QsRL6J": "Tem certeza de que deseja renovar {0} dispositivos?", "93mxIMhUPjOdxz1mAg5VT": "Tem certeza de que deseja renovar {0} dispositivos para {1}?", "8_eOeIb1VOROEQ4EsuJGG": "Renovados com sucesso {0} dispositivos {1}, o período de validade é o seguinte", "t__tQ1COBdJfeegyNEut4": "<PERSON>nte você mesmo", "V5ni5q4iTECCMrUfdW93x": "Nenhum número IMEI existe", "5WGqM6dwWgfZNerSr08_j": "{0} dispositivos foram importados com sucesso", "5P76CIxqHeL97BTdshJjp": "Entrada do número de série", "hrJVpqMEc8TKXFgGT79a_": "{0} dispositivos foram vendidos com sucesso", "xtan0lEAvk31wH5vywZIy": "Superior", "myzCJIAtsYrsBH7YLrbMC": "Adicionar subconta", "8EX2meYNZa_kdhW80e8L_": "Distribuidor", "zjj2zR3u_qvaZPmcIQmZc": "Venda com sucesso", "EGou8TaGJwBFQKJoHe1Gy": "IMEI existente", "PdDwaW3ZMpo7a1kMh1QTQ": "<PERSON><PERSON>", "iMVebDXUsA5c_fxl671kL": "Quantidade", "RFQZq8sjjZ5vjV5m6CAZH": "proporção", "M-1KNMPFUhwUz1A54afuJ": "A conta de login já existe", "vYM6cI35WYG617ajkDXbg": "Selecione um usuário superior", "8L8DMNF_TiVeobqbKjzNp": "Nome do cliente", "CVRtg6QBo3_I3SdSEnOFN": "<PERSON><PERSON><PERSON><PERSON>", "HIYRKGjbiHPM1vChmwTtS": "A conta de login não pode ter 15 dígitos!", "_Ir0v6itPxvLoVX8jyypM": "<PERSON><PERSON>", "gjf_Ayv-A3bIgDrLUyaLl": "Confirme a senha", "wqK1AUIRFGiB5eHofupGL": "Senha, não atipismo!", "fXrxuyIkLD3QEgjPvKuJa": "Selecione um cliente", "MTr2Ks8d0Ny8erQoUJ2kJ": "Selecione o modelo", "pleaseSelectPackVersion": "Por favor, selecione a versão do pacote", "60Cg-7z2oV7N30quX6B1c": "Selecione o tempo de expiração da plataforma", "lfSrcQrITYhRu4d4uMcDs": "Importar resultados", "uIEq4TWD9LFaCJEFLt84g": "Erro de formato de número", "qyDjiNshB1P2cTPWxQcIA": "Por favor, digite um número fixo", "n25N1-n4SySYruwj301zO": "Por favor, insira o valor inicial", "8K5x8ehQZuA1mdBtxsMeY": "Insira o valor final", "t1vi6s7lcTAtqd_7kwEOT": "Um IMEI para uma linha", "aFq5tmk1n281NDkCSp8X6": "Associação", "E9BGOCAvkhyQewmSWvUHr": "Renovar com sucesso por 1 ano", "w0vVvEoUupXMqiJV9fCF2": "Não há necessidade de renovar o cartão vitalício", "Z-IXWYRO5QljfIOj9i_sa": "Um ano", "O_ft1yy-v3_td6h9X6_CF": "Resultados de vendas", "ZFrlcab8jFZcpRNRcCnMA": "O tempo de expiração do usuário é maior que o tempo de expiração do numero de IMEI da plataforma", "HmTLpP09V2cIjAWRcUHzQ": "Repetir IMEI", "eY-kEiHnxN-wrVrq1fVX3": "4º andar, No.121, <PERSON><PERSON><PERSON><PERSON>, Science Road, Luogang District, Guangzhou", "dr1VBKYi7Au5f9nwUBwd_": "Alto", "yn6gOaagimB00Dh3sw0Xw": "Baixo", "09dzRv47cdB8ngpXdmrNA": "Á<PERSON>", "ZxAVznWm0Arp2GO-C5elp": "Porcentagem", "7fVMapQ6M_RkSzO5jDN18": "Classificação", "KQBFZQ0Voyfhg6fQXT_Rn": "Nome", "1JpPsfqrgrwbDs32GzHmo": "Vendas (conjunto)", "qmmSQeOd7o-k-CKy8cGQ1": "Resultado da renovação", "cilqXZDxJIkYAni2p6D18": "Bom dia", "ySCL835cERmc-2ui46kJg": "<PERSON>a tarde", "CP6KtfpD2nRp4YsehmLV4": "Boa noite", "XhZ_mUXUaEoscKh4O__52": "Quantidade de vendas", "B-oqbyA9EYX3_b604CHoI": "Detalhes do modelo do dispositivo", "i6izKntdBPjj1CVgTxoFv": "Quantidade do cliente", "h8y6BrYJM46qcer2hAm-e": "Clientes diretos", "LMGhuoa55f6P3zBo-I6_h": "O número de dispositivos online na semana passada", "yHUUUDenNX-ZDlHH-h8Bf": "Não é possível abranger meses", "c9Ois07PLUg8-LT8ypZpx": "Falha na importação", "qNXcCC7yboHklZOmNBtso": "A quantidade de dispositivos online nos últimos 30 dias", "oC3aVgjq_fLItbKC4dzC0": "Nenhum número IMEI executável", "6LW1YZbmhuEq6LyLyYwqR": "O número de pontos de importação é insuficiente", "_L3SCIlbztecHYB9zhGmZ": "O número IMEI é ilegal", "mXaZcmYFfm7x55jOUq3WJ": "O número IMEI já existe", "qmNtvRFOkHtbTvTeWLK0j": "IMEI que falhou ao importar o veículo", "c-aC4vVpgvcu0VyfYe2dU": "Informações do dispositivo", "GmeIr45NbbW42QwT7mmrf": "Venda à máquina", "Wylya-4JRSp679tGf_q4g": "Gerenciamento de cliente", "nd_7J9mswRt2IfBGNjIWa": "Configuração de cerca geográfica", "bo540fd46Pfi59A-x8C_R": "Gerenciamento de alarme", "51VD4AjMhSFl6EBZnYW3g": "Instrução de despacho", "bWqxJSaBirj0pJXbd4ubc": "Ver apenas o monitor", "uTfF4X61wgUVv37Uhu0aw": "Atualização bem-sucedida", "hiO2JmZ3oYl5uM8mflDcL": "Função modificável", "lD-unyqzZNFq1t9SB0eNy": "Intervalo de permissão", "1yPhethvo0Yf9j-z3fQaZ": "Conta", "igFoXwa2BzYkyYAJDqU9R": "Deseja redefinir a senha para {0}?", "XsvtYpl8vyei8PJ4zNqBV": "{0} a senha foi redefinida para 123456", "6czW74k8BArZqrVE40tKG": "Falha na senha de descanso", "KK1ozzOjBr5DpFPKLt1C6": "Deseja excluir {0}?", "HzV5269cO5xVNwIaK3De_": "Excluir conta virtual", "yXpq6xUfZ9hRjfzRQLSzh": "tipo de cartão", "EshFaJOQM_Oh3ICyukZP-": "IMEi inexistente", "mN6Jwf0wh5RTbxZiGQCoR": "Estatísticas de dispositivos", "sdeE2Xfr5yaOVDKBpwToH": "Crescimento TOP10", "7-o5VuFdLBdBRPbue62gr": "item", "flK64mYHgrj-k7-D2XupZ": "online", "dtFn22xfEx789uFKvaG_n": "offline", "dzaHeFci7OhejMnw1GSix": "inativado", "lkPusWocD2jptbcRGKhFR": "Adicionar conta <PERSON>", "Z9YgHkSg2uB840tCIrZyC": "pessoas", "7C1maetdK8mVxRyzBW2H6": "Usuários não diretos", "2Qfae-GXcMhGQxMT0Ds6A": "Vendido", "qs5MKLWLhFEFf1W5CGOCn": "Crescimento (conjunto)", "kt7tkMjYVOmcxeQ8WIe42": "Tipo de cartão não compatível", "vlKiesuPzDnB0GMEntyQf": "Total", "dh_0Cvxs6eGNbLMY-7v8Y": "Minha conta", "nHg2M0jmyailtWvemNIU2": "Subordinado", "hYo3Cd35MbLWBTXRKT_HP": "<PERSON><PERSON><PERSON>", "_bu8UzncFEL9nkg5qPBDs": "Existem caracteres ilegais", "3RP2ACHSyN8l-084R5ia9": "<PERSON><PERSON><PERSON>", "lOnLduR3l_8WRTpPLViDr": "Meu dispositivo", "D_CFBCrWWcdam_cGkm90t": "Usu<PERSON><PERSON> geral", "HPZ9-4BkksYDf9bOYA7Bq": "Clientes", "dGJYSHKNhX1So1amUzfdO": "Pesquisa Avançada", "0HfLeevl06T5-c-6Hybn9": "Movimento em lote", "RIiHXilwzDZZXOC0RFjS4": "Modificar informações", "pWAH6OlawcOyPzpCQiD3t": "Modificar modelo", "fi97wBDxt-kI86UwsAgTt": "Número IMEI", "2WVzsA1lduRG2ofkXI1gv": "Cartão SIM", "p533TIv1HaDSMgWw1t4a_": "Número da placa", "gEN4YPGMoNz8HYKg2kgMi": "Tempo de importação", "vys-I8F3EiJEhY84SJ2hE": "Tempo de ativação", "ZofyuSsvgF1XR16Ey32FU": "Número do cartão SIM", "zhx-pYPkX0douf0ybSGBM": "Informações do veículo", "AmK-1t3nyDtFLUWuuGc7m": "number plate", "5NW3PUUAmaqAZxIV48c2V": "Consumo de combustível", "8CyJDVju_fn2eLYBfCFa5": "Alarme de velocidade excessiva", "2J4Y_8V79GZz8oVP1uRLy": "Alarme ACC", "dlW7RH6fkSItw_Mtz2Ykw": "Velocidade", "Jet1u7xx2NupyskORg_e-": "<PERSON><PERSON><PERSON>", "Ht-5xfyfOJaPLfPI1Ina2": "<PERSON><PERSON><PERSON> {0} dias", "M_dYy2C4ldXbU0GcSh8XQ": "Não utilizado", "IikV2EDWehRNB_DuPyoAi": "<PERSON><PERSON>", "ihcJF8i_680UPUSADlQBQ": "Rastreamento", "FNZ9KgOe3_nZPHLr-gemV": "Reprodução", "nG52x-1HCtQNlZdzdGke_": "<PERSON><PERSON><PERSON>", "nuMj_JYBMK5JmtZA19Q7D": "Cerca restrição", "_LruCTL_GmFwkLYdKRGPL": "Compartilhar local", "d2rfxOdgIK8sx-aXn_UjT": "<PERSON><PERSON><PERSON>", "JvE2ZJEopyBOH0nltAgpC": "Mover para o grupo", "tJFFxuGRgmWrx8jXwGawx": "Gerenciar Grupo", "E2cJxJ0qcSBiNzRMKbaj0": "Mapa", "IKjGxwah7gczqSoJQWeJJ": "Satélite", "eq8ubUOnx-iIN5d8flNTZ": "Tráfego", "p5s_fNJw6Bfo4E59dkz8t": "Régua", "YmEYZtN_qZQbAvfoi-EnT": "Por favor, insira o endereço / latitude e longitude", "sxg1Ago7Wgz102blS1Uw0": "Desenhar Cerca restrição", "sgH0wIb6CRMhrPErfzZNo": "Redondo", "YRsd0eFEx-XpEoHM4bQOf": "Polígono", "vMpgu5wFuK5er9Dlkz9fF": "Nome da cerca restrição", "FMD-CUGF0TJ-UFTwcQV2j": "<PERSON><PERSON>", "3bKE4RZjKEEnP-6Lvd3QO": "Grupo", "g0Z4WYTOSoNVw6icvU8Xa": "Ícone", "a3JOJwEQkkXHUNJ9B8gzq": "Carregar arquivo", "oNh27yZ5e5LDkyOhuFrCO": "Exemplo de download", "YRfNHk3LACKCdKZKpdGFt": "Você pode importar POI enviando um arquivo Excel com informações relacionadas. Siga o formato do exemplo para preparar o arquivo", "993p62YTFUY0OgmGiplEj": "Nome: <PERSON><PERSON><PERSON><PERSON><PERSON>, não mais de 32 caracteres", "SXm03GuPs_W8_bHDUIiOO": "Ícone: o<PERSON><PERSON><PERSON><PERSON>, digite 1,2,3,4", "WdUDofXEM4vPVut2ggyll": "Latitude ： Obrigatório", "ihJPRdZfKZWbwqQg3TC6p": "Longitude ： Obrigatório", "GPiUGvJyTduYFCJS0VF_W": "Nome do grupo: Opcional, não mais de 32 caracteres. Se o nome do grupo não for preenchido, o ponto POI pertence ao grupo padrão. Se o nome do grupo preenchido for consistente com o nome do grupo criado, o ponto POI pertence ao grupo criado. O nome do grupo não foi criado, o sistema irá adicionar o grupo ", "K98R9Kl6t3eFtWbQBdzh-": "Observações: Opcional, não mais que 50 caracteres", "ntUmxqogGfJUuLkjvK6ZV": "Tipo de Alarme", "CA1jOSCwNEQbOx07cHlZm": "<PERSON>ra do alarme", "6ed5szctmCrXayO-9xgWW": "Nenhuma informação de alarme eliminável", "A7xrOFhybj9Js9R7Oulub": "Mapa Baidu", "TrFNV69OZRAH0okN4sumS": "Satélite <PERSON>", "2yf5VqhJTX_NvDljHWexs": "Google Map", "rc-VB0jPoY98SMUVoUe7C": "Bing Map", "r9Z0vOw35FkxLAXFabw2R": "Google Satélite", "goF8T8dHU6gao6PljrtNi": "Está<PERSON><PERSON>", "OCvfOWyVHgRIOLEA021Kl": "Mover", "CzvhWCze3pQCK5M_1KoaB": "<PERSON><PERSON><PERSON>", "K_s5NFaeLyxrujarrzfMk": "sem fio", "XoaUHxf1yCHUm8G-ttmOA": "com fio", "oeP8MwRcMAnrw4rbs7DY3": "On", "dMl02a_mb9XIn8CyX3A_D": "OFF", "7vdkNJd4kRZP2sQMmyxpv": "Verificar cerca restrição", "MENG0IZDkTNnl3WoRl_p5": "Relat<PERSON><PERSON>", "IJW7kt6UjfglE5oKPiJ0z": "Streetview", "gbtfYMFIkzFPpyAatuQPP": "Gerenciamento de cerca restrição", "4UElrpFn7scbLfe2oYK0B": "Subcontas", "9JFj_wyOKWTwMbNqgGrRC": "Adicionar grupo", "XjJkfhK6Qd8NBMfxckAle": "Notícias", "H7ZxbhzZKtJsZjdkN7YJL": "O serviço do equipamento expirou, entre em contato com o revendedor para renovação!", "EDcFx0tNb2Z2qVSxFsaJh": "Há um novo alarme", "kbqyQYySG3XGM2bVA-4-t": "Clique para visualizar", "LtNPazzpjZ8Ki6SHzTfs6": "Nome da pesquisa", "SUBLSbsZXN6Yu-QsHnH5_": "Tutorial de desenho", "AmtYv4ISEbwIRe2OC3KP3": "Cerca redonda", "hu_Qd-LVVX--ytlHSCPNI": "Clique no mapa para determinar o centro do círculo", "AmLlcGde2A2OHyFdaqiJa": "Arraste o mouse para esticar a área circular", "qAs2oKd_k5MsKqmoor8Py": "Clique novamente para terminar o desenho", "X7_1ZjxC6jNbqqwtSdZ77": "Cerca poligonal", "B8Vp2heRHQQFC19TLbWtu": "Clique no mapa para pontuar", "7ptVgNAs_gId9fabwwyXY": "Clique duas vezes com o mouse para terminar o desenho", "8uvQGTTGGK4YHP6RctT_K": "<PERSON><PERSON>", "RLJHlTX2QB4puw9-yaKkg": "Bloquear", "AHFQWLMEP4QcejtHVMHAP": "Destino / IMEI", "NtWULYpXIIyqfvlHmyJuU": "O dispositivo ainda não tem informações de localização.", "rVAoxxyYFdp_tC9Dnyb6M": "Movimento com sucesso!", "Y9r-CsONpxxRmg00jzjLm": "Grupo padrão", "OJAZ-TwNVqi3Bh7hztuLC": "Le<PERSON><PERSON>", "cvyH4iJuWCiLfB_Wsn4Nn": "Exclusão bem-sucedida", "kUTV5VcObCrB6kdnyfHDS": "Não posicionado", "NlOXMw3tnc5FS0jChJJeY": "<PERSON><PERSON> hoje", "bZb0QeKns1R-YrJrCIl_h": "Dias de vencimento", "a2soi5XT71DCr2H3iW75d": "<PERSON><PERSON> associado", "NprpRhvbYE9KLFSrs1Ut3": "<PERSON><PERSON>", "T3YbKXAK7yNAbyXGGy3E-": "KM", "zCKyujELRDbjx5vAbAD3r": "A conta não tem privilégios de operação", "NHp9pI1Qr5rbpe8r-4Nd4": "Insira o nome da cerca", "zhjiZcfXH8sIv6yxSkkwq": "Forma", "q_gUoHM-7bSFFC0IuHOmI": "Raio", "Csx-q_UsjB2o0oRRZf1qz": "Metros", "W7-XDEPApmz5QAAGPLqQQ": "Geo-fence em alarme", "UyLRnzSqYp4MB2K4_aOaE": "Alarme de barreira geográfica", "Oy3u-IJ6TzbP9vNvGYzw7": "Fleet Fence", "Y1eS06Ae8KWuwsunOTGsV": "Selecione pelo menos um método de alarme", "6dWutEmqMZnfcebUwMWcH": "Adicionado com sucesso", "JFQKJUrmNVUCRlOW-U6Qu": "Excluir", "kAKDzRgzgRbGj7TWa8W8z": "Adicionar grupo", "I8lfTbBiYbCyvuPzjkf_Z": "Adicionar um ponto", "CmWMoOrWpZU6sTGtkB4QL": "Por favor, digite um nome", "vjdwbqgsFl9Te_vH9kNY5": "Não associado", "z5sQqFDnFSiQzix1HGtIB": "Cancelar Geo-Fence", "wNtm7LWAqF0ewVQRdPb-J": "Selecione um dispositivo！", "zeWy2Egw7v6c3sDhGAX9c": "Excluir ou não: {0}", "mnCksVnc2zGhj-VRBgGPw": "Lote", "vBpDexpv7D_KNy_aaxmZx": "Endereço", "IjtUG09nKBjgDwMEUXJEd": "<PERSON><PERSON> mais", "kGPteGn1UwCDzRpDTYOOA": "Clique para confirmar a localização, clique duas vezes para encerrar", "LoX_Y5XcmGFXPrKocd3Vy": "Sinal: a última vez que o dispositivo se comunicou com a plataforma", "uHD_UGEfA88QbFYjH40Sk": "Localização: a última hora de posicionamento por satélite do dispositivo", "CfXyIfQ7YrDKYGs6IcEjE": "Quando o dispositivo online está estacionário, ele não será posicionado, mas ainda se comunicará com a plataforma", "I8b_zoiaDScRSfe8AcYJQ": "{0} Excluir cerca ou não?", "53WepRb0dgsGBYKlPWo_b": "A cerca está associada a {0} dispositivos, excluir ou não?", "r07mdOUMExdAaIj36JZgK": "Selecione a cerca", "fPpUDmYmZBfijNxuYo-dT": "Excluir ou não?", "LsRzknqAtdhScwNBoSsHK": "Informações da cerca", "Y9bbn5LDAbEo9O9LPdGcH": "São necessários pelo menos três pontos para desenhar uma cerca poligonal", "CUalv8dHu0RQdAaF3Wj-v": "Falha！ exclua o POI (Ponto de interesse) primeiro.", "n26EViLKtRWW73EXNwoIe": "Ponto de interesse", "GOXwqzj3woUoTJ5EcWIfb": "Editar Grupo", "OraWJ32a3r19pXlnEz7LI": "<PERSON><PERSON>", "hnmI2xuLjvPN3UZNk3qVe": "Erro de formato de arquivo carregado", "zSwW84PPwEdSY7KXU0_NP": "Carregar arquivo", "MWerfQOxskhfsWhfVL7aK": "Associação em lote", "jGphq0Bb5xDW59eNTBOkR": "Cancelamento em lote", "O8-0tOC041IMU4vRnzpNL": "Falha ao gerar link", "wue_UE3tLAxmsNy30nm8x": "Compartilhamento de link bem-sucedido", "tKwyVQxLCB3j3YDFNoq3y": "{1} dispositivos foram associados à cerca {0} ，", "LseoYpEYEbqOsg-3HO9uM": "Não foi possível obter informações de endereço", "Zbkw-Z-GkLdk92q2GqQb9": "Hora de início", "tjh3v6c9w4iWKRhEsxCel": "<PERSON>ra de término", "-L6s88wO8SJ2dBT3LRNqp": "De", "CnHQ3jqzoQUIJi5VNzDUS": "Para", "n98s6qScQ72Pfc3ipN7sK": "Marcador de estacionamento", "kErEgq9bY9npX-B_gR5kg": "Ponto de localização", "ppeXkkkZ-FQrqgexHkFd2": "Remover localização de desvio", "qis-Gzwt4NrDH4wyIX8FM": "Final<PERSON>r r<PERSON>", "2vbMWIgM9jMU39l_y0cMD": "Quilometragem de carro", "Wyr6tYmzD3CWhgVab7Bzs": "Seleção de tempo", "WPDTnaE8GGfKGXTg7ln4o": "Intervalo de tempo", "90u1wbF4gTwa5j8I-u7dj": "Longitude", "MlqbfDIYF1BFYrrm6l_F3": "Latitude", "kYg8FK3sHxS7WWLlmHxRs": "Direção", "lPdLzI8-RihRdC19Sj793": "Modo de Posicionamento", "SKow9wO-zFrJ-bTY4iiNk": "Posição", "Ii_xMJ-aK06I6WjE_7RvK": "<PERSON><PERSON>", "XbMQZa2UhMAnIgRdfYmBE": "Hoje", "NZn8Y-nZV3WRZLds93mmP": "<PERSON><PERSON>", "XuTgmO9YYX9ZgzdosA8t3": "Padrão", "3DYXD-6GdyKqIdA4QkIPq": "Resolução de endereço ...", "USsDC30U686JAi9DNifpb": "Selecione um dispositivo！", "KKjrKI9Bincd6VUnZIqbE": "Start Time Msg", "N9aI7K4jam3xJAzQDHr7k": "End Time Msg", "Kwn53CWjiBzlFcnJJJGx0": "A hora de término não pode ser anterior à hora de início.", "eFcy-BfOhf-h5PVaJ3_zt": "Carregando Dados ...", "eZz4PP9ckrYa3h1cjvihA": "Sem dados", "sOTuSCBfF-JISyxB_Hstu": "Reprodução de histórico", "D-3CnkxFwEGtcITqH7fbB": "Kilometragem", "HGjTtiFHYcRbFl8yOYVox": "LBS não incluído nas estatísticas", "d8OJkUti5rpb0ASvasdB-": "<PERSON>er endere<PERSON>o", "c2VXLffkDAgW_W7kge10R": "Tempo estático", "XbOlArDdJLP3YlKnbKEQ0": "Tempo estático", "cr0p2Sw1rzg2KIpg4kmxH": "Verificação rápida", "FgXEzLIfSgW9hXdZU2BkT": "O conteúdo do upload está formatado incorretamente", "n14RoDOGBsNntIwetoWMq": "Clique para ver o vídeo", "rMpzwjT1wGJzwTtW99xdT": "Imprimir", "8Hy4f3sEGqYcZA0E2Tgwm": "Exportar", "Oy0sGBs_ICAiweVP0kuh8": "Intervalo de tempo inferior a 31 dias!", "4CputoWSHyKi0tf0k_mKf": "Você só pode consultar os dados dos últimos seis meses, selecione novamente！", "T9djoLP0IBjXNQVB1szpe": "Redução", "fzpQzKWHHxUzOvguAlLA0": "<PERSON>var como imagem", "8zbVKtrFrdYmnIr6smhz5": "On", "REZwRP_j2rmLc3pFRLqvS": "OFF", "H4NX8-4EhG5zHLk1ytXkp": "Combustível", "hs24f25MR-Kkr7mTiNgrI": "Temperatura", "n_gGRUBDnHrrFFZNMBWQF": "Tensão", "faw91fOu9AVHonQmNZS1R": "Nome", "8jMVMcD_Ztb85usaRh0ky": "Amanhã", "coUL03ioWIVJ-B5LWsN6J": "3 dias", "7z_8O5fJQFxZ3qGxVSaD3": "Criar <PERSON>", "_qETsEONk7wNBovgka8my": "Duração", "XsXoMT3CDfSMMxm_H8UGU": "Kilometragem de hoje", "K-2ZfNUf8dV65yB98WRvE": "Cluster de marcadores", "bkEROx2ZY0mMX1e5F9fbm": "Temporariamente impossibilitado de obter informações de endereço", "7mowPMr4CB0A0uIRO4d8V": "{0} dispositivos foram modificados com sucesso e {1} dispositivos foram modificados sem sucesso pelos seguintes motivos.", "cUE4imnis7N3pb9QDkywH": "Falha ao excluir", "Yo_d2neO6liIX0B6ZtnX5": "{0} está definido como {1}?", "MCvwqkGr2-dQqS1DK3XwR": "Definir intervalo de tempo de intervalo {0} - {1}, unidade ({2})", "7GDN-cGPKDVJF3Q-qFCvd": "Modificação em lote", "2JODYAuyHeVxEZXdVhZAt": "Tempo de atividade", "KS-VF_Tm3pzzJDisCGNKE": "Tempo recém-adicionado", "Ipkl7-AfN7m1qHER0Tm7f": "Quantidade de dispositivos", "_QtO05JtDYmcbYWbiYs6y": "O dispositivo não pode ser restaurado após ser excluído. Ele foi excluído?", "Bv_g9A7SENymIKsLJ9ujV": "Admin", "itTlS_5B7wWxVWLZ2IZjG": "Distribuidor", "2biNeUu6eL5bY-ZYKcvi8": "<PERSON><PERSON><PERSON><PERSON>", "BLo9bZGWQ0hpd1oDuL3B2": "Link de renovação", "CguDuYAHEf6SmLcLCCDLD": "Aceitar Alarme Subordinado", "1teP4Jl_EJz1v5UdgQZAF": "Notificação", "J9_G307ZKQDjVoHtO7Qss": "Personalizar", "AYVWzUbEzW8nQXqBDTyC9": "Conta / Nome", "kGWxb26eIdnX_GrUdYd50": "Movimento bem-sucedido", "1F7LiURKsTqo36ZrZkafp": "Entre em contato com seu provedor de serviços para renovação!", "YQKLFb8qkw8_3Cu2O2IZ_": "Transferir clientes", "9grU7SGwJ1bWmFgjQvuDS": "{0} é um usuário final e não oferece suporte à transferência de clientes. Se você tiver alguma dúvida, entre em contato com seu provedor de serviços", "IG5Mrae_PTMgmeWAW6kX-": "Comando emitido, aguardando resposta do dispositivo", "commandSuccess": "Instruções enviadas com sucesso", "9L9roCS8UNQRrmlWr1zHU": "Parabéns! O dispositivo executou o comando com sucesso!", "lRyXdbGCaG68kEvHRl2wf": "Resultado ：", "AMWltiKoAEZG_1FlswCOk": "Ctrl off-line ， As instruções off-line serão enviadas automaticamente para o dispositivo depois que o dispositivo estiver online", "hCxLHY28pr4VAUX59yaX3": "Transferir para", "BYPkXF0WNPoQnCGOTnh8A": "<PERSON><PERSON>", "6kV2sMrY3lJRwThT_NSLQ": "Definir intervalo de upload", "jmaCB00OAHprnw3-I3hUm": "Definir alarme de desmontagem", "fAHRmeV6DRHlr6Pd_0dKv": "Número central", "UJhfS6Uu9OWK6sW6ASkHz": "Gerenciamento de número SOS", "-DQYU_0OifhlsFR-Adxv2": "Reinicialize o dispositivo", "yUWTv_VHIxw_swMm0qKyv": "Monitorando o gerenciamento de números", "DPftk33RF1Cr-9EzPUpV-": "<PERSON><PERSON><PERSON>", "YJu4l4OgiSPD6yMd0UctL": "Gerenciamento de número de alarme", "iSHNRyf7vh0Vtuop0muUV": "Alarme de vibração", "gXSOO777Zyjx828Cmep3Y": "Potência do veículo", "KuJyn-XHuYQq5O7sOtHoa": "Fr<PERSON><PERSON>", "pnfK4Vyg6n2wiCxC2iZEz": "Alarme", "ucbv19YmxM9BgaLpFQAdi": "Modo de pesquisa de automóveis", "6zwffX5jH1JpFu917DGAz": "Controle de óleo e eletricidade", "OuaI6fAFAWCRsLdLAG_Jf": "Restaurar fábrica", "BVLbawNBmWRSi4svvOQ_m": "Rastreamento inteligente", "KRilYPhoZO1sT8aKsGzEB": "Alarme de estacionamento", "IVhyLA_3ICW8aDDQr-v3v": "Configuração de fuso horário", "EwQMEv-4HfvN0bXg4Seja": "Inicialização remota", "t2xOtwpmtTfU6sc4426SW": "Fortificação", "FbNlJKmw7P2Um_Vyd3sfR": "<PERSON><PERSON><PERSON>", "m5sOa6yTHvNgFp7m82xK6": "Ouvir Telefone", "xn499WPrR7JCXsWgImCmt": "Configuração de sensibilidade do alarme de vibração", "KQxWomzE-Y7a5sl5e4ZK8": "Configurações de SMS de alarme de vibração", "JFwhNoaUQY8ROls29jwhx": "Configurações de chamada de alarme de vibração", "7BUMCa9c_IeOBWspNS9TQ": "Comando de suspensão", "ND5cf9nzQ6aQT96OhgZIC": "Raio de deslocamento", "Lt1mMvTHa_yuzv81mJ9Yz": "Alarme de temperatura", "lBUwL931fZBbruo7oJk1w": "A plataforma está prestes a expirar", "A9FNfVrnNX_IL3Sqwj9Su": "O usuário está prestes a expirar", "2fOpju8dVPzTsLD9Icbaq": "<PERSON><PERSON><PERSON><PERSON> expirou", "COB-45zsnvgfvOuofxPf5": "Tempo de expiração da plataforma", "7_McUY6-mnqlIwzq_o7c6": "Tempo de expiração do usuário", "x6j0xlfBVYCgf3-pXoh7Z": "Consulta expirada", "XbFegIO6XdtwVCLj3tHjn": "Data de início", "k3rb7GIYeArL-6QUB2jYR": "Data de término", "JOHRDjZ4DXNl0KtpQTn5Y": "Consulta de tempo", "r4dYsKXhyAkW9-WKvNGd0": "Um total de {0} registros correspondentes foram encontrados", "V7gpSV-XtOtpWE978XWMe": "Alarme SOS", "KSTTK7q9bKSNc36SGzqL2": "Tipo de Alarme", "GHkNMYw76nVM5W4mRICca": "Configurações de alarme", "YWO2tIL3JS9UWLacM-ECj": "Configuração de combustível", "xwcKgTp2DvPPU3BRpr4kw": "<PERSON><PERSON><PERSON><PERSON>", "error10003": "<PERSON><PERSON> <PERSON>", "error90010": "O dispositivo está offline, comando personalizado falhou!", "error70003": "O valor do controle remoto não pode estar vazio", "error70006": "Não suporta ou não tem direito de enviar o comando", "error20001": "ID do veículo não pode estar vazio", "error20012": "Veículo não ativado", "error10012": "Erro de senha antiga", "error10017": "Falha ao excluir, exclua o subusuário!", "error10023": "Falha ao excluir, o usuário tem dispositivo", "error20008": "<PERSON><PERSON><PERSON><PERSON>, IMEI já existe", "error20006": "Insira um IMEI de comprimento 15", "error10019": "Formato errado do telefone de contato", "error10024": "<PERSON><PERSON> repita vendas", "error120003": "Compartilhamento de links desativado", "error10025": "As informações do dispositivo modificado não podem estar vazias", "error2010": "Carregar arquivo", "error20002": "Não existe número IMEI", "error10081": "Número insuficiente de cartões de renovação", "error10082": "Não há necessidade de recarregar para o dispositivo vitalício", "error3000": "A função foi atribuída à conta do sistema e não pode ser excluída", "error103": "A conta foi suspensa, entre em contato com seu provedor", "error124": "Não pode operar sozinho", "unArrowServiceTip": "A plataforma devida é menor que a devida pelo usuário, selecione novamente, IMEI conforme abaixo:", "editDeviceTips": "Confirme se o dispositivo a ser modificado é do mesmo modelo e não está ativo!", "uWFemTPKIbGOyQ17ugfkE": "Última posição", "tempSetting": "Configuração de temperatura", "tempSensor": "Sensor de temperatura", "tempAlert": "Após o sensor de temperatura ser desligado, ele não receberá dados de temperatura!", "m7PB-o0n69UQe3-RLFtm6": "Desligamento remoto", "d2v88dFMBwc6IBasbVZUB": "Nome do item", "laXMcUYMWZ5Z1GJYdq5_g": "<PERSON><PERSON><PERSON>", "8E_5DReq4p9vShgeUzwDv": "Sem direito de operar", "-9EQ9kbkuRXcOGCWQ6cMq": "Data de Manutenção", "zdVunWKu5AhodKvqgP4rI": "Tempo de atualização", "0SbtD71xRYUoRVQf2nVLg": "Prazo Efetivo", "5WAnTiOO5Wp3w-x0SVjSN": "<PERSON>v<PERSON><PERSON><PERSON>", "_soRxqN9L0mb6hSyXV6Vx": "Fazer efeito", "npm5kq2r28GlD7S4lIe14": "Data de Criação", "lWW57n7Ua158IiiFvL0jU": "Hor<PERSON><PERSON> de início da reprodução do histórico", "jZ9YZbQgfeKLiDHeOQF4Y": "<PERSON><PERSON><PERSON><PERSON> de término da reprodução do histórico", "wHLiHQweFr6Dg4PvyCpT4": "Período de validade do link", "BnOTpdevlmr1HZw_IH-KH": "Compartilhar link não está habilitado", "g0lDgo0eVJuPrPB5Z5Vgd": "Próxima kilometragem de manutenção （KM）", "Rj0tzi6vp9nWZtswv3ipq": "Próxima data de manutenção", "nwJzmS9Osv1MtnLuWwzIZ": "Kilometragem actual (KM)", "r9QdHdk_7rac9hv4aw9cl": "Copiar", "9fBvgXknsLqW_Sr8WqrIj": "<PERSON><PERSON><PERSON><PERSON>", "3QqqxkqFx7OzOMr7KoS0F": "Kilometragem total atual", "7fSVAcN6FNA6tRDv-_R-D": "Última kilometragem de manutenção", "kpJpvXpu9gAqYd8cN-Tcr": "Próxima kilometragem de manutenção", "dsC8Y9WsQautq4sdheMJf": "Data da última manutenção", "nqL_foYkFFB3XXif3unHh": "Notificação ativada", "TogUVo2D5tJSTDaOr-n1p": "A Próxima kilometragem de manutenção não pode estar vazia", "fidPvoq7zsvnxAuSg5NGM": "O próximo tempo de manutenção não pode estar vazio", "bxeltfqSrYbvgH34URuDu": "O próximo tempo de manutenção deve ser maior que o tempo atual", "bsQ5K2AQ9sCuBtOMxmqf_": "Cartão de renovação", "YodpqD_6tNSUO81daApII": "Selecione o dispositivo a ser modificado primeiro!", "pB2SeJU_kVoVlVfQFD-sq": "Este pedido foi emitido?", "5xdEeVm0qnz9SxT7MG8qC": "Plataforma expirada", "Py6WRg65Hlhr4ZZeN4JTK": "7 dias", "9hrkM5Xiik9vRyY2TjZC7": "30 dias", "DwPR-SBjnWtgyoWDDpzr3": "60 dias", "wcGDlaCZO2gApNapnD1GE": "{0} <PERSON>as", "zc7x8FJOhr0K527ImZQy8": "Se houver um conflito entre a configuração do alarme de temperatura da plataforma e a configuração do alarme de temperatura do comando, a configuração da plataforma deve prevalecer", "mB7Q6tNij2-TEQuj_EQip": "Niedrigtemperaturalarm", "ngQ_u9YCVROkTgspYaeDF": "Valor do alarme: temperatura", "W5vK5LEvgAc_JHhPcxySK": "Hochtemperaturalarm", "YJK4tJOghE3DJ5hCCblYl": "Descrição de exemplo: Se a temperatura for> = 20 ° C, o alarme será acionado quando a temperatura atingir 20 ° C a 127 ° C;", "fpq5Sc6vhKfXv3uT3-_v0": "Manutenção de quilometragem", "f7IWU1fmCaxD0JjYJZoAR": "Mecanismo de lembrete: {0} antes da quilometragem atingir o valor definido / 3 dias antes da data de manutenção, uma notificação de lembrete;", "PafeQj-F3Eo7YM2eQupv1": "Suporte a vários e-mails, separados por ponto e vírgula", "Ce2R23rARaUED1OI3Xch5": "Se marcado, Notificação de e-mail + Notificação da estação, e-mail pode ser alterado em alarme - E-mail", "m7syXSguP29TzdRR-jtqL": "Selecione um arquivo CSV", "Nt1bQaoSwAA1lWy-Yki9-": "Selecione o arquivo", "wjjTfBWo4sFD6dDqMUA8E": "Instruções de importação: 1. Salve o arquivo excel como formato csv 2. Importe o arquivo csv para o sistema.", "JoV7M-08iOvdSofJL2dXv": "Modificação bem-sucedida", "En636CNcxTxoDjeZPjArx": "Modelo do dispositivo (após modificação)", "C2cZ15pSLdgYB6PdIEcHT": "Hist<PERSON><PERSON><PERSON> de Comandos", "oQjvZaVtcYehcHekft4JC": "Terminal não compatível", "dHJWVOHGgZNJMVJctlIgr": "O terminal responde com sucesso", "3ouZgJBDf6-vt--s9KqFJ": "Falha na resposta do terminal", "cVVOCbNxQLFcv1HwysKUM": "Não enviado", "qtX4hKNhrIq4HQWKq76qF": "<PERSON>v<PERSON><PERSON><PERSON>", "VC9bpkEpdi5BGS4bxu-Ve": "Foi emitido", "KV0PCwFh9KzMeYVPkXd9i": "Execução bem-sucedida", "7055N1pThT9t2nVidaqYf": "Falha na execução", "qMnlim32WjTPSVJezsiZS": "Sem resposta", "DzuqoOisOoNLlaCC-Ji_y": "Data de Venda", "VZEMI1HZaAQEAOccCxLgK": "Tempo online", "qBNdY9Hof9Y2O9F204y0I": "Registro de manutenção de quilometragem", "2_GOBlwzPbyr5HQDEsUOK": "<PERSON><PERSON><PERSON><PERSON>", "CgoQEhz77jlNp0vrlVxSn": "O vencimento do usuário não pode ser maior do que o vencimento da plataforma", "4HbPhU8SVH27GO7vwb5Az": "<PERSON><PERSON><PERSON>", "F4gONEExeouoNaqxfRgUl": "<PERSON>a inteiro", "YxtnLS-9penDcV00ukAce": "<PERSON>a", "1PMQ9WuBMTKpUj5pxY9s5": "Noite", "M3BBgNFBGii6WiaU5jQsY": "Notificação de Alarme", "gVQwdq1hZqq52wB7i1u1n": "Receber alarmes de nível inferior", "M-aGJuBti_0J8Qpbvmqzf": "Informações da estação", "mpeSz021INSNFgOa8BCpf": "Mensagem de alarme", "1ECHLtFguDsDq_3uOG0yp": "Registro de manutenção: Depois de definir o lembrete de manutenção do veículo em Meu Cliente-Equipamento-Detalhes-Configurações de Alarme, verifique o registro de configuração de manutenção aqui ou adicione configurações de manutenção de equipamento por meio desta página.", "RE62y37yvH6qImoM4ss42": "Registro de compartilhamento: Depois de compartilhar a trilha do dispositivo na página de monitoramento do veículo, você pode visualizar o registro de compartilhamento aqui ou criar um link de compartilhamento lá.", "_WlC2Ljs_RF5cLhyYJnSi": "Novo link de compartilhamento", "1sW-OzlYc_q1b9n_NcFpi": "<PERSON><PERSON> instru<PERSON>", "vxvA4VAvFjv9MTiMd5EZB": "Check Command", "enQR7BvU-FbOHOhV869wf": "Verifique a versão do software", "ZjesfJleICyF8c_POTRz8": "Verificar status", "GTC0khuQYbxhjkOcU-f6G": "Verificar latitude e longitude", "BbRsM3ZxO3aXYhzCH6iSU": "Verifique a configuração dos parâmetros", "lLcqsvnFC-b1pvBbyIudG": "Verifique os parâmetros GPRS", "9B6fVyRW86BNLPYKEXqaz": "Roll Call", "6vK3EWj5Tu8CeExT-hBsJ": "Verificar alarme de lembrete de SMS", "qbSvjPXqpmObBi1yVrlzi": "Verificar número de vinculação", "vCi97K5uNUBnZV697Gpjo": "Nome do comando", "j34MkEOAqjyT80854yoTQ": "Conteúdo de instrução", "s6lAnu683jaqbQ7tCaJ2S": "<PERSON><PERSON> de en<PERSON>", "8Ch-s1H4v0OW2yx82azFE": "<PERSON><PERSON><PERSON><PERSON>", "5be1cXISzVRWtU12xrHrV": "Tempo de resposta", "hVknJbW_i4WBVBCdHbYZz": "Envia<PERSON>", "F3JFF-1PIyXW-jnh4bsMd": "Telefone central", "ZvLlyWA7PnfWeaWbJ9UQS": "Alarme anti-demolição", "2izr0ZUzt8xrZhKYzDZh5": "Alarme de plataforma", "c70mJqcGxaOeuqV5V9Z7c": "SMS + Alarme de plataforma", "wVmqyjZhzO8ddRA4vDwhi": "Alarme de plataforma + SMS + telefone", "DyrTMwE_4K5Uk2TKYFLIh": "Valor do alarme", "M2L1RdEwKiK8xe739Zd6i": "Valor do buffer", "K3pwVRJqNEWbwF0S786O8": "Intervalo de tempo 0-255, 0 significa fechar", "jQC4bDCPKXPp4Lff_4hG6": "Alarme de plataforma + Tel", "eKj89wVWw918dADwmoY6a": "Número do celular", "qLAbckbjkA2Jh7bjocw1V": "Implementar freios", "u4rNU4FweIl9vmKO8n7Bo": "Tem certeza de que deseja verificar o número de ligação？", "3Z1ZggbT0y84ej1_bi1hx": "Tem certeza de que deseja verificar os parâmetros GPRS?", "nRNHg0_EYHP_Wc4PiR2ib": "Tem certeza de que deseja verificar as informações de latitude e longitude?", "K9LhupOLQu06xBR1c8CY7": "Tem certeza que deseja verificar os parâmetros？", "z7H1qYX3aJE35pbQU1Mx3": "Tem certeza de que deseja verificar o alarme de lembrete de SMS?", "HTDQgJ7b2SoT9Rw7CdhQU": "Tem certeza de que deseja verificar o status?", "Di-hmtqF6siUZJy2QH6DA": "Tem certeza de que deseja verificar o número da versão?", "ktHad5imbbn2sPz06Utbf": "Tem certeza de que deseja reiniciar este dispositivo?", "zU8zLd7T1sJBQIv0bzTu5": "desconectar", "dRqd3njVatWzZR2v-Dejt": "ON", "ZRPZ-M8Mk6NjGlbu6F7uk": "Tempo de estacionamento", "7-bUdwYB1cZvFilxmF6WL": "Ativar alarme de temperatura", "AQ97fUpbsZxJQnd6NikCY": "Alarmtemperaturwert", "DX4-lrE6fLMHp5wOxwvMr": "<PERSON><PERSON> hor<PERSON> o<PERSON>", "7bV8hfRhGldGWdo8Vb1il": "<PERSON><PERSON> hor<PERSON> do leste", "y6VrMMGH4Mh4mO7HsHmBj": "Hora de início", "BOvye1w4YI1Hbo_9heqdM": "Tempo de descanso", "Uu6qeV2XLpiwboneINLak": "Erro de formato de intervalo", "qeJLq1tfQF-5G4t003P0c": "Configurações", "fQWV5H2FZT3uLQ0sLLltN": "Selecione o nível de sensibilidade", "nP1vM9sfXoEhoHMXN3-j7": "Configuração de escopo", "ePlqFNBNJSFjQI6j-eSUi": "Nível de vibração", "mCtr-rCplu_IYmqN77Cw9": "Plataforma", "SLQRfUeAU4Dvy5O1WNHT0": "Plataforma + Mensagem", "tWFKyzzfR_wlbUtuvEIGj": "Plataforma + Mensagem + Telefone", "tWFKyzasdfsdfsdfsdfdf": "Plataforma + Telefone", "Qjag3m9P6bSba14_Zw745": "Modo de vibração", "Bs2brZAj-iy3iAW34SB3D": "Forma de lembrar", "NXoB1WqRAzDcN0HJsa0aw": "Intervalo de retorno", "tziOOsAmI3dacmVYXP1qI": "Hora de despertar do alarme", "0iPIesWJahFY1C6WIW2Sr": "Hora do Despertar", "0tZ4moV7mgZFjX4qqmNw5": "Intervalo de exercício", "gbhcMXdMMOyETBXIHgDZ-": "Intervalo estático", "j5J2lzHmNEClnupw7vgF8": "Neste modo, o ponto de entrada no trabalho ativará o posicionamento GPS, e o ponto após sair do trabalho desativará o posicionamento GPS;", "Es2otsyREhsk7D6vv3TBu": "<PERSON><PERSON>", "PdWLpHAYftr8mUT61cQVk": "s<PERSON>bad<PERSON>", "om5IfTKXo7Qi6fz986E6c": "Domingo", "CHuzsvL1dgA0SVIzXsUON": "Neste modo, só habilita o posicionamento GPS dentro do intervalo de data e hora definido;", "KeBSto-LPbBMKb7eta5NT": "Modo de retorno cronometrado", "mtCLx2EVthWETG3lLl1k8": "Modo de despertar do alarme", "Nfck51t_8ikXK6fKDt2g5": "Modo de posicionamento semanal", "q7c1wxDQihrNW-5hKBXwK": "Modo de rastreamento em tempo real (consumo de energia)", "giACddDYdcUy9-wkgaotd": "Modo de rastreamento de intervalo (mais economia de energia)", "nbB7jDHhjihV246qgHGii": "Modo de posicionamento em tempo real", "9YwwBZ2KTfGNGEt_PjDj7": "Modo de posicionamento único", "FbU62YUMNlB98e4figT9t": "Modo de rastreamento normal (economia de energia)", "IyWI4ui9nSVwNx6hh5l3Q": "Modo de despertar único (muita economia de energia)", "6wNw-7dzRHALobiqhwNHX": "Modo de rastreamento de tempo (mais economia de energia)", "1ubUpBg3y15OGlxqErTwT": "Modo de rastreamento de tempo (economia de energia)", "kppRYbv1HsYqNSpzmnz2_": "Modo de rastreamento semanal (economia de energia)", "lYy-c9I-wddGRhq3__n-o": "Modo de rastreamento único (economia de energia)", "_pLfdO5dD6EIHGzn3EccU": "Modo de hibernação inteligente", "a1n_G5EuFGYkPvjxlUwuQ": "Modo de posicionamento por período de tempo", "ZumqxPha0ETt4s6IWj_pM": "Modo de check-in", "bFR6ganf7Gh2ntOnWJZKM": "Modo de período de tempo", "o6e4_Chy0zEchuvkyrCSC": "Monitorar", "XoaIcv8RDjIKhJCCe-Bgg": "Falha de modificação", "a-Eywl004h8c0Z8yaNvPq": "Modo de rastreamento normal", "351stQZgFO1epbSXb6-g5": "Insira o tempo de intervalo!", "CVeOX4a2fE17TSJnYHWF_": "Por favor, insira o intervalo de tempo correto!", "PxTrEplTrqAsSWEaCaA5L": "Por favor, escolha a hora!", "OFZLob4ixsZGAbEMkCeGB": "Selecione a próxima data de manutenção", "kB5kAAr1kMy-xk7poKZjB": "Por favor, informe a Próxima kilometragem de manutenção", "_YGwsF4tX9xp3j5lGqgDk": "A Próxima kilometragem de manutenção deve ser maior que a última kilometragem de manutenção", "kOzwGA87dUbTPO2J8VAAE": "A Próxima kilometragem de manutenção deve ser maior do que a kilometragem total de manutenção", "5xdEeVm0qnz9SxT7MG8qw": "Expirando", "5xdEeVm0qnz9SxT7MGrnq": "<PERSON><PERSON><PERSON>", "rPn2PAqmNooNekpD8vHZ_": "Compartilhamento de link expirado", "cA4mkzbvSQKVnKohu-NBc": "Erro de link", "7ddIPjWcWGS0crmj1wera": "Limite de velocidade excessiva", "7ddIPjWcWGS0crmj1tuff": "Marque no máximo {0} itens", "7ddIPjWcWGS0crmj1dwcW": "Dica: Depois que o comando for executado com sucesso, o terminal discará automaticamente o número definido", "aHz9YjNwCHLIwsfvnU8-X": "Os usuários normais não podem criar subordinados", "SYFx7Jw39HypqfmpAoSMi": "Registros de manutenção já existem", "ubZxA7UWGmQd_Gn_FpuUS": "Definir Comando", "9OMax5dpvzrcm_Krk5Qc1": "Ligado", "L1vd4W1ECVlMaIZ_IDKvr": "OFF", "rEY8A9V_1uxYDZCM5MwZq": "Dispositivo não ativado.", "kB5kAAr1kMy-xk-flusio": "<PERSON><PERSON> de combustível principal", "_YGwsF4tX9xp3j-flusio": "Max", "kOzwGA87dUbTPO-flusio": "<PERSON><PERSON><PERSON>", "5xdEeVm0qnz9Sx-flusio": "<PERSON><PERSON> cheio", "5xdEeVm0qerterthlusio": "Valor do alarme de quantidade de combustível", "rPn2PAqmNooNek-flusio": "Configuração padrão", "cA4mkzbvSQKVnK-flusio": "Formato do tanque de combustível", "qwersfasdfegergewvdfd": "<PERSON><PERSON>", "retrrtdxsgerhdfgshsi2": "Caixa cheia máxima", "dfgdfthtrrhfddfglusi3": "Caixa vazia máxima", "aHz9YjNwCHLIws-flusio": "Padrão", "SYFx7Jw39Hypqf-flusio": "Oval", "ubZxA7UWGmQd_G-flusio": "Formato redondo", "9OMaeerfdgswertehhrtf": "Consulta de tempo avançada", "9OMax5dpvzrcm_-flusio": "Irregular", "-mwXOBzafJJzS5ECa96HK": "Um total de registros {0}", "e83QYflrstV3mHtOKQUA8": "<PERSON><PERSON><PERSON><PERSON>", "2wrWc6_xwJGFFzHae1FBm": "Gravação", "8_IICEycxVRCZ9oVUc4XO": "O número IMEI pode alterar 1 vez para a placa vitalícia.", "NORhEcC5v7cp-BdxgZl5u": "Novo IMEI", "wYeSVrHLh5AYblQHEkeLW": "Digite um novo IMEI", "yzdVhW5qve_BhwkYrJnC2": "Selecione o modelo modificado", "913JlvNitsiZ1R7_ypSye": "Data de início", "znQ4cCuYAjv_vrmmN8SV7": "Data de término", "xSIfVXjSrVDgcrpNE2Y5q": "<PERSON><PERSON> de", "hwx9hX29-Q8iAi0Baajhn": "De<PERSON><PERSON> disso", "6g6wxHvMzcyEZqSUFYY97": "IMEI antigo", "9Z5P7gAyBPa7F9zacoYIW": "Operador", "5oCJuHcl4bkZHJoS78gHM": "Digite um IMEI válido que exista no sistema, digite novamente!", "U-u7gdzttM7kMFNe73MuA": "Por favor, insira o IMEI vitalício!", "0rcBbUV3Iju7g4de37PhZ": "O IMEI foi substituído, digite novamente!", "8OxUTZHtyqXfbhaGnWguO": "O equipamento vitalício não pode ser substituído por equipamento vitalício!", "dN8YQHV0UmJ0biAdQ5eUs": "O novo IMEI só pode ser substituído após selecionar o modelo e importá-lo!", "aJqS-MRCOEzT3vAoobwyA": "Tem certeza que deseja substituir {0} por {1}? Clique em ok", "safweerhgrsgsdgerwetd": "Selecione um intervalo de tempo", "rethrtyjsdfsdfgerhrtj": "Selecione o tipo de consulta", "5M1oL9E7Oa4VYJrSBCMI9": "temperatura> = 20 ° C, então um alarme é acionado quando a temperatura atinge 20 ° C", "21C7S-yVPUtM_umDE9-D3": "Suporta a entrada de 10 a 3600 segundos apenas", "8EZPAmUvEcgIQlu9Af7lO": "Suporta a entrada de 180 a 86400 segundos apenaspenas", "0Yj_478WQcnoLCTAqhX28": "O dispositivo reinserve uma nova informação de localização", "5VyshM83XNAdnIDpGFwQe": "Se o lembrete SMS está ligado", "sL8DUa562p1PB4TCqLQN0": "Exibir o número encadernado", "bFypuArP-qFt5NBFfwsRw": "Cancelar o estado de defesa", "HJMoeiuNxOViTffIvIAGI": "Defesa Depois de estar parado para mais do que o tempo definido, o alarme de vibração pode ser desencadeado após a fortificação.", "eosV4vPkvxGINYFnMqTOc": "O conjunto de parâmetros de dispositivo inclui o dispositivo IMEI, o intervalo de upload, o número SOS, o número central, o fuso horário, o comutador GPRS, o nome do domínio e o número da porta IP, etc.", "XoYqBt9yTe2Cumcn88i3-": "O status atual do dispositivo, incluindo o status da conexão GPRS, se a energia externa é conectada, valor de tensão, força do sinal GSM, status de GPS, etc.", "8pCLreWQJI7SM-dCnGDqa": "Os dados específicos de longitude e latitude do local atual do dispositivo", "qDp8wbELHWHCtXR4kIKLr": "Versão atual do programa de dispositivos", "dnRal8YUBaR4lwcct5KZh": "Status do GPRS, número de satélites usados, nível de sinal de satélite, etc.", "Aiy8_YOvXwvR1y6NH8vzH": "Defina o modo de retorno de tempo, o modo de semana e o modo de alarme não são eficazes.", "emPLIpp281tgE1Y9-SfqX": "Digite 0 para o intervalo de tempo, o que significa desligar o modo de retorno de tempo.", "qPtdLQjjVdR-hBSU9vYhO": "Defina o modo Semana, ele enviará automaticamente o modo de retorno de tempo primeiro e envie o modo de semana.", "0fmV32e9MeQyY5lqIXLf6": "Defina o modo de alarme, ele será desligado automaticamente o modo de retorno de tempo, desligue o modo de semana e envie o modo de alarme.", "9kMue__kTvle19S0-jw98": "Formato de e-mail incorreto", "d9YwxF2OSgivkz7M8H5Bc": "As informações do dispositivo estão sendo retornadas e podem ser visualizadas no registro de comando posteriormente.", "asfwefsdfsfsdfsdfgrer": "Renovação bem sucedida", "ULolU9NS8lnR1QkO3rSsm": "Resultados modificados", "3j-R_aY3-zIKPRKka1zBg": "Este endereço de e-mail é vinculado a outra conta", "bzVAxsJEtQtawIDU5RBt1": "Por favor, insira", "6nl7HBlbHrOtUvi-N0OPM": "Notícias da Estação.", "b0rVRhAL29g8HAgKi6cD6": "<PERSON><PERSON><PERSON>", "4FFZ6E8mwSsIR99YCODZG": "O<PERSON><PERSON>", "EG1qollS3y8DsffUEQ11i": "Você tem {0} dispositivos que estão prestes a expirar. Para evitar a desativação do dispositivo, por favor, renovie-se no tempo!", "Y33r2M_1dN1ut_Bp2XdSU": "Lembrete Quente: Os usuários comuns precisam entrar em contato com o provedor de serviços para renovar o dispositivo, e os usuários do revendedor podem usar o ponto de renovação para renovar.", "H9Nda-k7XDEYdYb0Oxd01": "Prestes a expirar", "JgHgaEMi5JzduvJ65HHG8": "Voltar", "WKkPZV30ap53zWCZyuwuD": "Você tem {0} dispositivos que estão prestes a expirar, por favor, renovar no tempo!", "Ux12pE1OeZhyzEB5Svd4O": "Atual", "GWh39-lGjyVpcylYO7u3j": "Um total de registros {0}", "YoxsJOzz0McmdP3PDAcTb": "Tempo de expiração", "6L0b2U18GaqDt7DpA_yiy": "Contente", "Hi_cEuIxsZ92-zskwssT7": "Usuários-alvo", "zUiSwYI9z29DqQ37Yt4om": "Por favor insira o nome do usuário / conta", "6VlwgEKwHw_dv9rtupqSQ": "Número de resposta {0}", "htnsA0a3Ra2_fYJ3r-5x6": "Manutenção de quilometragem", "13b7lm7aQ9H9sHFfoIXPB": "Kilometragem actual", "c0nJYvCKZYWjJzvr-X9GM": "Último momento de manutenção", "TNGaQ-bXQxYyhgHYvOsPC": "Última kilometragem de manutenção", "hjO1Vg319X18G9FnlJEPY": "Tempo de manutenção predefinido", "KsoQwWQ7fu-RaTUnWvTl1": "Kilometragem de manutenção predefinida.", "s5SpmyxQe30oLsV-H-g88": "{0} le<PERSON><PERSON> de manutenção", "DKIGDk2OpzWx0lfNjGC9h": "Renovado com sucesso {0} dispositivos", "iVazSwP-tbFP5TcNnyS7q": "Lembretes personalizados", "QDYMtaj3f4DzIQ0ZGMOPu": "Responder ao feedback", "yJvXIZPZa3JFBQNZpzKNb": "Você tem certeza que quer deletar?", "e3XxVhLcsTxKYpgRDLYn4": "Antes da renovação.", "5IzURNFLb869S2Iil0zBK": "Após a renovação.", "RaCuDe8vPz5OURmKQNTMm": "Você renovou com sucesso {1} dispositivos em {0}, detalhes como abaixo", "7Qr_N6o7eIW6DnbI-fmC0": "<PERSON><PERSON>", "LxQFx33DbLbDCFMGVbm9r": "Dispositivo de alarme", "s5dkA82oFKqgptUbqsic4": "Publicar", "6X_prYTdd-NdxYrTzYiTv": "<PERSON><PERSON>", "S3hMIOyR7bzPA6vDcwblT": "<PERSON><PERSON><PERSON><PERSON>", "ZgqbT6PS21BDxcwIKFkO2": "Língua", "O0aHYTeJjn0FH79_1eXvf": "Criar Data", "CW7ycrIhRhBNT3JiCPuvR": "nome do cliente", "DNheNhg94oWEH8ZCjFPxE": "Desative com sucesso!", "mlTAaKUQGc5csKwzYGmsU": "Detalhes do anúncio", "vmaYyrRFsLx1fdLisRQg5": "O evento expirou", "mIJPpkv893lCNJ6q9lO7P": "Customizar", "GancrVwpSQ_JP9HG1XU8N": "\"{0}\" está esperando para ser processado", "yW2jL_VVgNCa1ZK_CVOme": "<PERSON><PERSON> \"{0}\" foi processado, obri<PERSON> pelo feedback", "mPnTB8w6f32SrMN4DMi6G": "Em relação à \"{0}\" foi processado, responda personalizada", "dashloadi3JFBQNZpzKNb": "Expira em breve", "dashloadiTxKYpgRDLYn4": "Estatísticas de status", "dashloadi869S2Iil0zBK": "Estatísticas online", "dashloadiz5OURmKQNTMm": "Ativação total", "dashloadiIW6DnbI-fmC0": "Se<PERSON>", "dashloadsdfghhdfgdfgd": "<PERSON><PERSON><PERSON>", "dashloadiLbDCFMGVbm9r": "Comparação", "dashloadiKqgptUbqsic4": "<PERSON><PERSON><PERSON>", "dashloadi-NdxYrTzYiTv": "Todos", "dashloadibzPA6vDcwblT": "Normal", "dashloadi1BDxcwIKFkO2": "Anormal", "dashloadin0FH79_1eXvf": "Mensagem não lida", "dashloadi9GePO6A6jBjr": "Expiração do usuário (opcional)", "dashloadihBNT3JiCPuvR": "Observações (opcional)", "dashloadioWEH8ZCjFPxE": "{0} Atualização de versão！", "dashloadic5csKwzYGmsU": "As estatísticas de serviço desta conta (incluindo subordinado) são inferiores ou iguais a 30 dias", "dashloadiLx1fdLisRQg5": "Estatísticas de serviço desta conta (incluindo subordinada), o serviço da plataforma expirou", "dashloadi3lCNJ6q9lO7P": "Número IMEI / Conta de login / Nome do cliente", "toIP_sB0-7C-n32qKpzMk": "Atualize com sucesso!", "t-pf5RwPxV_OcNP1WidEM": "Copiar Share Link Success", "Jj2KZHdeUO7O0GAA9qykl": "Falha ao copiar link", "rB3aU56xAS9EVj5XVZ5Yu": "Depois que o comando START REMOTE for executado com sucesso, redefina o modo de trabalho do dispositivo", "pPGMtc0DrQn1jiEFpVe7Y": "Data de validade", "Nln5aIwzw0Bqh386-rPkg": "Conecte-se", "PBm_ngQE-uvzxq3XBwYOs": "Sistema de rastreamento global", "4PUHYIKijDJgBGwb_X6oX": "Suporte a imagem de formato JPG PNG e o tamanho não excede 1MB", "OMFetLPAXXivTkvAOSIff": "Avatar.", "Q30JLZF3GiZdfWq1MaCR4": "Informação básica", "6rP2gYwNfQwClJBEmxBEy": "Número de telefone", "9sK_TGewQl5zfDn75RpIq": "endereço de contato", "KP0yfMnOJWx1FBWS-opEk": "Informações do provedor de serviços", "kA-s0kUkwL45l6iYv-TKh": "Número de contato", "IokO2UlgeBdvUDTLyX6ln": "Mude o retrato da cabeça com sucesso", "YPoyixx0lWnv9IoARFDls": "Salvo com sucesso", "vMngAC40PvIZ-niUfbb8U": "Definir nova senha", "mxMq8Gu5X1vUWmqndRe7g": "Terminar", "MslCKqFFYRY2OyL-Zo_Dk": "Provedor de serviços redefinir senha", "0-v98dDMuY2bdr08YIehg": "Política de Privacidade", "jD1yQ2o2qqCboJHfrm7uS": "Termos de serviço", "nmDH8KjrgNcL1-NXZoDqu": "API Aberto", "Cyllha7ZgGJqvuwHsEqaD": "Conta/IMEI", "prX3JUTouWcIjV3_duH37": "Por favor, insira o código de verificação", "ZcMHgev4PYIoGliHjPmtn": "Código de verificação", "NC9hUZ2SjihaL3Jguqap0": "<PERSON><PERSON> outro", "SP0RYsgGSCmrPHOn1LXCF": "Próxima Etapa", "BbZrXXe5mpSu5LWgE0htM": "Conta não pode estar vazia", "HV2CNVNoo9AxRsVyBNYPM": "Código de verificação não pode estar vazio", "bgEEvDhpqJetROOyhaAmD": "Essa conta não existe", "caceMSPMiJj6-8Gnn-Mj5": "Erro de código de verificação.", "q6NfOlwZotvZUoO9xT_Js": "O código de verificação expirou", "-mkJJAlBTMfaCMMD_cUVA": "Voltar para a página de login", "-1NoOoI1-XfLzqLSBP2cC": "Não é possível entrar em contato com o provedor de serviços?", "UrmoTz-bEoYVUZudlGkmj": "Substituir o método de recuperação", "eDBwJLmqbZI_jLHPeNwLR": "Confirme a nova senha", "9o7fPzhzvlOW_AjZTYvQA": "Por favor, confirme a senha", "OV_u3cmw3_nqGdgXQhMq1": "E-mail não pode ser usado?", "OWU3uV1ucUTxye3fWEOYn": "Nova senha não pode estar vazia", "CS1umvWqPvy5fyx5vpLev": "O comprimento da senha não pode ser inferior a 6 dígitos", "VW5d-hqpDfpW1wxKz7GZB": "Confirme a nova senha não pode estar vazio", "7aDPEpfMB28Q6UpQ3KiPe": "Sen<PERSON> s<PERSON>, por favor, digite", "AuAJ8pz6FdyRSEDF79UtG": "Obter código de verificação", "rNhMvrlEMqTqvgVETayyC": "Re-adqui<PERSON>r depois de {0} segundos", "2lSdlLHY13XWUtSy6zJ1R": "O código de verificação expirou", "3CyUohrSLLacv5So4dPGX": "Senha só pode ser uma combinação de número, letras, símbolos", "EYoz5FqDoi_qDRmAsXOAW": "Recupere a senha com sucesso", "Lv3v6w6vj3VIrTLiLi_uz": "Verificação de e-mail", "rVQn6WUbLJreV967ZLfdy": "Você precisa recuperá-lo por código de verificação por email", "zb1y7CpuMLs3EACUW8OnM": "Você precisa entrar em contato com o provedor de serviços para redefinir a senha", "Oo_W9j9D9ILdAR5k-vcW6": "A conta não está vinculada ao e-mail e não pode ser autenticada", "CRyJ__yaI5l27i-XQkH-C": "Todos os direitos reservados", "Atr_RssdPBb1BwK-5pdcT": "Por favor, marque a mensagem primeiro!", "CGfq1mj1IRuij49lT_V9r": "Você tem {0} dispositivos que expiraram, para evitar a desativação do dispositivo, renovar-se no tempo!", "zfAaSueFmo1M44F10jQfR": "Exportadores de exportação.", "SrGP45dYF4TfIMabJ8GI0": "sim", "PSJif2BD7ijRnlbhUCzbL": "Não", "Kclu7hitguzQnIENer23F": "Inclui dispositivos inativos", "nlTS_5a2cN5XVp4KtxjsC": "Campos comumente usados", "KcBBwSU5GF3Vi-dyE_STX": "Selecionar tudo", "d3qRaIFxfxVC8m9k5uUIK": "Tipo de equipamento", "EVbsxypelcsn0TWWQ86fY": "Status do cartão SIM.", "9HmB7gxHH1KowJ2BRUtQz": "Tempo de expiração da plataforma", "b-ZK14jqMcQs1RkyRsi6T": "Tempo de expiração do usuário", "gwHls4t8DcXwChxc0iTMw": "Dispositivos Observações.", "kPLQ9bi6YQ7CGlfsj1-Xr": "Nome do grupo", "FxPry5C52XBPmrYIZYyL-": "Informações da bateria", "gBEw5BIt6PcPC9zFkw1pu": "Informação de tensão", "0RE8zR_4ssG3-vDm1dDFS": "Filiação", "G0h5Phv0L2ZYiNkW6qjOD": "Campo de extensão", "7hRo37AgY5tlz6R_c8A8E": "Mantenha o tempo do estado", "QGdPkhCM7YopsUXXGEC-s": "Última hora do sinal", "cnyEHrN3GNiGP2f0vJWse": "Último tempo de posicionamento", "G3TxUVRv5FTSlKpEn1Ql6": "Configuração de campo", "0Aqxb_GDZq_HUfv9X1rx-": "Lista de tarefas", "rDoa54vkvUnzRjC8KfzbD": "Número da tarefa.", "u5VGmAhjxmg1wptxMpiCG": "Inclui", "rt7d3vYgqgqlKgp8G0ZFl": "Não inclui", "EjLZvKQnhIOeBl97DUMEI": "Exportar campos", "b2DWOt8gsb5UtOwfsq-0t": "Status da tarefa.", "_ktpLizL1zQbR4S-2a9yS": "Arquivo", "PSkqmsVXAnXwS4IEg_PCw": "Exportar é bem sucedido", "qemv8Jr6YLWEMlEjIqd7u": "Para ser executado", "ZTusx1IoqVDMV5n8ga3Kr": "Exportar falhou", "G6xSzxmYbLAVmP3-A9tND": "Execução", "dd9U0JHJTew4zoPUYl11x": "Reexecute", "dbJgeT1ZDM7ccrHhrlq5m": "Execução", "f2qfydR_3cc6WkgIguaev": "Download", "NBv8StIMAV4Ss2MZz_cDl": "Download bem sucedido", "tEmrFBYMHpu78vsHVCymD": "Sono inteligente", "RhTgJ0vhpibaKqqZuOpiy": "Claro que você quer nomear o equipamento?", "TxRDK1wUxDUn8xNJIkXRd": "O dispositivo entrará no estado off-line se estiver parado por mais de 10 minutos, durante o qual nenhum comando entrará em vigor. Quando o dispositivo se move, ele será despertado e irá online novamente.", "8GdL1zWMJptJ8AiAol3Yk": "Status do cartão", "OT5pi5ls02Yn-qXFicKOX": "Número do cartão", "i_njN3t7VF2mJP1xxkkNN": "Informações do cartão SIM.", "J7GYiYHl6YxxMz6DV14ZI": "Função GPRS.", "nKkMpCeUmGpAEZOHFubgT": "OFF", "JH_oKJbhNVd2y3VDb6oS5": "Limiar de paralisamento", "UWZiT2UI0jtd6umy4pP83": "Alarme off-line", "1BGMlf50Sr3tsvjmhkYJL": "Após a abertura, quando a velocidade atual do dispositivo upload de dados de posicionamento <= limite estático, a plataforma determinará o status como estática", "lHr1G3dsLNsKPX2toJVeL": "Quando o dispositivo estiver offline, a plataforma envia o alarme Offline do dispositivo", "NT9rhnVfP_H1_BU9AAWXS": "O limite estático só pode ser definido de 0 a {0}/H", "ewfWEdsf3NsKPX2toJVeL": "Umidade", "ETjPg2LN1EYYtVQsyU1Si": "Tempo de expiração do cartão SIM", "zLKnijHT1o-_DOpaFEyZS": "Campos base não podem estar vazios", "4o8gSmZGcrwz70qV4Eg6q": "Alarme de mudança de nível de óleo", "vovDYOhKoxvccZkmUQPKg": "Testando do ACC.", "O_u_DNP0PxptNrO2O0dhC": "Detecção de rotação para frente e reversa", "Yhb1FXkSo-2p_oQYUpudn": "Platform Notification", "CVCBH6Y-uYJ1j-m2sYz_A": "Notificação SMS.", "erhrhxfWEGsfdgeergdfs": "Dados do mapa", "sdfghESDGdgeerREFgdfs": "Termos de Uso", "aEN_Am4XWjsh0ktkF23iA": "Comutação", "dFkTJxdatpPvSNfTqBOLk": "Por favor, crie um papel primeiro", "fwgdgafafewgrhraqrger": "Verificar Número de Monitoramento", "erfdfhhgtrhrtsrefgbhg": "Exibir o número do monitor", "ewrrertrhhfdhhfefgbhg": "Tem certeza que deseja verificar o número do monitor？", "fersdgsdfzsefrgfergrd": "Defesa Automática", "wqsdgsdgdhwrthtyfghb2": "Defesa manual", "5dibUgEIhQ_nxsvtyH-Gn": "Pista de intervalo de tempo", "HT5-DuSlKusr_PXRq3BKi": "{0} para {1}", "YjNIXBe35TxkPNmhQXjsl": "O download da tarefa foi enviado, vá para a lista de tarefas para verificar o progresso", "Bad9L51R3bTOzbZK96v4M": "<PERSON><PERSON><PERSON>", "sdgerhtnzbZKrtr4A445D": "<PERSON><PERSON><PERSON><PERSON>", "sakFjkCsiFnKnbi32nwdG": "excesso de velocidade", "adasdwasdafadq321asda": "Depois que a configuração do relógio é concluída, quando o dispositivo está marcando e saindo do trabalho, ele irá empurrar a notificação da plataforma, que pode ser visualizada nas mensagens da estação central de mensagens. Clique no nome do dispositivo para ver as horas de trabalho do dispositivo dentro do intervalo de tempo", "asdwasafwsadasda32ssa": "Depois de um longo tempo de modificação, ele substituirá o tempo de configuração anterior", "Be2562h253grgsHHJDbRDAF": "<PERSON><PERSON><PERSON> acentuada", "BtyuwyfgrWERERRTHDAsdDF": "Desaceleração Rápida", "BtyjdfghtwsrgGHFEEGRDAF": "Aceleração Rápida", "bausguOI4chuJCSUjcnwoo8": "<PERSON><PERSON><PERSON> acentuada (vezes)", "cveuiHXBbcjNCI65cvehJCI": "Aceleração rapida (vezes)", "YBGBXHcbeiHBUIbjhcwoi54": "Desaceleração rapida (vezes)", "wugyrgHVBUJncw6cf4whcxc": "O tempo ocioso não pode estar vazio", "FDhxhjhe6gv5_cekhq64cxX": "Velocidade excessiva (vezes)", "xcjw54cXHDCG3cw_xkklklc": "Configuração de velocidade excessiva", "ScefhxDXWc654CDCHcnxopc": "<PERSON><PERSON> de uma hora", "cxbuhy_cjnbxnX54VE6Vcjc": "Em 1 dia", "cxbujHJXIH5xcbujxbic45v": "Em 7 dias", "chchguiHGCEW46GFXhcij_X": "Dentro de 30 dias", "xbcuCBUWCBIz56cv6we_xni": "Dentro de 60 dias", "vniHCIHznjvoeg5fwhncicm": "Mais de 60 dias", "bcxwuGXUG_xnwfd3vhjxwio": "Começar a trabalhar", "bcwihn_xnwf5fdcdfoijqdc": "Sair do trabalho", "xujwguycx_xwf5v465cw6xa": "<PERSON><PERSON> cedo", "bncwvc543_xwjbdncmjJXjo": "Estar atrasado", "cbujBCXIHCXCVW5VBjbzhih": "Verificar em tempo", "xbucBNXB_CXNVE4V5C5C55X": "Local de check-in", "cjwcnwf52vcwb_cwekfnbxc": "Configurações de exibição", "xbujqwfdbn_ckeneg2vewrh": "A hora de início é igual à hora de fim", "xbjwgbcxufgvdd542x4chxc": "O período de tempo definido se sobrepõe, redefina", "cxbwf_cnev52cwhnicwxcqm": "Definido com sucesso", "xufvuxinbqx9cn3wr2fvg2f": "Con<PERSON>to falhou", "cbnwjedb_cxwkf6cfqhbcxq": "Jo<PERSON><PERSON>ho", "xgbuwedfcx_xqwdbq5xdqwh": "Gráfico de Análise de Óleo", "cuwhbc_xbwf52xhxxqwdsxx": "Gráfico de Análise de Temperatura", "bnciwchi8cb2891dhxc129x": "5 minuto", "xbu2iby827tg29c89x9yd9y": "3 minuto", "xhi21b4xy89y1201090u1ed": "1 minuto", "xqigd92x3t82brcvydgdiac": "Todos os status", "cbuwb2x86y129bx119bdh21": "Detalhes de mudança de tensão", "zgu2918dfgcx809rc429_cx": "O tempo de expiração do usuário de alguns dispositivos é maior que o tempo de expiração da plataforma e a modificação falha. O número do dispositivo é", "cnbN8SYBnNXcj38u3vcv9cc": "Endereço detalhado", "dxb82y12ec2ycxhn23982x9": "Dispositivo selecionado", "sasdfwsdJfioOsadOI_1A": "Selecione o tempo de expiração do usuário", "Ksdojos_sadiokvn15sSW": "O tempo de expiração do usuário de alguns dispositivos é maior que o tempo de expiração da plataforma e a modificação falha. O IMEI é:", "Osasd_ojoiasdlkj_wass": "Selecione o modelo do dispositivo", "aOuinlkvi_5sdAvOsdWOm": "IMEI não pode estar vazio", "ansiuhWjhd_asd_QdwxDw": "IMEI não existe", "oASdoioijds_sad12Adwd": "O modelo do dispositivo não pode estar vazio", "WGSFGDSgdFnKnbi32nwdG": "Quanto menor o valor, maior a precisão. Recomenda-se configurá-lo para", "1dfgnfgndgadqGRTH234S": "Observação", "2dfgnfgndgadqGRTH234S": "Pessoal", "3dfgnfgndgadqGRTH234S": "<PERSON><PERSON>", "4dfgnfgndgadqGRTH234S": "Motocicleta", "5dfgnfgndgadqGRTH234S": "<PERSON>", "6dfgnfgndgadqGRTH234S": "Caminhão", "7dfgnfgndgadqGRTH234S": "Ônibus", "8dfgnfgndgadqGRTH234S": "Caminhão misturador", "9dfgnfgndgadqGRTH234S": "Táxi", "10dfgnfgndgadqGRTH234": "Carro de polícia", "11dfgnfgndgadqGRTH234": "Máquinas agrícolas", "12dfgnfgndgadqGRTH234": "Barco", "13dfgnfgndgadqGRTH234": "<PERSON>ren", "14dfgnfgndgadqGRTH234": "<PERSON><PERSON><PERSON><PERSON>", "15dfgnfgndgadqGRTH234": "<PERSON><PERSON><PERSON><PERSON>", "werERGEDjty47thwhrw2r": "<PERSON><PERSON><PERSON>", "hrthwertehhgrjrryyuyu": "Por favor, insira os números do cartão SIM de 11-13 dígitos", "tyjwerTEHAWEERerghrt5": "Erro de formato do cartão SIM", "SAF2344asdfgre2425321": "Observação do Distribuidor", "asduiunkj_sojdAojdjio": "Recomenda-se não exceder {0} IMEI de cada vez", "lkhoasdoi_ahdjkjnihds": "Importar para", "asdioi_asdjhsjiohs4ij": "Insira um IMEI em uma linha (é recomendado ter no máximo {0} IMEI de cada vez)", "asiIojKODiidoOIjiwkiw": "Dispositivo não localizado", "rerdsbfgrygRG8retEqrg": "Semana passada", "trhhfwertdgdfgqWERs45": "<PERSON><PERSON> semana", "35sfdgwERGRGg534sbvfv": "<PERSON><PERSON><PERSON>", "hu8crhU0O5-9ObaQNtymN": "Ative a tela de status da velocidade de marcha lenta do dispositivo", "idlingDesc": "Nota: se você não quiser receber o alarme inativo, você pode fechar dentro de \"push\"", "bzVDxO5OkfMskWoNiFsov": "Regras de julgamento de velocidade de marcha lenta:", "s8x02hV2p9E13TkI1Xr0s": "(1) O status ACC está ativado;", "561Z1QlQSV9ALHXwaOA2y": "(2) No estado estático, o tempo estático excede o valor definido (1-60min);", "cnWw9L3lCRWHXyv4IW7KN": "(3) O modo de posicionamento não é 'não posicionado' e o estado de recuperação é 'estacionário' quando o veículo não está posicionado;", "86pBnkS4xC_Am4khna2WU": "Definição estacionária: a velocidade é menor ou igual a k km/h (K é o limite estacionário), geralmente o padrão é 5km/h;", "aasduii_15jdiusdiojAw": "Taxa de reabastecimento", "asdwfhjkxohuUIjudoiuw": "Zoom de área", "oiuisauih_soIasdiKdiw": "Restauração de zoom de área", "PjjiIkjh_1iojIjkdklss": "Redução", "kjhOojolKJkjuh_alk2Wd": "<PERSON>var como imagem", "asd_ihjk123_asdjsajhd": "Sem resultados correspondentes", "xgwuedhcuiwhwdhwciqiu": "O tempo ocioso deve ser um número inteiro positivo", "uiwsh872y89cb781c289s": "Parâmetro inválido", "knKLyHJUTw7crKa9duKC9": "O intervalo de tempo máximo não pode exceder um mês", "asduiijoh_jkakjsdui15": "Latitude e longitude", "9VVTIU-Eu0k8W5Q_IiKoj": "Opções", "Td5X-QbKaFt31TSs9FTV2": "modos de localização", "JBp30yKusF3Jvw8cQVZZj": "Posicionamento por satélite + LBS", "y6P82AgvLwCA3gRisElPs": "Posicionamento por satélite + LBS + WIFI", "ewr2334qRTEWrg34534t5": "<PERSON><PERSON>", "ewrasf52334qRTEW425321": "Definir / Remover", "asdiOIosdj_564Hjoijid": "Adicionar clientes subordinados", "hk-j6yAbxH519LUQTapg5": "Preencha mais informações (opcional)", "2kiLagD1hB6MjdguFOHeh": "Pesquisa histórica", "znIORRJA2sCWxEuuJGAf9": "Sem histó<PERSON>o de pesquisa", "nEXvu2BRLQvSN2q4zNh16": "Copiar com sucesso", "PDD2x6KmulIA28JPPiWmf": "<PERSON><PERSON><PERSON><PERSON>", "T2Sz03hEpYKf8z_mXsC9e": "Compartilhamento bem-sucedido", "wUttprQVTKne9Pjj_eas4": "Link", "Todi_jvRP_qH1l9la6K-I": "Seus amigos podem verificar sua localização em tempo real através deste link", "i_FwdBnrmb7MzNMyx9Md2": "Método de detecção", "7Lg2fzoMMZD9T8t0xpX_P": "Capacidade", "nyTzyI4JkbFylgZL_8s6v": "Tipo ultrassônico", "qPY1Oqb4GFE6p3lvhoJef": "China", "rvZdqRnVvEGgrInYrcao_": "Capital de Equipamento", "98zGSk76EGdJ2iIIoKbxe": "Total", "dfsf253663wreqQTRH223": "Por favor, insira o raio de deslocamento correto", "iCfNkRWDcS38BMFKnNEe6": "O intervalo de tempo de exportação não pode exceder 7 dias de antecedência", "sdfWET34N6n345knk2tt2": "<PERSON><PERSON>", "wetdsfgT45626n345knk2": "Intervalo de passos", "WE65fgT45626n345knsdf": "Por favor, insira o intervalo de intervalo correto!", "dsSFGSDFh753A6n354Y45": "Neste modo, o dispositivo atualizará os dados de posicionamento de acordo com o intervalo de passo definido.", "weafaE354SF234AFasdfw": "Alerta de fadiga ao conduzir", "sdf2345afaHHF234AFasf": "(1) <PERSON><PERSON><PERSON> ligar, quando o dispositivo ACC estiver continuamente ligado, a plataforma continuará excedendo o valor de alarme e alarme", "safw36g346347DFGdfg34": "(2) <PERSON><PERSON> a<PERSON>r, confirme se o dispositivo está conectado à linha ACC e se a fiação está correta", "rzPd23XcIb6rhIjDQxfQI": "Clique para arrastar e soltar grupos para classificar", "nle_9j8akFLmPCwbA6Tvt": "Arrastar e soltar é proibido no estado de edição", "JnbwpzQ1es7Knnztzs8BF": "Atualização falhou", "jCngDisNEtHDk2AbrnGT0": "Falha na transferência", "_pPqRg2tUY4Ks1XYYQ22Y": "Confirme se o lote de dispositivos está na mesma conta", "gxywuyxyudeytgqdytgxq": "Expira em 7 dias", "xbgwdygfcwygxqgbcdgyc": "Expira em 30 dias", "xh27t6gd2ftx281xg1yd1": "Os dados de status do dispositivo desatualizados são atualizados a cada hora", "xg72stb27826td26d1276": "<PERSON><PERSON><PERSON>", "asfwe87345a99T34g36kd": "Descrição da cor de rastreamento", "xg27td76gsdgb198ys1y8": "Sua senha é muito simples, é recomendável alterar a senha!", "xh278dgtcfg7xchgx1h8x": "adicionado com sucesso", "EG4bzTy443aMfL9yHir_p": "Condições de recolhimento", "XYgitqAwgNUZ_WMaw417U": "<PERSON><PERSON> condi<PERSON>", "hjYHD87gdSZHBXCH8FJ9H": "<PERSON><PERSON><PERSON> se<PERSON>a", "cbuuwxgh7TX67XFGGCHCH": "Esque<PERSON>u a senha?", "HX8UYguGBXCUYGWYGFHYU": "Experiência", "EsLrJ7laPcMXBxHwm2UoS": "Sincronizar o tempo de modificação do usuário?", "xhq87s8s1ds1hdh1dhddh": "Regiões Administrativas", "GSAGuew823hdhdusuxjyy": "Tipo de cerca", "hd823d5ddchf28H8xhd89": "Estatísticas da cerca de entrada e saída", "dghd72f22ffdhdydyud2h": "Número de veículos que entram na cerca", "cbchd72hfcbchjxzjzjjj": "Número de veículos fora da cerca", "mvndjdbxjdjdhchd8dhjd": "Duração média da estadia", "Bhxchjsd83947fhdhsjjm": "Detalhes da cerca de entrada e saída", "cn8c287y2gdhgdhxhxhhh": "Nome/IMEI", "vnNDHhchc87yffggvh28f": "Fique >=", "MXDNihc837yhv8vhjcjjl": "Cliente Atribuível", "fn3uyaBGUGYBCX87bxhui": "Hora de entrada", "vmveioujf89c_xhje8hjm": "Tempo fora da cerca", "vmbmbiu4hbHCX8ycbchjv": "Duração da estadia", "MXCghc87f2hgvhjdsjsus": "Ver faixa", "cnc8c2hgJHXHihxuif389": "Detalhes do alarme da cerca", "sgeert4645FASRG23XFGg": "O tempo estático deve ser um número inteiro positivo", "eq9r0DyDWJue8aasqqeSH": "<PERSON><PERSON><PERSON> <PERSON>ua<PERSON>", "asdf45DFfg325sdg5FAS1": "A resposta do comando expirou, o sistema reenviará o comando mais tarde", "afeSR5SDG4g45gs23SGWS": "O comando foi executado com sucesso", "sdfwgrt457226EWT435fh": "Você pode modificar as informações do dispositivo carregando um formulário. O arquivo de formulário deve ser inserido no formato de cabeçalho de arquivo de modelo.", "WETNK235asgkn34Edkjgn": "Os seguintes campos estão disponíveis para modificação de importação:", "gfh3345sdg3425SDG2864": "1: Número do equipamento (IMEI) - obrigatório, números puros de 15 dígitos", "sg36SER676sd2YR25745F": "2: número do cartão SIM - opcional, 4-20 d<PERSON><PERSON><PERSON>, letras", "58FFRTJhrh565RTTRJ353": "3: Número da placa - opcional", "YFJ45345fhf5674RST345": "4: Nome do dispositivo - opcional", "dsfg64YRrtjty56645asR": "5: Notas do revendedor - opcional, visível apenas para os revendedores", "fhj25ERT765RT3453rgrt": "6: Contato - opcional", "as357832WERU2458sgery": "(xls、xlsx、csv，não mais que 5M)", "erg346RY455Yt33462rgg": "Parte das informações foi modificada com sucesso e a lista de falhas é a seguinte:", "dXYpGu45Rm1EWLEqXyc1N": "plataforma aberta", "g2345sdfg6t3300462rgg": "Eletricidade do combustível", "Yhr9Y0kuM8ANBfaRN3_NQ": "Julgamento de Estacionamento", "kM8fJsLkgzgRDpq2T_SUx": "Rota", "4PkVpEbTVm-b0kGGM-f4S": "Tempo de condução", "6TcxQMYvgXAQ_JSBxXWMD": "Itens de Dispositivos Suportados", "6TcxQMYvgXAQ_JSBxXWa1": "Distância de deslocamento", "6TcxQMYvgXAQ_JSBxXWa2": "<PERSON><PERSON>", "6TcxQMYvgXAQ_JSBxXWa3": "Alarme único", "6TcxQMYvgXAQ_JSBxXWa4": "Intervalo {0} <PERSON><PERSON><PERSON>", "6TcxQMYvgXAQ_JSBxXWa5": "Por favor, selecione a linha de alarme deve ser deletada", "6TcxQMYvgXAQ_JSBxXWa6": "Status do Alarme", "6TcxQMYvgXAQ_JSBxXWa7": "Limite de velocidade da linha", "6TcxQMYvgXAQ_JSBxXWa8": "Por favor, selecione uma linha", "6TcxQMYvgXAQ_JSBxXWa9": "Por favor, selecione a hora", "6TcxQMYvgXAQ_JSBxXWb1": "Selecione a distância de deslocamento", "6TcxQMYvgXAQ_JSBxXWb3": "Por favor, selecione a forma de alarme", "6TcxQMYvgXAQ_JSBxXWb4": "Por favor, insira o limite de alarme de excesso de velocidade", "6TcxQMYvgXAQ_JSBxXWb5": "Alarme apenas uma vez", "oMUEw9fns5Q_1nPpGRnbu": "Estatísticas de Linha", "luet6c7PKCXPFY01x20CW": "Por favor, selecione os tipos de alarme", "luet6c7PKCXPFY01x2001": "Alarme de desvio de linha", "luet6c7PKCXPFY01x2002": "Alarme de excesso de velocidade de linha", "42iJdaFze5K4luDvSkt2k": "Pertence ao usuário", "42iJdaFze5K4luDvSkt21": "Localização inicial", "42iJdaFze5K4luDvSkt22": "Local de término", "wegasqASDE32446sgwe01": "<PERSON><PERSON>", "wegasqASDE32446sgwe02": "Ferramentas", "wegasqASDE32446sgwe03": "Verificação de quadro", "wegasqASDE32446sgwe04": "Verificação Regional do Carro", "wegasqASDE32446sgwe05": "Navegação", "wegasqASDE32446sgwe06": "Carro Regional", "wegasqASDE32446sgwe07": "Gerenciamento de linha", "wegasqASDE32446sgwe08": "Por favor, digite um nome de linha", "wegasqASDE32446sgwe09": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "wegasqASDE32446sgwe10": "A linha foi configurada para alarme e a configuração de alarme desta linha será excluída de forma síncrona quando for excluída. Quer continuar a excluí-la?", "wegasqASDE32446sgwe11": "Configurações de linha", "wegasqASDE32446sgwe12": "Configurações de alarme de linha", "wegasqASDE32446sgwe13": "Pontil<PERSON><PERSON>", "wegasqASDE32446sgwe14": "Desenho de trajetória", "wegasqASDE32446sgwe15": "Desenho de Navegação", "wegasqASDE32446sgwe16": "Clique no mapa para marcar a rota (clique para confirmar a localização, clique duas vezes para finalizar)", "wegasqASDE32446sgwe17": "Número próprio", "wegasqASDE32446sgwe18": "<PERSON><PERSON><PERSON> da linha", "wegasqASDE32446sgwe19": "<PERSON><PERSON> da rota", "wegasqASDE32446sgwe20": "Ponto de partida", "wegasqASDE32446sgwe21": "Fim", "wegasqASDE32446sgwe22": "<PERSON><PERSON><PERSON>", "wegasqASDE32446sgwe23": "Reselecionar", "wegasqASDE32446sgwe24": "Escolha uma rota de dispositivo > >", "wegasqASDE32446sgwe25": "Escolha uma rota de dispositivo", "wegasqASDE32446sgwe26": "O intervalo de tempo máximo não pode exceder {0} dias", "wegasqASDE32446sgwe27": "Linha de Navegação", "wegasqASDE32446sgwe28": "Clique para marcar o ponto inicial no mapa", "wegasqASDE32446sgwe29": "Marque o ponto final após clicar no mapa", "wegasqASDE32446sgwe30": "Adicionar ponto de passagem", "wegasqASDE32446sgwe31": "Estratégia", "wegasqASDE32446sgwe32": "Rota mais rápida", "wegasqASDE32446sgwe33": "Rota mais curta", "wegasqASDE32446sgwe34": "Evitar alta velocidade", "wegasqASDE32446sgwe35": "Geralmente", "wegasqASDE32446sgwe36": "<PERSON><PERSON><PERSON>", "wegasqASDE32446sgwe37": "Resultados da navegação", "wegasqASDE32446sgwe38": "Pontos de passagem", "wegasqASDE32446sgwe39": "Total de milhagem", "wegasqASDE32446sgwe40": "Por favor, marque o ponto inicial ou final", "wegasqASDE32446sgwe41": "<PERSON><PERSON> linha", "wegasqASDE32446sgwe42": "Clique e arraste a área selecionada", "UVefl_f1h0ZTPQmWvaySJ": "<PERSON>gun<PERSON> se deve acionar o alarme de linha apenas durante este tempo", "pdMyZPIg2I7k3u3ByYLrU": "Quando a posição do veículo se desviar da faixa efetiva da linha fixa definida, um alarme de linha de desvio será gerado", "HhZrDdgis2Hm5-sP9EurB": "<PERSON>me da linha", "-YUdN9jL-AktN-FaK-AY6": "Itens de Dispositivos Suportados", "t47OvSwHgN1jrekCjlFgG": "Protocolo", "L0DK_-dPWIgIwVk29nx2U": "Localização", "OETskCr0ggPlXNLTOD8cX": "Entre em contato com o vendedor para alterar a senha", "cqY_Nktaga5MCpKzhZlzJ": "<PERSON><PERSON><PERSON>", "GCZMAUxDiNQjGiQlcWsgx": "Imagens", "HbdiV2RLW3mpJDkZ_KYFG": "Nº do item.", "P6hV_PyvDRj6HaDyYdaU0": "Gerenciamento de modelos", "2S0dsK4yqAlGMmJh4k9PL": "Não pode estar vazio", "WnpPKREGrpW2LdDnNXIzi": "Arquivo muito grande", "B7o8-BkLXrQP-C2VC1vRu": "Carregado com sucesso", "PzBG2cs4d9h1G-yy4y-JY": "Falha no upload", "6e8OYOZeRaCVXbbzz93ij": "Exploração madeireira...", "oo6OKdBR0WdWtwR9ww0BI": "Localize instantaneamente a plataforma global de serviços de localização", "J_O90-QzEYhF0wwpP_1OG": "Downloads do cliente", "0no1-wPECvW4oaBpA3PUH": "Financiamento de automóveis", "gMBUxLUUq5HkqYp-IZMMw": "O nome de usuário não pode estar vazio", "SlzCfc3ZLA2qtLCdh9rQ0": "A senha não pode ficar em branco", "LoUsU3fXwcYRpNz6nk2Mb": "<PERSON><PERSON><PERSON><PERSON> ou senha incorretos", "EvTOrBlsYbI6MLRmnjEby": "<PERSON><PERSON><PERSON> simplificado", "Q_JRX2AWfBfpvq0i7WSSf": "Plataforma Básica", "sfwA5_FWQER4kGGM23f01": "Quantidade Total", "sfwA5_FWQER4kGGM23f02": "Dispositivo Suportado", "sfwA5_FWQER4kGGM23f03": "Se<PERSON>", "sfwA5_FWQER4kGGM23f04": "Ativação acumulada nos últimos {0} dias", "sfwA5_FWQER4kGGM23f05": "Ativação acumulada nos últimos anos", "sfwA5_FWQER4kGGM23f06": "Um máximo de {0} pode ser selecionado", "sfwA5_FWQER4kGGM23f07": "Estatísticas do dispositivo", "sfwA5_FWQER4kGGM23f08": "Outono", "sfwA5_FWQER4kGGM23f09": "Análise do dispositivo", "sfwA5_FWQER4kGGM23f10": "Ativar", "sfwA5_FWQER4kGGM23f11": "Estatísticas do modelo", "sfwA5_FWQER4kGGM23f12": "Estatísticas de alarme", "sfwA5_FWQER4kGGM23f13": "Conjunto de campo", "sfwA5_FWQER4kGGM23f14": "Estatísticas do cliente", "sfwA5_FWQER4kGGM23f15": "Quantidade de crescimento", "sfwA5_FWQER4kGGM23f16": "Vivência", "sfwA5_FWQER4kGGM23f17": "Quantidade total de Clientes Subordinados (excluindo usuários logados com IMEI)", "sfwA5_FWQER4kGGM23f18": "Atividade mensal, quantidade de clientes que efetuaram login neste mês (desduplicação, não inclui login de usuários com IMEI)", "sfwA5_FWQER4kGGM23f19": "<PERSON><PERSON><PERSON>", "sfwA5_FWQER4kGGM23f20": "<PERSON><PERSON>", "sfwA5_FWQER4kGGM23f21": "Unidade: dez mil", "6CM5WFkktUM9AyRxu4XAm": "A expiração do serviço inclui a expiração da plataforma e a expiração do usuário", "HqKkLvYhwnihOKgZ5D5gq": "Serviço expirado", "Ja6QviJTdBDcOxl2ngWXG": "Consulta em lote", "_HKPHYTEishptFq7vFBk1": "Campo", "iLDn-CSjPBQCrfCiJcKgp": "Insira uma linha de dados", "VLNvCttQNLcWxHSN-fPOl": "Uma única consulta não excede 1.000 dados", "k7qAMHi43Xjw2PYIQ38hq": "O intervalo de tempo máximo não pode exceder uma semana", "jaGWdGPfszdjZvW57NUiJ": "O intervalo de tempo que você digitou não pode exceder 7 dias", "pvqy81ufHUkt_cJ8QuDoR": "Confirmar <PERSON>", "453T_t2i6SJoDBBAy_R6B": "Voltar ao login", "dJBhX5e3-fFcp5QbouP5w": "Esta conta não está vinculada a um endereço de e-mail, portanto, a verificação de e-mail não pode ser realizada!", "jDSJ8u2HnhtninUCX6vMB": "A senha foi atualizada", "qsvd2Se3tlv5Dsb9Hya01": "<PERSON><PERSON><PERSON><PERSON>", "qsvd2Se3tlv5Dsb9Hya02": "<PERSON><PERSON><PERSON><PERSON>", "QBfoK7OejhnKA31m5cUb_": "Dispositivo alvo", "dBc3sWmATHCVdJB5RtzRA": "IMEI/nome do veículo", "asdfwemATHCVdJB5Rtz01": "Somente suporta transferência do cartão de ponto da conta atualmente logada para o cliente alvo", "asdfwemATHCVdJB5Rtz02": "Gerar recentemente cartões de pontos para clientes-alvo", "asdfwemATHCVdJB5Rtz03": "Reciclagem de cartões de pontos do cliente alvo", "asdfwemATHCVdJB5Rtz04": "O saldo do cartão da conta atualmente logada", "swefqrerggARxHSNAfP01": "Posicionando intervalo de upload", "swefqrerggARxHSNAfP02": "Intervalo de upload de data", "swefqrerggARxHSNAfP03": "Intervalo de batimentos cardíacos", "swefqrerggARxHSNAfP04": "Limite de tempo de condução por fadiga", "asdWdGPfszdjZvWs765sg": "Posicionamento por satélite+WIFI", "uAtFq7CLH25vyR_9qCWOx": "Análise de ativos", "owditLvP75QFCxudedrBl": "Insira a duração off-line", "OZTi4YTKLZz8u6-YnTnG8": "A consulta de tempo off-line não pode exceder 1.000 dias", "PopB3vD--FyqFAitPbG-9": "<PERSON>uando fechado, a velocidade atual do dispositivo nos dados de posicionamento carregados <= {0}/h, a plataforma determinará o estado como estacionário", "iwdChV3IahUyambbg9AFn": "Quando ativado, a velocidade atual do dispositivo nos dados de posicionamento carregados <= o limite estático, a plataforma determinará que o estado é estático", "u7_yN4XM3aiplwBPPyKQJ": "Ativar alarme de vibração", "HH3gLagydJHVlK2reit8q": "Desativar alarme de vibração", "PxYMk1w1YywfN127GyF_L": "condição do carro", "avn4Z2zY-8AmiFArM_Kzi": "Alcance", "1FnxxFoT2tdJpyAUbkQ8d": "Engrenagem", "vZ_NmsWGw1TljsTvgtYSc": "Status de carregamento", "gmiIB_fgQPFeHSPgUZ1OX": "Ineficaz", "sjqasBHO4-7v_oE_oQhzf": "<PERSON>m carre<PERSON>nto", "rr1RNSaQZHRkg3YmwbXN3": "Carregamento CA em andamento", "3NqztzkemfaYubkgd94tg": "Carregamento CC em andamento", "gW9Sr4E4DO3vcWEXwSANp": "Carregamento concluído", "C3mHbV6ctjTFoPxWohWcP": "Solte o pedal do freio", "eW5vpowqQzG4FDKqffwhd": "Pressione o pedal do freio", "Y8SpiwQuS5O9oxoBoy2kL": "Solte o freio de mão", "E3uUXB0kIhb1zEAcQGLUI": "Puxe o freio de mão", "RDwtFG4WvXrnr1gJn4FEf": "<PERSON><PERSON> de obter", "jAwo7Dt5bMPQddJLedM5N": "VIN", "xWvddkQeFsyOfJIBomA-W": "Potência do veículo", "NmYX0e6R4ef5q9at8zVBm": "Tensão total da bateria", "bgQGnhJJGfoSv7R2bHoep": "Carregar e descarregar energia", "IFI8p5520lb5gVh9NKX4U": "Velocidade do motor", "QVF1XS6JNRbW6dAeOGSpE": "Temperatura do motor", "_HAw3HjMpxf_5Li2ijxzG": "Status do freio de pé", "8BSkuJhUJVmpC-FyYv_tP": "Handbrake stu=atus", "dPrshwoE0R9HYtR_pqiyB": "Engrenagem", "GFHU5hpHTATv03VLlUraj": "Informações sobre a condição do veículo", "asdf2353WEEEW34463E01": "Nenhuma operação por muito tempo, foi fechado automaticamente!", "asdf2353WEEEW34463E03": "Centro de captura", "asdf2353WEEEW34463E04": "Vídeo ao vivo", "asdf2353WEEEW34463E05": "Não há vídeo para reproduzir, selecione o dispositivo primeiro.", "asdf2353WEEEW34463E06": "O dispositivo não está respondendo, verifique se o dispositivo está online.", "asdf2353WEEEW34463E07": "Dispositivo desconectado, reconecte.", "asdf2353WEEEW34463E08": "Aguardando o tempo limite da conexão do dispositivo, remova a fila de espera", "asdf2353WEEEW34463E09": "Cartão TF", "asdf2353WEEEW34463E10": "Não é possível gravar normalmente, sugira a formatação do cartão TF e tente novamente.", "asdf2353WEEEW34463E11": "Falha ao contatar.", "asdf2353WEEEW34463E12": "<PERSON><PERSON> j<PERSON> {0} minutos, pare de jogar automaticamente.", "KqFWT6QtVOAQ7hc3cr84X": "Selecione um dispositivo ou procure por um", "QK_2GJFOQAXQpT2t--zKK": "Nome da imagem", "yC-UA-x1iWn-_8MWhMRUJ": "Pré-visualização", "UTh6HJ0cZkBaGYhmxiqzi": "Por favor, selecione uma data.", "hDsjEhjGMV2q1OhZLj51s": "Com vídeo", "BVvJFDI4tb81rTca8ggnz": "Sem vídeo", "nuxEh7ICQm1xi9uibTsEn": "Seu navegador não suporta canvas, atualize seu navegador", "n4CXnjUAtGwof4KxtLIs7": "Capturar", "rbxQKc5qyK61DZUKq4R2J": "As imagens de captura adquiridas podem ser visualizadas nas 'Estatísticas de captura'", "G4YqkP4TlCeqrV4kaShwj": "Captura remota", "DF5rkRH7xPSGfwVqhIZpx": "<PERSON>r <PERSON>", "CKBlMeR7SBptjZx9In-Di": "O dispositivo está offline, tente novamente mais tarde", "9Gz9xO-UIA7OhYqouSYlk": "Dispositivo não ativado.", "bREKx_fI1e13-UqxZSRal": "O dispositivo está offline.", "xb_MxCHFkbfiDQK87uTTx": "O dispositivo não está respondendo, tente novamente mais tarde.", "K0DCR9FMFQUUR12RWd7nJ": "A consulta foi bem-sucedida, selecione o tempo de reprodução.", "Ai9BqK4KDDGgpUlIM0L3Z": "Nenhum vídeo reproduzido nesta data.", "15zeVzw38Aza0pV2JpH8a": "Execução bem-sucedida, as fotos podem ser visualizadas no 'Centro de captura'.", "2UV-WqfP4CJZmmLPAGuny": "A reprodução termina, o dispositivo é desconectado.", "R4xVH6Gojtza6L_bDXWmZ": "Tempo limite de recuperação de vídeo, tente novamente mais tarde", "RYTm5aEvo7HeIs2bpdEIN": "Tela cheia", "o9T1vRXeXagCVmJ2L7HXh": "<PERSON>r da tela cheia", "ixY_KDMtIVUvWecVMQTWg": "Volume", "P0UwTpyNrAmeEFn-ZtpL8": "Slience", "0_iZ3QPJnrJ7wWOLfcFt4": "<PERSON><PERSON>", "xN4IbwPtNSUXm1NMMrCJr": "Pausa", "gDT1meCgWso2DTOwunb0f": "O dispositivo está offline", "8QHejHRn_K6ApxX1HI_x2": "Selecione ou insira um nome de cliente", "sEc-d5mjOt11-Bl13FHEi": "Selecione ou insira um nome de dispositivo", "hoaQubz4oNzQmOYis2iw1": "Defina a data de reprodução com base no fuso horário onde o dispositivo está localizado para evitar que a data de reprodução seja inconsistente com a data de consulta. (Pode enviar comandos para determinar o fuso horário do dispositivo.)", "-LMYZ7kqXRKWwUyYD7GOF": "Captura automática", "l4A6SV00MzaTtBzrPfqK9": "O intervalo da foto suporta apenas {0}-{1} minutos", "7x1-CyvyQU47Ks-9Mscic": "Insira o intervalo da foto", "FlBzcCTYSxWGdVm3pcxQP": "Relativamente baixo", "wyDuKJCKh-KmMMTnY_bBB": "Não. {0}", "94nNNXBShqMfNNc3dNGHx": "Data Limite", "xAyYnNoSfX2pbPVT9h1pC": "Intervalo da foto", "8-ai5B94OPd5_wwtjQyZu": "Executar foto automática somente quando o ACC estiver ativado", "IXsWeOfIA5805yY47UJGz": "Qualidade da imagem", "wf2tZAJzkmSHhwNlwULgH": "Câmera", "_Vr87IrKGdP_Z0Ir0boqS": "Por favor, insira a data da foto", "d_0qVpLU434hyMGjCIT_p": "Tempo limite", "IYUaUWFilxVZo9ESIhEy7": "Cancelar modo de captura automática do dispositivo", "r2u1VZH0-LuLyUjjtQp20": "Cancelar captura automática", "alzrqMaGjzgsumqGYvejz": "Estatísticas de captura", "F7bv-5zI8V1rO9ndXVOoV": "Tempo de pagamento", "uOMU5cwmKPl8Qugv8cZpi": "Provedor de serviços do dispositivo", "LvOf_dbnglLtOVFdtEwov": "Status do pedido", "lYoEvNPHjrc6x3Ct4fh1n": "Forma de pagamento", "qzwEjrvjWERzbbwcvI5rK": "Tipo de embalagem", "SY_o_6OPrbX3-RA03j02e": "<PERSON><PERSON><PERSON><PERSON>", "AnLVb2XOdBa6NjOeYULb7": "Pacote de gravação", "WZK3OsRZ4KO9I6X8u1mLu": "Wechat", "Xl9OJ9sjrHo_ukN8urbT6": "Alipay", "HSHNrVZVhYcCKzEXB2O_A": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "O3QtNyUozWoUEwHSnRrzJ": "A pagar", "ufxwjQ0al34EVQsfU2cQz": "Cancelado", "9IGNKCfQfiGSnZ0ojOGrl": "Número do pedido", "a9z-zLegU7yCzTuKkNtOC": "Número da ordem de pagamento", "wyF5AfveJkdrdwRnGU330": "Número do pedido", "uRcRfIURR21OtZb2XaJIz": "Hora de criação do pedido", "uigvo2391weT6pPqyfxYg": "Nome do pacote", "L8rvTj6RbNqMjBCUHrP4w": "Valor", "Vho8Z0XiLBfL9q77Vbq7X": "Número de série", "LTht5hSzFVGNGPsD-aWdO": "Detalhes do pacote", "XuMA6V1Wxn2UZv7ETqtnL": "Serviço de valor agregado", "66mGM-OMMfoNzSaI85F5O": "Serviço", "UOIe7iu7who6AF5Ea9nuE": "Tipo de embalagem", "QIJcJzpIK9rdXE4EIl86s": "Hora personalizada", "FEbdGHU9DzOupQt0oPGxr": "Dispositivo comum", "vCx8UWyPG3dwdwKY7yN7I": "Pacote básico", "yFydQode-qpYNF6prCBa8": "Pacote pago", "PxiKJqRk2NZgR5rrcfbxV": "Informações do pedido", "tnVXZ-wvzIO2qu-BR6Raa": "Valor total", "xhBOcDdGN68_xdqLGD_AK": "Número do pedido", "w6f4z-ArVpDgonjSek29T": "Tempo de pagamento", "ZolzOIth0AcW8J0-AYx8m": "Forma de pagamento", "i9LCQ9tWnpPpxkzaSPAkE": "Valor do pedido", "amountReceivable": "Montante a receber", "jq-noYxqRQlR1lpP_fKaN": "Valor do pagamento", "BKMFu69paMH0rP0zUnz5x": "Pacote de renovação", "FBmAMXGiheGz_xtB50egU": "Validade do pacote", "fifkznhs7I8Fg-SaLDuGt": "<PERSON><PERSON><PERSON><PERSON>", "VFMYa1yNoRQsy2FXnykHa": "Informações do pacote", "Fop3dk-nX2QIqUnQMVv0G": "Tipo de embalagem", "UzzQQv1w_yEq7V-Ar-RNi": "<PERSON><PERSON><PERSON>", "ocMzF-bOfCsBqkM2MeBHt": "Serviço de gravação anti-roubo", "9Z_0oEj77LZJQDoY7KtCa": "Serviço de rastreamento", "jSK9Ol1Oa90s46x2ts1w_": "Tempo de posicionamento (intervalo de upload)", "ryqVCbe_EynBQLBPkT6ug": "Serviço de cerca elétrica", "W7OC5YBgi9SJwShHGe-eO": "Serviço de estatísticas", "ZlYns25c8Tujl-vP1nuEn": "Serviço de alarme de plataforma", "rijikLyLIhPpZYsAQZHpb": "<PERSON><PERSON><PERSON>", "883ApjJAhE8Rjotd5jXrT": "ID do pacote", "jyVu4QMd46VDBzTPBAuAT": "Categoria do pacote", "84HZrvlRdBMYzNbvpub__": "Nível do pacote", "dUxii2qTpMb8i9Lfp0kEo": "Parâmetros de conteúdo do pacote", "8NNHQ_xsjOjWx2o4u9cKW": "Preço do pacote", "hGZVKjFNvq1TAvLwxkAgq": "Mês do pacote", "_CXEjvnybYTxiZPXdJknx": "Status do pacote", "cCQXQbp5AVce12lU6a937": "Conteúdo do pacote", "nsqSz47S_fmKY2einyuul": "Tipo de serviço", "uRDsAdnGtIGFDejyo0j0w": "Hierárquico", "QGJEx6AV2WGlBkvg2VPIG": "Tipo de sobreposição", "z7PZM_aLZkhX-rRa1FZ0l": "<PERSON><PERSON><PERSON>", "KoVO0P4i-qT6lUsN12fFK": "Edição para membros", "ZSaRleSUdarb4RX1awLBT": "Edição Platina", "sQ6JBdba8e6qBU57-Ik_h": "Permanente", "UGsdURGj2-A8w_d-tIoPp": "Para pagar", "k2Xz_evCamP4Y6Am33-Xj": "Ativado não pago", "Z4hoIjz3ox3rSFufsljh3": "Período de experiência", "2We31Z8Jl4hCBRM_pd5BT": "A experiência expira", "yrpPHud6Y6vUqEW1LzSUh": "<PERSON><PERSON>", "mgwsOBjaQLIs7QogPPWZP": "Serviço de pacote de assinatura", "mp6yVv2_LUo1Lr6d2St01": "<PERSON><PERSON>", "_ik2yJTjlR7Rdqa0Bwweq": "Gravação antifurto ({0} minutos)", "M1I3R0AnDHCWu1mZ1OsVP": "Rastrear (rastrear em {0} dias)", "Nw2KKVqus1cw69F_aMpji": "Tempo de posicionamento ({0} segundos - posicionamento)", "l7rj88qG_-sQlNjsAGFZ_": "Edição Normal", "uiY_pCNl4iy2jsY8bSHQv": "Edição Ouro", "WkEFol678sA7CJ17KAlFb": "Serviços básicos", "3nuuynf0P_SrOx0JqRKPW": "Pesquisa em lote", "70ERCDxsXgDD3RuW2XD6q": "Tempo de pulsação", "D4fkMsn4E25Wnmp2AAm_l": "Não detectado", "OD6TR38aI88Bu7uB8uTwq": "Falha na detecção", "BHbhSwWp8seqYBF5anqUk": "Detecção aprovada", "tT2sRSRlwvHp-8CBIm7bt": "Tempo GPS", "N0OhAZYIH0g9tcRa8qeD4": "Insira o IMEI de 15 dígitos", "W9AT_wWgNGQiHIiUa0xzP": "<PERSON>ó suporta entrada {0}-{1}.", "sa8NRVeTVfugiuRw61wuE": "Hora de acordar", "M_TMom29kkT90sW_GtNrB": "Tempo de rastreamento", "AKhshdLe4CcX5kgQ6wQbA": "Modo de rastreamento de tempo", "BLlJDrVJyhhSzQrSOxyh5": "Storia", "3xEp9e-_z1918x8ohGTow": "Selecionar", "sAgQ82QEkN8XhiaQYF-FX": "para marcar o ponto inicial no mapa,", "x5p9J9L802EOSHMvXgfyi": "para marcar o ponto final no mapa.", "NC0GvIUNGMIckA487qi_W": "Sal<PERSON> da conta", "xsV0NNfyQJJhMNnQpfXJr": "Detalhes da conta contábil", "OQQDG7tWbK4xJ_SxAn7mM": "Gerenciamento do provedor de serviços", "-qO1k-kyMJ78r-k7hvTFh": "NÃO.", "RugUrOHMBSp8lZCLkCZOT": "Por favor, digite o número do razão", "tf9xHu3qD81AQzCR4i2Bh": "Valor das receitas e despesas", "f8gnN4SjblDXQUgvZtnei": "Por favor, digite o número IMEI do dispositivo", "lcNVANxOy5jOngZ8n6BSU": "Número do razão", "6zhGiJhw5vrsMXx3jSew5": "Número de recarga", "y8ZOkzHwx2mVGslRNjFSo": "Recibos reais", "K5b1Or2vbenyxUOLm6DzC": "Novos prestadores de serviços", "xgzOVj5OyHFE7FtwzEISW": "Divisão do provedor de serviços primeiro", "vyx_f6IfvRThEn7vSa7uh": "Nível do provedor de serviços", "UBGf6jO1VlyC5cZG6G_Vv": "<PERSON><PERSON>or de serviços pai", "KgSLgX2QXc5P4ebGJhoCr": "Taxa de divisão de faturamento", "5TuLCfhzC3c4Fhk9bFXFe": "Registro cumulativo", "jG2Um06j_7e9GgAO6u6x6": "Sal<PERSON> da conta", "S3i2lNWXrpS54EiZ_zVr5": "Valor do razão", "fkWmt4XVNoCEOqcqM53Vu": "Recarga total", "llSrRUSNQCGG3Tzw4CSoM": "Por favor, escolha o provedor de serviços", "JNNF3uGWW4u7fYgJtmHcL": "<PERSON><PERSON><PERSON><PERSON> de serviços", "-bWCf0xmzSKqssy9O6C-8": "Descrição: Taxa atribuível atual {0}%, provedor de serviços: {1}%.", "cQCrS5O1FtY3f7CeXf9Ul": "O valor do pedido após deduzir a taxa de manuseio.", "XXlgxn8xHhDCvDo0nhRaI": "Por favor, selecione um usuário revendedor", "SnUXBvZwZ_rci24Ivvgrf": "Gravação HD", "rYEVE8FCA2SXW955yf98B": "Gravação anti-roubo", "QNLyVdYTL-D09KKJszyHr": "Pacote para membros", "k7tmRHnc8k29NMS7xFv1O": "Deixando o combustível e energia cortados", "GYlfTrb-v_i175rZ_TsnY": "Isenção de responsabilidade", "nlPyvAgAv_1BNyr-98ope": "O corte de combustível e energia pode levar a acidentes, use a função razoavelmente em cenas que atendam às normas de segurança! <PERSON><PERSON> as consequências são suportadas pelo usuário!", "YELMd4qb9ZvpmW11-5PX1": "Item de configuração", "RsEwptNDs5Yk1gsTLvob2": "Se deve incluir subordinados", "Z_54Oo1YxTWx_x4DiIaBC": "<PERSON><PERSON><PERSON><PERSON> hora", "dDAKOU8dHan4D9HlS0Qwu": "Fora da cerca, alimentação e combustível desligados", "mL45YNy1DNVrfOdSVUscD": "Configuração", "9m4QL0-j7BiH2hLI--u94": "Concordo", "vSvvdtZaIL_oUd7hfGj-b": "Modificação do pacote", "UCE4NbQTRf2L6QFdVr56w": "Importação de dispositivo", "ULguhh46biHAPnWL1-7As": "Modificar registro", "ZeQ1BBuCptskLmr-Jj8QS": "Pacote de sobreposição de gravação", "hHj3x3eBVUf2fJQt7aHYK": "Entrada manual", "PSBfdFXVla4Vk5sigF3Ct": "<PERSON><PERSON>", "urtoz-lPdO_PHQIlaz5Zs": "Clique em carregar", "1926HGNuX84IOWFR5BjL5": "Apenas arquivo XLS、XLSX、CSV pode ser carregado，e não mais que 3 MB.", "C1iN37-gSdpTsqWkrHJ9e": "Importar conta", "A_9Rad7zVPj2GBmUGpEVZ": "Por favor, selecione o conteúdo do pacote.", "y6fr8Bq1SUa2Y4t3QsKh4": "Insira o número do lote.", "RVpUIj1EBBJ1awwP0KoTR": "Por favor, insira a conta operacional.", "Q01UqFAPK5CgQBT6Va9Ur": "<PERSON><PERSON>", "x76HSqPas266y9PL13L2c": "Modo normal", "IudF8SqtFP2jmoBEgx5RC": "Modo de economia de energia", "_VJb7sn4Wp1sg_MpUMqqx": "Desativar modo de localização", "E19JqAWWpVfDPfQNztbi6": "Número do canal", "76VJ_0CYltD6DDiTNyZxf": "imagem", "8j-cIxRn0mxuGcQu-J6U-": "vídeo", "J-XTISs_rbWSjK4D-RUnI": "evento", "P3xgcENq9CpwWHOJDCodc": "IMEI/ nome do dispositivo", "jyUQCyhg9_HqCbJnmTNj4": "nome do evento", "4IqR47LAiISMhbMm7IhgP": "A execução foi bem-sucedida, visualize a imagem.", "RHSs8Xwa_q8HNTZN6MyTw": "selecionado:{0}", "0D8w0x3Z3-4C-ei6GzRO2": "Carregando, tente novamente mais tarde", "jpXZlwrJaiDcEaQiwy10A": "Selecione pelo menos um número de canal", "NowXQsGP0xEGBs1WjE9_Q": "Não escolha provedores de serviços duplicados", "cQosMBRHsqt_530cSmbwx": "O intervalo de tempo inserido não pode exceder {0} meses!", "xOwwQNdPzsClyM2sZEHsx": "Selecione o dispositivo e verifique a data", "5x5ttmnhGQm_xfx8-fUMW": "A consulta foi bem-sucedida, selecione o canal de reprodução e o horário.", "IKa5v1S4C9m3JqKSNCGDm": "O canal de vídeo e áudio está ocupado, não pode ser operado.", "PeLZGwiFPA7OD53c77fpj": "Certifique-se de ouvir {0} ?", "xEKN7mlzVzK9GQt_b-Min": "Fim do monitoramento", "TE2fBaGlpdBY0lKWjOTKq": "iniciar monitor<PERSON>o", "5D4MnWs_OiIq-I_QsGAyX": "Canal", "VgbN0QpFxOoOVebTMifEb": "Conectando......", "-rwIcMW4enfFNVkLmJOOF": "Monitoramento:", "Z2KhLcz1RQv-0BG_4cUod": "Falha na conexão, tente novamente", "-uah4lHNi9qpmlWruM9Tm": "Fim do monitoramento", "SW8Lee_dtDjszssgsNc4W": "<PERSON><PERSON>r", "ZrCxLSKRGicXpczHTBMUR": "Por favor, selecione o canal de monitoramento.", "Mwu8g570bWTiEqGKSZBKM": "{0}v<PERSON><PERSON><PERSON> fechado.", "6zg6_aIDxHLfIYIsnh7wJ": "Sem vídeo no momento", "_SP67dHXDs5attlodtzd4": "Depois de selecionar o canal, clique no botão play para reproduzir", "-iSrrSkshPlUsGVHwnJlz": "Tem certeza de que deseja localizar o dispositivo imediatamente?", "BCInOUOWDZqvRtmRgoaIa": "Modo em tempo real", "rksCtLkboMzqxSXtG_Njp": "Modo de espera ultra-longo", "_d7RyxRhjvrOS12pZe1Rv": "Emissão de voz", "Y-ouNNO1zuT0O25-wmQC2": "Recomendado não mais que {0} palavras.", "BgThYyBBQxKdbvTTxaqEu": "Deseja enviar o anúncio de voz?", "-oHWPszUGv_bgmY41k0OM": "Velocidade do motor", "rcVq-K-FhY0x_XGWgWQyH": "Pressão absoluta do coletor de admissão.", "WB2u4Gtzp_x_qB5aUdVA-": "Volume de óleo restante", "D5xhxtuMiaCdB6ZtyfA-I": "Ângulo de avanço de ignição do cilindro 1", "wiVXwMdYifHIIDTaYy7Uk": "Temperatura ambiente", "sxKO1EpE7584f885OstmP": "Pressão atmosférica", "hVS2wfr7_gPvVf2ZXdUov": "Temperatura do ar de admissão", "nIvuguTEb64P4DeUO6Kyx": "Temperatura do refrigerante do motor", "wL1nAP2XF8cqzzAea1l-M": "Correção de combustível a longo prazo", "ir8dRcv6tPQQT3b4fDUhJ": "Valor de carga do motor calculado", "pmSiApNt_l-CLgU_V9-EN": "Posição absoluta do acelerador D", "Jvzgr6i4iCYe861FOePqW": "Número de códigos de falha", "vOB_ErOfnohph6BFBeJa8": "Status do código de falha", "OGYkaAzV3zLdQVPWtr3aM": "Não iluminado", "6l5PUoCfEha4agcOU7c3c": "Acenda", "VXAStgxYyFeoijgMgkPGh": "Animal de estimação em busca de volume", "XqVd3m4oejVwBud-SvnxK": "animal de estimação em busca de luz", "2247BJLuV1ixtjGA1O7gV": "<PERSON><PERSON><PERSON>", "vF3kO35MlJqcY7R7BojED": "Pacote Geral", "Bpdy-V0IcCvHQ_Agega0T": "número", "1W2i1KY0Bfz_8olXlC2d6": "Preço do agente (yuan)", "zUwkOXwA9wIf2S0kzBT7x": "Preço de venda (yuan)", "pWM8WfOGzZzcyBJMF4w4Q": "Descon<PERSON> de <PERSON>ndas", "-sHvr4dqzhBg8zhkGisc4": "Classe de serviço básica", "SlQAbSeLcvKG64T_WCQ4v": "<PERSON><PERSON>", "WYZoPhp_aCouZhtTNRx_v": "Serviços de plataforma", "ulJ0IYNw9yhO3xtOejMkq": "Serviços de plataforma", "BxnsRGI8yOGF3AIgmEDPA": "Gravação anti-roubo", "S5BLPa5cR1lfkPtX8C9ra": "Gravação HD", "cu1pVC4m4_ExdeETrqlaH": "Para cima", "_7CU8hvXPxWIHZ2Hw09lG": "Baixo", "wgxerTVV3ubJSwGQbIRXK": "Preço de referência", "eGPJZha0zDcNjnOUxMl5f": "Preço de venda", "hqZrtrvlMrwFhVBJDoK9d": "Preço de referência (yuan)", "biaeNmEm5PyRHVooq6ijB": "Novo agente", "woHbo-mjkIJGvCCXfPGdW": "Editar agente", "krRoZPXODjW95IxZY3Jxr": "Preço do agente", "EoOkU9Au2oolyDpxoABEO": "Descrição do pacote", "0gTRzqUCLGERAfFhL-8y3": "Pacote Padrão", "iqmW63hnN6IZmBcr4Bx_N": "Todos os dispositivos", "rY_cnjuM0KhLnfuxRdY7Y": "dispositivo sem fio", "VLVXHDOnSopPJux0MHDsU": "dispositivo com fio", "Cc1uwqoUoGjbyyWsaRu8q": "<PERSON><PERSON><PERSON> pacote", "vl7XPni0Dn5MHXeBkgUvR": "Preço de custo", "Oe4zmx4CPyMN2Y_uT0kXj": "Reinicialização do dispositivo", "hjX3McS9BgQRZc-zoimJw": "Dicas de operação: Após a redefinição, os dados de teste, como tempo de ativação e rastreamento do dispositivo, serão apagados e o status do dispositivo será redefinido para não ativado on-line ou off-line", "0rxp9_tAb1nkVwt7ZfEG9": "Propriedades do pacote", "aoalRCCBuSPYz5q4mvZI7": "Número do celular", "jDJyz2pFt82shF84u9X7N": "Status do serviço", "V0p40frI_DrTg5_KB9uQB": "Validade do serviço", "F3tP2-szQjwBbJ2_Wgdky": "Comprimento da trilha", "AbcDEN7NWWYIeiIQTs0Ps": "desvincular", "j-09UjLIz8t7ucYKWSTdZ": "Você confirma para desvincular o número do dispositivo?", "ovK_RRKbTaBSEHhgL_kRy": "Recursos de valor agregado", "mMGDudnh3-MYiKN3ZmVZQ": "Duração da validade", "iIRxRd4XquTPvNwkbbPAt": "Regras efetivas", "YEsjuadygPpzDlYbOQdfC": "Custo", "ygl1CCzlGB6pYQi5oJgMg": "Tipo efetivo", "eIhE-MpijfPx1_YivOWfn": "Mensal", "N5usFqbTBj_x-AGRsPR8s": "Custo acumulado de serviços de valor agregado", "a9_Kks1xUEIPs5523_ebG": "Adições de pacote", "CTqygomw_FwACb306Vgef": "Edição do pacote", "j_nyP6IyvoRo9kejtwukP": "Yuan", "ZVF3com1sFG-XrZC-livm": "Categoria de serviço de rastreamento", "F6SGMDU-K-qsjW7cV9B-3": "Ativação do serviço da plataforma", "POuxG3He6OPD_8Jxozz0m": "Horas de atendimento no palco", "KeHIZrek2q3vBPe4Cp8Da": "Horas de gravação anti-roubo", "QKAK9kLAa86Xy4nOgZf-G": "Horas de gravação em HD", "iPiBIJga70QxIIRCBldDv": "Em vigor no mesmo mês", "FXJLzQWH0isx1K681LHvq": "A partir do próximo mês", "tf0jk5t-3-we9pamkVaB8": "Preço de custo (yuan)", "o7LB26olpIL6MYrh3AEkB": "<PERSON><PERSON>", "Lf5yEIWEaqmGp-MdVGn6K": "Insira o preço de venda", "_0RCRKXXXIzu2xxfKtnTl": "Por favor, insira o desconto de vendas", "ikXI1IxXKIQp8u1h9AA07": "Preço de venda X desconto não deve ser inferior ao preço do agente", "RB650X0GgnbB2n4QFIvyn": "Por favor, digite o nome do pacote", "O2O9o9Gdf7kujPQNuGDx0": "Selecione a função de valor agregado", "hMbxn5NOJ97gDiey6VkNK": "Insira o preço de orientação", "AsjA-juIl1c-vrm5bkDZe": "O preço de custo não deve ser superior ao preço de orientação", "hqIAi-yNhcVJJes_Wii8D": "O pacote padrão não pode ser removido das prateleiras", "1tG8AEnd8XObHHyr_LJWO": "O pacote expirou e não pode ser colocado nas prateleiras", "Zt6qNQ4s8YR-9ONkDbjz4": "A duração válida (quantidade) deve ser um número inteiro", "HcbviLThyjLIgx7yQhJIo": "O custo deve ser um valor maior ou igual a 0", "PnNbXwUM5AIUvXD55zozb": "O preço de venda suporta até duas casas decimais", "8SZSVQ78DZJPp5CIkPmPA": "O desconto de vendas não pode ser superior a 100%", "M_FhAqKNUJRMUlAI_5Vg7": "O preço de referência suporta até duas casas decimais", "p0b0S03PgYUBMU8N6cMsY": "outro", "eWUjPc_p2SoSuyirGqpJC": "{0} minutos restantes", "KDjF1mSArjb_8e-G98TE1": "Total de {0} minutos", "3bRqkRhUHfGo7-5bj1NSp": "dentro de {0} dias", "qPN0DtMhqG7A1BZBT1cLO": "Desvinculado com sucesso", "eabaCuk_HwzDEiN7IPbvs": "gravação ilimitada", "7o9hFnRaSRSx3gBUxUS7c": "Modo de perseguição de carro", "yvNR-W0Zy8b780JbYTcDz": "Se 000 for definido, o dispositivo permanecerá ligado até que a bateria acabe.", "2akZqOLanUYhOK--8J8k9": "Unidade de duração da gravação", "UQEVNmYnChMFTjlzBF8HL": "fluxo (M)", "68DfXrUSOzBjsZopo9k6K": "dura<PERSON>（min）", "j8lyKEC7rR2Sp2J66aMSE": "peças (n)", "thL7pcDqxtuMTgDoCxlBT": "peças", "WZSRsRw77pZ671WKh_pPu": "Gravação antifurto: 1 minuto = 0,125M = 6 peças; Gravação HD: 1 minuto = 0,385M = 6 peças;", "wl6K0SUQd6ZY6YvzFM9u-": "Quantidade de energia antes de carregar", "BumVkHW3JAn_PRo1yQcpB": "Quantidade de energia após o carregamento", "eimmiPhZYS-8rwVnb6O_F": "Capacidade de carga cumulativa", "HUdLf5Qxok84Kaszyp_fp": "Tempo acumulado", "FNSsRfB5Bxlf2Olm2Bw5M": "O veículo está desligado e ativar o dispositivo remotamente pode fazer com que o veículo perca energia! ! Tem certeza de que deseja ativar o dispositivo?", "0re2Dz1xGLdRIiOtLWBC7": "interfone", "jYDwCZu9Zq3SmUDyx02I6": "A conexão foi interrompida, tente novamente.", "kT3Em9RLb40yUyQU2evbW": "fim do intercomunicador", "J25nBN1alltybJIfS_Oog": "Falha na conexão, ligue o microfone primeiro.", "3mmuWWCL0vQVg_OKx7s83": "O intercomunicador foi cancelado.", "Q3niut5rzhCbc19r8z6IE": "O interfone acabou.", "Pg7k0mGxvOxvRWARJpzfg": "Determine o intercomunicador de voz com {0}", "XNuroHgKla6Y8FamER4aN": "O monitoramento foi cancelado.", "jiTHwcnpRb8phd5923CyG": "O monitor acabou.", "3QLWf5J9RxGxyAS6uvvn3": "Habilitar mensagens de alarme SOS para este dispositivo após ligar.", "4RrCaePSVwyb6dIgHJAqX": "Porta do carro", "7zhRC0Dt31JaorstFzV6V": "Confirme se o dispositivo suporta esses recursos", "LTRnQg4MJhykYTWC-gy9o": "Status da porta do carro", "ABmfMli4oBWSNTfpwhgCE": "Ar condicionado", "BqNwXl-ubCc-XQqV9O_ai": "Perfuração em detalhes", "F6x4nIih9qd4aJ38EvJwq": "Punch in Summary", "FiAchNAkfO6rQ5HjVgU9H": "Gerenciamento de drivers", "6IiHBsPsmt2DsTs-jU5T1": "Nome do driver", "l4zTYCxKwWS524zpXPvDO": "Nome do veículo", "UeGir6yBHf-qmk0S1MGlw": "Propriedade do veículo", "OXT0B09os-9cWGS3qpQlU": "Número do motorista", "8MPbkjc_ZQug88ZNTfODw": "Método de perfuração", "lDQIuVzHPDvREIDtlgET2": "Punch in status", "yfUqcxxldbfEsjLVNkxej": "Método de saque com cartão", "ZyeVJa2gqbhhQO9I71t64": "Status de retirada do cartão", "ji8RZWQkmXTT7ATPAAy_2": "Duração do tempo", "Pg8DZ2SFsTy8TojEeHX1B": "Localização da perfuração", "0ikzdX00_UnFach5SBJXF": "Perfuração no local", "HIwKcqVBFF874LvZ_WGam": "Tempo de saída", "MToDC9TiRnmBcdPXl4FwO": "Atribuição", "LnK9XV16hgzXMb2YOEzT1": "Para vincular", "rdH_UWyNxXZODhFqxxUmP": "Novo driver", "0Dt9UrCF2o2oZXyw4vW13": "Certifique-se de que o número do cartão do motorista está correto e foi definido no dispositivo por meio de comandos", "1leGeFJqaq67Fn4tiH1zx": "Bind driver card", "Srl9p3JczfeWAbfvhn6Ly": "Desvincular cartão de motorista", "UGF865b9yIhFrTCVyLWxX": "Um total de {0} partes de dados são selecionadas, das quais {1} é desvinculada e {2} é vinculada. (revinculação precisa ser desvinculada primeiro)", "hHaTIsbL4r6vms19pugun": "Excluir informações do driver", "dJ3KpaxrLmH_3rhK9unJv": "Configuração do número RFID", "thiOWyWe1XmakaJ3GiOa8": "Limpar número de RFID", "AxP3mfFKmnFaXGqh5fIhI": "Uma linha representa um número RFID, suporta até {0} números e o comprimento máximo de um único número é {1}.", "rfidTip": "Uma linha representa um número RFID. O comprimento máximo de um único número é {0}", "d3bW-I2JF9X4k29xNZGGb": "Consultar número RFID", "_k-zAyJ8HPGbJdM17dzPH": "Driver desconhecido", "QO0NEmAJYU5fQSmklCOJy": "Correspondência do sistema", "PZufwLgOnX4fpRaD3iCSU": "Entrada normal", "9QOX0uIsKTjJBbo44I-Bb": "O número RFID suporta até {0} números!", "-leT9C--bOJXbur1tGMLg": "O número RFID é inválido.", "FVu9irj9xPTNFGBGvlobc": "Tipo de relatório", "COZQ6iZB1zS4aElBMs78o": "<PERSON><PERSON><PERSON>", "qXO0YkCGN6h8FYEqKl3p_": "<PERSON><PERSON><PERSON><PERSON>", "cGBoFIfJ2d5YNsf38ePbg": "Tempo de construção", "jZyH20rj2wEEEQcsmGcYk": "Intervalo de tempo", "BpQY6g4_DbX8RhgZMVPQ4": "<PERSON><PERSON><PERSON><PERSON>", "qH0wuLIULJgKD37_nfLrZ": "Lista", "8hALcKt0MpF_SQGoBpb4K": "Formato", "Mx9jIE1yFOacxR3pPnIFY": "E-mail push", "bjPw4KOPuYjc6NUfC-SyH": "Tarefa única", "9AYF2wxsGFHQUgIvy034D": "Tarefa cronometrada", "AdhpSaYYkylLxIlRrLNHr": "Todos os dias", "n2QHPhR_EzYSzaNPQ6NmF": "<PERSON><PERSON><PERSON><PERSON>", "9KLn6bfLtRc3MQgbsjyEo": "Mensal", "ieZ8uvcsbtzbKohADR8W1": "Dispositivo selecionado （{0}）", "RyPBlexis_W5GIRM2muRj": "<PERSON><PERSON><PERSON> rápid<PERSON>", "G74wdj_2KXBhhyV7zDJdr": "Última hora", "TRBbE8yn5-eXAAnReeJJb": "Últ<PERSON>s três dias", "HS2zeNZLmT6EroeI89wkw": "Ainda não iniciado", "iit63cuu_F-O9XEnoFvcn": "Ignição ACC", "XUnTcb8HIcoLV6jX_-bHw": "{0} minutos", "zqbPU0UwwDMJdobNznkdn": "Selecione um tipo de relatório", "er_Ap5667cZtQW4X0jKfo": "Por favor, insira a duração", "MJMdS_gSDGYTeKt7KPuKb": "Por favor, digite o e-mail", "VX7OSMnRmFVq9SD6yTT8c": "Não executado", "gksm18T7sI0BjTgYSs3xr": "O nome não pode exceder {0} caracteres", "usermanagement123asdd": "Detalhes do dispositivo", "s0ozWWyNHgC_hIgeFR28o": "Detalhes do pedido", "8nrk8LIy6KzpJQv8D1o0w": "Nome do usuário", "nJDVObsHH5ogCl6MxIO4C": "<PERSON><PERSON>or de serviços afiliado", "daGXDH-K0OYEPY_EUdT-c": "Dispositivo de hardware", "qdU58CUu3U1NuSOt3DBVU": "Amigos associados", "t22DN7yhHaAkxCW69UYsh": "Última vez online", "D-b7dWflNTTmQ5ISj_Fcy": "Desativar", "fuKtObxB_3LepAmhP3yf_": "Tempo de expiração do serviço", "629CyKaKNeUB9bKRG0Pay": "Tempo de vinculação", "4hM8uJZMOB7wXdwjRHa9p": "Tem certeza que deseja desativar esta conta? Depois de desativar, esta conta não pode fazer login no aplicativo.", "yk5sO0QMXGD7l_MxoTXYA": "Serviço de localização móvel", "3aj3cEG2NB2VfffUFAdC_": "Insira o IMEI", "M3jEXD8nRHgxmpsbQKAuz": "Por favor, digite o número de telefone", "xzJrxRX1dNU-Rb_0eqU-7": "O número do celular suporta apenas a inserção de números", "gi1Rx6yaRf1mw_Ma10Og7": "Aula de recuperação de data", "0DzF5hCURmG4E1XvrInUx": "<PERSON><PERSON> di<PERSON>", "zTlleNA-rGV7NcSvnohCO": "Quantidade congelada", "j3-S-EHwypDM9f764RfWK": "Reti<PERSON><PERSON>", "UpktOagmkL4g9LVa3s4DI": "Caso o usuário não conclua o pagamento do saque após gerar o pedido de saque, o valor da dedução será congelado e não poderá ser utilizado", "HdBEUEdRZtTN4ZfrwWDWj": "<PERSON><PERSON><PERSON> da retirada", "cBvWd-2OWDLK-phHepxX-": "Por favor, digite seu número de retirada", "3tBnaH8FayAwn1TmLrMRE": "Número de <PERSON>", "-2qRzHpb3yJe-NXYwm9Q7": "<PERSON><PERSON><PERSON><PERSON> conta", "4ox8EnAHNJ1ge4l1vollX": "benefici<PERSON><PERSON>", "CqTkd8dVrd22g3nDNVCoy": "<PERSON><PERSON> da retirada", "0RwYjMyWnVMT6CIWbTvc2": "Tempo de aplicação", "YgZRMzhnb44UD16opnREb": "Revisão de pagamento", "IhERyASJM8HWY4YSRVoeO": "Auditoria", "0GEWe45NcNov7FwWFSMVW": "Total de saques em dinheiro", "L6xr2tfBSDAIDoOf-18gW": "Autenticação de nome real", "LSUmKyLB2Gn7aIkH7L-hV": "enviar", "Q0SSVtTXVVVneQs56sfNV": "Para garantir a segurança da conta do fundo, preencha a autenticação do nome real do provedor de serviços, se você não tiver um nome real, não poderá operar a plataforma! As informações são preenchidas automaticamente por o reconhecimento do sistema, se você achar que a informação está errada, você pode modificá-la manualmente!", "ExBfBD38BcFKpPdnaMMHJ": "Upload de licença comercial", "Wsx4i0OdVEnXu1i63lLdv": "Nome da empresa", "ICkaBpHUtevl_AT1N__hY": "Código de crédito social unificado", "mzMzHjVtXGlHXVdehyj32": "Carregar ID", "Im9siJmOMFBKtFadxaSF8": "Nome do representante legal", "wRlEeQixlH9M9_HIP4vYK": "O número de identificação do representante legal", "SgzsmJZu_MjkxFDKgtv_h": "Apenas {0} arquivos podem ser carregados e não excedem {1}MB", "qC-26Qu65eWrPudHuLnuV": "Tipos de arquivo suportados: {0}", "zEv9TzyaPU0xOkzgdVrzd": "Tamanho máximo do arquivo de upload: {0}MB", "mDQb0v7xumz3q5VuF3Vmc": "Cara do bilhete de identidade", "2ok-cfZmy8rdxwZL6-4nd": "Carta do emblema nacional do cartão de identificação", "IkmaKekEYC3fKh3fRt8NS": "Por favor, digite o nome da empresa", "_RtgmCH5vQ8wdY1tlQHW7": "Insira um código de crédito social unificado", "nisCJPEsBiP2BkHolBjWx": "Insira o nome do representante legal", "omKEokmgAc1zk-PZxqP-8": "Por favor, digite o número de identificação do representante legal", "y1yB9wGJ8CWF63TCI9WpX": "Insira o número do celular do representante legal", "hQBDtnCXVOY5IURttX9Wp": "<PERSON><PERSON><PERSON><PERSON>", "Pzs_J67ueBLvXIl0jEXSE": "Enviado com sucesso", "ZPq_ipnNUy_FW5VWSeimD": "{0} Reenviar em segundos", "_blypwAlGDx8o0-3JRFdO": "Por favor, carregue a licença comercial", "LTy7Te4YBG56m2zQdlHiP": "O formato do cartão de identificação está incorreto", "0ZqoYIX5loeLfUCWxwKB0": "O formato da licença comercial está incorreto", "IYmN5ElWd2GKDXm_6Ox7l": "O código de crédito social unificado está incorreto", "jjTIqk33Rdkkx6Py1HxoY": "O número de telefone está incorreto", "FDRZnbeysAjO9tp4YIYqv": "Para alterar as informações do nome real, você precisa entrar em contato com o negócio da empresa e alterar as informações offline", "rbQMeechuyoZqAFzEyLM6": "Autenticação de nome real da empresa", "TI6ctBVhkuX19fhWGPn49": "autenticado", "8jyucpdW1YuSmtRGAIb6K": "Licença comercial", "IsF9Jc79BZ211alKXLRR0": "Cartão de identificação", "QFiEj7y94A9Ja8AkWUNyf": "Número de celular com nome real", "qd8IBrhi8u-w8Y7DF8b-q": "Carteira de identidade do representante legal", "HAN6a6h7V1iZt0lnwGGcV": "Alterar número de celular", "T83DURPRdSVIBzAvsaeez": "Certifique-se de que a conta de saque e cobrança pode ser cobrada normalmente, caso contrário o pagamento falhará, a taxa de processamento gerada no processo de solicitação de saque não será reembolsada!", "d5h6lLvBW7oXMESBAf-xM": "Re<PERSON>rada da empresa", "9dNZZYaCdlmDKhSaYelkB": "<PERSON><PERSON><PERSON> pessoal", "GZy7CzuyKTwsBKOmuCs1T": "A autenticação de informações não está sendo realizada no momento. Adicione-a primeiro", "JN6k5nduoDoNFRKZS3DEn": "Valor da dedução", "pHaDrlW6Ws3jwOkuAbQoM": "Upload de fatura especial", "t66P_5Nqai7Za4Eu4ITGB": "Arquivo modelo", "uvVmCHuLW4xeFcLm45zbc": "Carregar novamente", "qR0w92Dg9fchUpj2DzSC_": "Formato de arquivo suportado: pdf, tamanho máximo do arquivo para upload: 5 MB", "uCBzEQrQku-26KkCRss4A": "Valor recebido", "fU-37Env5DcFrKxRb3xsW": "A taxa de retirada é {0}%", "vcjXle_BohdJUl4d1oLue": "Por favor, leia e concorde com os seguintes termos, Contrato de Serviço, Política de Privacidade", "7SBVFKuCpv5odq1HN33Ln": "alterar", "Wx9bkDQ3RJ4pkj9EV2TAy": "Li e concordo em localizar imediatamente", "w3RNmBUSW9RqkqTEoK59O": "Contrato de Serviço", "OqJohIkvVLQABgxc5DRdr": "Política de Privacidade", "bjGxifi3699uiCO6gsCRV": "Gerenciamento de contas", "xiLtJRgF_g8uX1RTxKvfX": "As informações acima são preenchidas automaticamente pela identificação do sistema. Caso as informações estejam erradas, podem ser modificadas manualmente", "NaD6zzw94Z1os45DPZwjr": "Upload de autorização de abertura de conta", "CUmdfQcRlZ4i_0jCpy3a0": "Nome da conta de saque", "nMIYlhG5tsGuYCDxDF3tt": "Banco de depósito", "8Q3xmvZ9DUvHOt7XY-i9i": "Nome da fatura", "BjInLzWyZCAq-mP4O2kg0": "Nome do comprador", "xCBXDogrKXOiq4chx5EwV": "Número de serviço do comprador", "YyzFoNwurUCVV9H6hgtsA": "Nome do vendedor", "CCjOqQunpaqrCRLHmX3N4": "Dever do vendedory número ", "aBPmzVAoqSZiJWXyn9bN-": "Taxa de imposto", "REFy169sYWJ7Md2hb8sGE": "Insira o nome da conta de retirada", "LWAmlhiJWmHzYrie1S9N1": "Por favor, insira o banco da sua conta", "pJWb9Yer6FxP6ovkzGrK_": "Insira o número do cartão da conta bancária na qual o pagamento será feito", "WDW0-yYxFWMfodLosOWb9": "Insira o tipo de fatura", "sgfGs3OTZyvteg31GHtiX": "Por favor, insira o nome da conta do comprador", "a2ULMzDVkEk-nPXKBJUXI": "Insira o número de identificação de contribuinte do comprador", "vgTEJTdZurmuqJhzrZ29P": "Por favor, insira o nome da conta do vendedor", "vKsW1EMNxUJGGfV4ZYBXg": "Por favor, insira o nome da conta do vendedor. Por favor, insira o número de identificação de contribuinte do vendedor", "TvCzo89fxA8tR-8Lpyb0q": "Por favor, insira a taxa de imposto", "Fkbqaa_km9aC0nyOkzceB": "Por favor, insira seu número de celular", "hnIRT9k39y16Q99rQQzsA": "Número do cartão de identidade", "oKb5Wq5V5XoAfzU-M0Otf": "Nenhuma informação de cartão bancário foi adicionada", "WryJR0-_zuJIjTJ4EKK1z": "Nenhuma informação do Alipay foi adicionada", "zbt_yS1gKEnz57prLvp2K": "Cartão bancário", "jvnedWVXi4gkwKd5I2hvo": "As informações acima são preenchidas automaticamente pela identificação do sistema. Se as informações estiverem erradas, podem ser modificadas manualmente. Cartões bancários classe I são recomendados", "DE215MPVNCkdUHvjIgGZk": "Upload de cartão bancário", "Fllf7DIpwifaI2rjf1wQO": "Banco receptor", "L_lzmLBmj3YzI8mZG5J0S": "<PERSON><PERSON>", "LgIQiPo4j90qzVhoe0Vy8": "Cumprir e concordar", "sdFitZwo_yLFH4QGGN3Yt": "Acordo de Parceria de Emprego Flexível", "5tav7xkbaZ8_ibteH8f-S": "Número de celular antigo", "_0koNeMLJ29pQU0iaEDgu": "Novo número de celular", "DxCo_EDksU3itpOI06hg-": "Por favor, insira o código de verificação do seu número de telefone antigo", "KKTmtCyrgM1yvbXV5zHcv": "Insira seu novo número de telefone", "3-OViSd7qngs2IVge-AK5": "Por favor, insira o código de verificação do seu novo número de telefone", "oQ34kNi30enS9BhzsG0H-": "Para alterar as informações do nome real, você precisa entrar em contato com o negócio da empresa e alterar as informações offline", "0sCR-_9qqz7K1qILK3DVb": "Insira o valor do saque", "EbBJyirZ75fpO-gVDM83Z": "Insira o destinatário da conta", "OsDOX2z2g1Q-gK32LRxFM": "Insira o número de identificação do beneficiário", "_l_YQpYxCoNyeAL5aqwGZ": "O valor contém no máximo 50 caracteres", "pWME5Atx2F7StkRlHkIm3": "Insira no máximo {0} caracteres. Um caractere chinês é igual a dois caracteres", "qkjCxOqPs_QAibUK-0Y1Q": "Insira no máximo {0} d<PERSON><PERSON><PERSON>", "WJLplQQ9j88JLH1YqCRmA": "Insira no máximo {0} caracteres", "TF-2MZlCuvwuW28jBv6I0": "Por favor, insira o nome do banco da conta receptora", "w_r8IwPRfn4mFsJI9VODn": "Por favor, insira sua conta Alipay", "qlaqPAd2iRvHO0ZwbK-za": "Conta do provedor de serviços", "VDbpijEe2xTelunTQS-4e": "O valor deduzido excede o saldo disponível", "eY_5eabsCmQBbE3r0rV5a": "O valor de entrada é menor que o valor mínimo e a retirada mínima de uma única quantia é de {0} yuans", "TRJhAFDq4ep7qm7BVCQXJ": "O valor de entrada excede o valor máximo, limite diário e mensal de {0} milhões de yuans", "nST-25toc3noZP0cfW1qk": "O valor inserido excede o saldo disponível", "DK_Faq17n14OmHECO018w": "Retirar {0} yuans, remeter {1} yuans, deduzir o saldo {2} yuans. Tem certeza de enviar o pedido de saque?", "GzYwkFzGANgH5TJD5X8X2": "Prevê-se que chegue dentro de sete dias úteis. Tem certeza de que deseja enviar o pedido de saque?", "xkj_ATjt1xs4_R-rg1Ji0": "Alterar a foto do perfil", "5207eon94HDHbK4sDdL74": "A formatação do cartão de memória não será recuperável, por favor, opere com cuidado!", "EHD3KoOJqi2GCWNVeEmwd": "Formatar o cartão de memória{0}", "kO825bS74aibjof-TZHgI": "O número de dispositivos exportados não pode exceder {0}", "rwsPrmsetvYwcvgy_tgQH": "O valor do alarme deve ser um número inteiro entre {0}-{1}", "xN-U2ljzfROs3R2UFCYsM": "Gerenciamento do número de rastreamento da Amazon", "_N5YSHeYO9S0dkx3zF49M": "Insira o número de rastreamento", "nBb0XGJJR6Ejlzv5qSHpV": "Número de rastreamento da Amazon", "9tWLlIJg_5sgvhCXM44UQ": "Insira o número de rastreamento da Amazon", "IyD6K3KtVOOO_VcBTjgZ2": "Não pode ser restaurado após a exclusão. Deseja excluir?", "UFmFUDIdFnppVa1V3bBST": "<PERSON><PERSON><PERSON><PERSON>", "_oBDgDEVJpXqs_vs3_HI9": "Arquivo modelo", "YGoedXAVvM1MW-3niNbjm": "Importado com sucesso", "Wi6h3aRHdkRiVA_ltXWj0": "Editado com sucesso", "QE818HiR698_BEiYMx3u2": "Só suporta arquivos execl", "uTwenDZPA7tqG0989SQXu": "Descon<PERSON> de paco<PERSON>", "aaBbwSAmqzqUCcJRh515g": "Preço de agência ($)", "QLgJjy0jWjsRUEDzFwoX8": "Preço de venda ($)", "OFM2oOsQ2x9IwEF2ttv_b": "Preço indicativo ($)", "xOo6bW69d4UFSs5A8Lc6t": "Preço de custo ($)", "gQPrng0oObyjc6BgSvrjD": "D<PERSON>lar", "g68FVq2_VXAjp7hYpDiRN": "Taxa de manuseio", "mj3M4r0KwKkzvAFrIO1Wr": "Valor recebido", "5-ZgZDBneAm7B6wX8GojM": "<PERSON><PERSON>", "A6LwrujHDGaAuqduxVNP_": "As configurações de E/S não podem ser repetidas", "DVbzGOXzFw3chkPtHUj3s": "Operação de gravação", "bindingRecords": "Registos de Ligação", "LpQxX7pBohd343PV2C6l-": "Tempo de geração de gravação", "_b20VKdHpaKqao11ii03n": "Operador", "w7fGz3NHk93u9dv-zX0QD": "Dispositivo afiliado", "YOrAWZ7vpBk3TJg-dkb4P": "Gravação", "F0uq5S360ilbM_B2J6AKU": "Tempo estático", "Fcj9rd0TdhVtlRNldRgLO": "Hora de início estática", "sTK0zTcZFNQTdsKoeupiA": "Por favor, insira um número inteiro positivo", "AAQN_3lzSsjg1ihjW8Ox6": "Por favor, insira um número inteiro positivo de {0}-{1}", "sjsJKFIklgkg1ihjW8Ox6": "dia", "SQpS4TgBG3fMcqbMSt5r6": "Configurações da conta familiar", "jdlS4kVTg_CeomNPAM_bK": "Formatar cartão de memória {0}", "tcqzqrxZ_gYuVhkt8OehJ": "ponto de parada", "T6iMJCitY1kEG5bcW9HaV": "Os parâmetros do link de compartilhamento são inválidos", "-6NUqOp6i8GMNZf-wX0W2": "lento", "MTpCOGV40pnn900xY1J0D": "<PERSON><PERSON><PERSON><PERSON>", "W1n3IpE3yaJTXBtZ2UgoA": "alta velocidade", "lmCYFn9l-af231uvKKXNg": "clique para mostrar", "5l9_lUSa6C_lFR_eESOlJ": "Experimente a aula gratuitamente", "PjztEjunq2DdH8CUhaTom": "Amigo aumenta classe", "5j3kZHTwBeczKhgIlGP_-": "<PERSON><PERSON> de <PERSON>gundos", "OH_8D7HPFdgNgklMyg1AE": "Cartão de identificação carregado incorretamente", "OfBdh8qjrGLU-ZdWl5vOq": "O formato da licença de abertura de conta está incorreto", "OWZM_rW5ca_xlQz_J8Xvy": "Formato de fatura especial incorreto", "9OPyc0VO0-5p0d5Qed7Qy": "Por favor carregue a fatura especial", "6HtrWK5wh8axd06VVCS92": "Definir como padrão", "uOPB9tT30SyYk_0INSAYq": "Por favor, insira sua conta Alipay", "n_gZB7je9SGq7ENwoME-1": "<PERSON><PERSON> quando as informações da primeira conta de saque forem aperfeiçoadas a operação de saque poderá ser realizada", "hIpV6Yp1O1wSHehp-inf-": "A conta de coleção não pode estar vazia, adicione", "RMrKKjo43e39krDHu3aj8": "Por favor, cumpra e concorde com o Acordo de Parceria de Emprego Flexível", "PA1XThpT0ZeCsgxFK0j0J": "O formato do cartão está incorreto", "tKjv5WnytxK4wm-WJXePG": "Selecione a forma de pagamento", "XFJzOJWIXzS-m-Ny2eXc9": "Insira um número positivo, com até duas casas decimais", "ESkCYm4yQfnXTqmN8lyEM": "Os números antigos e novos são inconsistentes. Digite-os novamente", "2cdbOHjhkxnwglIpBtMkT": "Verificação de nome real", "IiLSldp0KbladWLu3iyAc": "Por favor carregue", "Wo_ctGJBTe7KxuJltu9Gt": "Antevisão do avatar", "JmBSnAvO4DN3XSi1cxZnb": "O valor do saque não pode estar vazio", "Z0Tm_VlEKyRO3z_N1zVo5": "O valor da dedução não pode ficar vazio", "85Bv3yRHBX1Q-bx2fsC7i": "O número de telefone não pode estar vazio", "zovpNYJACi9yNGKOVuO_j": "O valor recebido não pode estar vazio", "6_bQ6z9UwX8_tB64es-tv": "Não fiz upload", "G_w7OaWVA0Fc_ZaYRdEet": "Licença comercial carregada incorretamente", "MNysP677Xd0NQ6tBqvUxG": "Licença de abertura de contaense carregado incorretamente", "oJ1yZ_MvfuN0QEeBrJW39": "A fatura especial foi carregada incorretamente", "n-cfAU-iHIIcYW0DRnb1M": "Insira a pessoa de contato", "1kfaRbMY51Sk_FlCts7OX": "Por favor, digite o endereço de contato", "IEdjqcPX5tg0KEDbRFuFf": "resultado", "u7luzUuxiNGav9UQrcdN-": "Boletim de pagamento", "Z3rtP8jS9MkkLD-7eNsm9": "Auditoria de pagamento", "0gKC2yGndZbATta9DrMLH": "Por favor selecione um resultado", "Jht787aJMAS7FrZc5PFwo": "Por favor, insira comentários", "zsci9ZO7C5fd2L3lpNgPb": "Por favor, selecione o motivo", "-OG-7bm5-uMXgqOUZJs5E": "Por favor, carregue o comprovante de pagamento", "vDoHr7o7eBmMjapFFY8OX": "O nome de usuário está incorreto", "FEiCv02i2fFwURArNjISu": "O número da conta está incorreto", "YTPo1eg1Yz7EQvr72KecO": "Recomenda-se consultar o banco recebedor ou ajustar a forma de remessa", "I1GGIAAo0PS8mfokKAxQO": "Por favor, insira o motivo da falha", "21imeJSSKJ9gbnFeLZBAO": "Auditoria de retorno de câmbio", "DZzQ1lUL4GRdGJWqYGSYd": "retroca", "xdADy1Z3oZAlvbj18SpWe": "não reembolsável", "2tHLcFI4qtrbhNizgLM_e": "O cartão bancário é classe II/III, que ultrapassa o limite de cobrança", "GVKUFE9vSgm2vThS0OcLb": "Conta empresarial", "2xItceXA56C70CviN7hdw": "<PERSON>ta pessoal", "LZhiHlMRIm8k0EoZom6Tx": "Re<PERSON><PERSON>", "WYfBrkaFrjnSb4zcrXvXP": "<PERSON><PERSON><PERSON> bem-sucedida", "uC2ieR0grcypLFaU4Cin-": "Falha na retirada", "fiYBECKlqfQ37zsh0oe3e": "Taxa de retirada", "_ex7EzDE-2SAKg28OfvjC": "Tipo de conta", "rcNxw-zV_WkSiLzT52c7F": "Conta de auditoria", "YffYxg-01J2YhI9tvT13w": "Fatura especial", "uytDBRHTcdWnmzmK6fKY0": "Progresso do processamento", "PiElRejZ-v3cSTBDm5qv5": "Solicitação de retirada", "uj4JVD9nGaVo7z7c3-uH9": "enviado", "DLKB29kasijsA6oH3Hi1h": "Retirar dinheiro e transferir fundos", "5U7GHtibp0j-Y7dysyT4-": "Verificar recibo", "VYdJLhD50Dg1KJJMCmDFZ": "Registro de alteração de conta", "yqvaBjHTsKmA2LqSHAMJ3": "Por favor, insira o número do bilhete", "2dcfGeIDG9wGif_SW6SCm": "Informações da conta", "xM9JFuMrJJaalkJwLOYoj": "Fatura eletrônica", "RC9ijDhKKlVpzWTJ_XeCd": "Forma de pagamento", "k6EP4_ZJK39lzc9AwkWvT": "Conta separada", "7lftycmlnIBPgSygUr96m": "Re<PERSON>rada recebida da conta", "S2TQ3uhLwMNpAtrlBHHLZ": "Distribuição off-line", "LTDA3b7Un6GgIPY4leo_H": "Retirada off-line", "HrJ-Uh34Wr3BSUdYA4iZ4": "Comissão de retirada", "YdSMTbOvyY2TixaUAcudm": "número do registro", "by_j84zg2KXDekxc0ORKK": "Categoria de posicionamento de celular", "WNFXoZ2k0AQRuxAeHiePH": "Categoria de pacote de presente", "1Lm2fks-D28PGJDB2-QEm": "Registro de presente", "NSVI24la1iHcxE77XkD3j": "Insira o número do pedido", "LbFrBk6riSeMQhZfbSFBU": "Número do pedido associado", "24jZw1sAUlrUcHXVqC8nl": "Número do dispositivo antigo", "ljiVq_vz1IcyxSBvLz5n9": "Novo número do dispositivo", "BLHhC-hCNlmfdLUyrpBGE": "Transferir con<PERSON>", "dCzQ4KFY-2pAvEdvMcXbd": "Itens de função", "UTq55YDmzFRzm3R7o5pS3": "Tempo efetivo", "fcR_eRPV_vuaCod-X3pMK": "saldo restante", "eg65yL7TTeZtMUi-SI7Rf": "Transferência de pacote", "9NTH28CxVugtC2EK38qjg": "Pacote de presente", "YeAgt-s65pnisd3GVw5cZ": "Cota de presentes deste mês", "8HGMdlzSUPltolCP43dQ3": "<PERSON>do restante deste mês", "e-k-39po_d2imfFB9rqcg": "WeChat Pay", "-6DPwMdNy_QQpwuo7kuOh": "total", "OVTUT6W1T8c44aoqR3P6N": "Selecione a forma de pagamento", "XhBA-h70LpYFm5FvDEQZD": "Digitalize o código QR para pagar", "MKDFByNhuA4Q6lc7kKGnD": "use", "h0svoUIo43SfkW63bLk3W": "{0} vezes", "VgvWw-6git1MC9SgonQOH": "O número de dispositivos é maior que o restante! digite novamente", "BcqbA7moiyqrLmNgNYVy5": "Insira o número do dispositivo antigo", "BkVM3eOcVuzpNIfjm-pkB": "<PERSON><PERSON><PERSON><PERSON> vin<PERSON>", "ifvJXu_6FIFkEmAwGXVGL": "<PERSON><PERSON><PERSON><PERSON> {0}", "1ULnqJ-kbF80fAeQeJcku": "Por favor, insira seu endereço de e-mail registrado", "MkwKY8UFV5BYmGR2Mxodq": "Erro no formato da caixa de correio", "jhesBFPUhQElo899N45RY": "Endereço de e-mail do usuário duplicado", "Ef1mk9PHB8ODRFJoczZKV": "animal de estimação", "oEDqfKAIpGPQkQG8CGkgy": "bicicleta", "aLxS4NInZyaUxSNvXTJB9": "<PERSON><PERSON><PERSON><PERSON>é<PERSON>", "3q5qw05Wj8yWZbxMIrGZp": "Gerenciamento de GeoKey", "taCCGgf3d8bb84p3_id3E": "Valor chave", "qxIADlgwah-virZ8nR6iu": "Período <PERSON>ta", "Y66QsIM1QtLdK5WM2rd3D": "Excedente de <PERSON>", "LGHa7JNWZdeF1_LXIkraR": "limite", "uIEW8VtyZTL8vSRQuCNDv": "todos os dias", "H9MpmjLPEK7MSh_UDrxrI": "Li e concordo", "kTLDGY0XzS0m8E0FCTjgU": "Nova ligação", "tHuKOisIETCyXaBDTuGJj": "personalizado", "fnkuxm9_NrkH8_Tv3YleO": "Nome da chave", "j7YuqwDqRlF0IjWiuKlba": "Google", "FTIf59K9kivPSetxIUrNf": "Tencent", "aIBhMRPmLVzRG-pQb1Mo9": "Baidu", "CHuYEEkDGK4YxGo8MurjZ": "não personalizado", "FXXEmOPHqITyLJvOCEuUC": "Nome da chave", "pk3H6woWKDwAEch-Xwb41": "Configurando usu<PERSON>", "PZwpizZ-BjCmgRnSp048L": "<PERSON>e ser excluída a chave: {0}{1}?", "Jm91_vcsOlbdLFr9M28Qu": "Usar a API de geocodificação permite que você use o serviço de resolução de endereços do Google. Se quiser continuar usando o serviço de resolução de endereços do Google na plataforma, você precisa inserir sua chave de API de geocodificação na plataforma. Você pode se registrar e obtenha sua própria chave no console da API do Google. Você pode definir um limite de acesso gratuito pessoal com base em sua cota. Você será cobrado por exceder o limite gratuito do Google.", "FRbY0el5plNTH4CH8dDPT": "Padrão de cobrança da API do Google Maps", "9mmurV7wHvpIeR7nUXj2O": "valor chave", "ZidcfkEaoaegWCkr25Ddp": "Grau residual", "F2g2pJ6QAesDcCFK_8Ppv": "<PERSON><PERSON><PERSON> ou não", "SEpOsvkTJIs7rq1SuxBWL": "Tipo de chave", "JVS1v5gcUSn5hMyjRZiMe": "Por favor, insira o nome da chave", "cD5nN5W2nGnv7ehPyw5fr": "Por favor, insira o valor da chave", "26CTHIClRcVCK1bBRwhnj": "Por favor, insira o valor da chave", "zoGrf2ereLlxNVDFzTq3t": "Por favor, insira a contagem restante", "SXBcD_L6s79BG2r1rbApf": "Sem limite", "wOFW2Ax2skLKfvbu1gLQt": "Você pode se registrar e obter sua própria chave no console da API do Google. Você pode definir um limite pessoal de acesso gratuito com base em sua cota. Você será cobrado por exceder o limite gratuito do Google", "tjCzMH4WONHn5m1VGfzTQ": "Por favor, verifique os principais termos de serviço da API do Google Map", "SxnXfOEYbbEoCICpC21pX": "Selecione um cliente-alvo", "unLEXxQ5Se8zqvgBrB8Qc": "Gravando classe de consulta", "DFz6LZXDUzGC9h1Hnul2u": "Por favor, selecione o tipo de pacote", "KFCuQkp2b0zldaAxmGwgB": "E-mail não registrado", "gy8pmlehGRoLkJdV_edY8": "Consultar alarme vibratório", "wlukedrwDRyOK0XeIkhZO": "Status atual do alarme vibratório do dispositivo", "HihkzR3INnfDv_bS0ml8x": "Modo de posicionamento de consulta", "Ppa_zXemQO2ey5qX_mzXm": "Consulta SOS", "-S5Zj2mRhidmTd8IcTPAF": "Consulte o método de posicionamento do dispositivo atual", "dvr_kBxVSCo9O4skA53qY": "Consulte o número SOS atualmente definido", "rbAN2_LHXoCO3Rj9ifHHq": "Modo de posicionamento de tempo", "CEQE89HjKKDIM5xBDWBA8": "Modo de posicionamento inteligente", "OPHvVo3S-kkq8EJfg7gSZ": "Modo de super economia de energia", "j-SWrpc1Vrf1S2wKVXsrg": "Modo despertador", "ILxJ2c9W9RlSgcyXJU3AI": "pagamento bem-sucedido", "sZW0PuFvcmAVNmno6QOc7": "Conta", "mLKZ7cU5tvQwf6tn5ddby": "Por favor, insira o novo número do dispositivo", "xZblAEf-Ca5QidcBCgFWI": "Não é para presentear", "l9KpGab_FPNpBWZDNAWrF": "Tem certeza de que deseja transferir o pacote?", "9ti28ZU5Zt40xby1G_ahv": "Cota de presentes deste mês = número total de dispositivos ativados na conta * proporção, se for menor que 10 vezes, o padrão é 10 vezes", "aizY__cr8e9LMRXQUTIgf": "Número de dispositivos ativados: {0}", "ux9Kw9v1rmHt6LahAEhBK": "Proporção: {0}% atualizado no primeiro dia de cada mês", "bSBDQVvDZ5-2m1m1b0X38": "Ren<PERSON> acumulada", "vx8cJaHHxhErDo_u98V9g": "Taxa", "wvZQ-g667pqaS5k7ElKBJ": "Número do pedido associado", "SCx72TSY1VcnknS0ks5lJ": "Saldo total", "DeCU3YJEQZ4m6WTKYOimH": "Configurações do sistema", "JgTflL8nrYglkCeN2xVKk": "Unidade de comprimento", "uaDXv6VTf_i6KFdDpiA5b": "Quilômetro (km)", "oz1Fpf2Cy2REH5VddyL87": "Milhas (mi)", "_Mb6dEr1YRXYFPEh11ycQ": "Pé", "TYzDOtOg8b-lSYzNRPTjQ": "<PERSON><PERSON><PERSON>", "machineTypeSupport": "Suporte de modelo", "aUEMcGA3UzcsMsybrAd-j": "estado ativo", "tCdgp3PrZMzQB5QsVmyih": "Número de celular vinculado recentemente", "KeCWjWFz4-VIYEIeukzc5": "Hora da última ligação", "S741RuNHsAsXlx43GUApW": "Pedido cancelado", "kIM7VbZMekH-KO6wIHSqh": "Tempo de cancelamento", "WRHOzHvIYZ7yQT7MYxaBw": "Quer comprar um pacote", "LNt5xB9FS0cjz8sZxzrB2": "Detalhes do pedido cancelado", "8ki2CCZoENshLvjE7QLGh": "ter", "5Sv5ytuUMlSPalt5usauH": "Tempo de importação de equipamentos", "7HL8IXRIIp5cKUnInarno": "Status do razão", "J0UYZYck8uhBvlodD6N4V": "congelar", "W7PEgcWDx2hes2lBqOX4u": "<PERSON><PERSON> chegou", "91_1vPtPXMzqa7sdwnbWw": "Montante recebido", "8cpgA7arN32LnJYYoHs6b": "O valor congelado consiste no valor da conta congelada e no valor do saque congelado. Clique no número do valor para ver os de<PERSON>hes.", "iZ6Q-Cqe20id8Jqqi-n28": "A autenticação de nome real pessoal ainda não foi aprovada, entre em contato com seu provedor de serviços superior", "IpRD_Ggq8UhJWAyYyRTo4": "Vá para o nome verdadeiro", "1xXJa_y9Dj73JgvFgLmpY": "{0} autenticação de nome real ainda não foi concluída. {1} só pode ser retirado depois que o nome real for autenticado. Você quer autenticar?", "7R8ninLk0Y8vd9nEBvIPz": "empreendimento", "oYXW0p5J3DkyunUzLbpq3": "pessoal", "3bYcoGZhmlMZGYFqPsQMI": "Congelar o valor dividido", "DPJPrWEMHEaNY32Puj9ld": "Para garantir a segurança dos fundos, o valor da conta obtido será congelado por 7 dias. Após 7 dias, o saldo disponível será inserido automaticamente antes que você possa sacar dinheiro.", "1LeLeSLQG4tH3Mb7oPHNA": "<PERSON><PERSON><PERSON> valor de retirada", "24RGgbcmuZQWvxaEek6P9": "Compensar a diferença (imposto)", "cbV0W15tenwiobhwsxGKS": "Devido às exigências da administração fiscal, nossa empresa deverá pagar 6% de imposto sobre valor agregado. Caso o prestador de serviço forneça fatura com alíquota de 1% ou 3%, deverá compor até o valor do imposto sobre valor agregado de 6%, que será deduzido do saldo da conta.", "MxKJZnXtEbLQy_CJM4Wo0": "Faça upload de ingressos especiais que cumpram as regras", "LgRB21jCFOEI2iFWMhk_u": "Por favor, carregue um ingresso especial com o nome do projeto como taxa de promoção", "fugpgJ6HPD0IiniTJJsu3": "Nome", "wxLv0t9cVv88YKsE56eV0": "Autenticação de nome real empresarial", "QLw5b0HuFkQor0jnyYnDQ": "Autenticação pessoal de nome real", "S_3XDTwVR39xyBxoDNayv": "Faça upload do seu cartão de identificação", "YoC4H8soU_i0YdFgPxxDM": "Por favor digite seu nome", "Zxsxh9PZBmwPf4rykaJF8": "Para garantir a segurança da conta do fundo, o prestador de serviços é obrigado a completar a autenticação do nome real. Se o nome real não for utilizado, a operação da plataforma não será possível! O nome real pessoal suporta apenas saques pessoais. Após o envio, ele precisa ser revisado pelo prestador de serviço superior. O nome real será considerado bem-sucedido somente após a aprovação na revisão.", "tCAlTVE55bteYRYT6gDbc": "carteira de identidade", "N9O3C4bv7mci7eE1eYJTp": "Por favor insira seu número de identificação", "IjrAT01SxjPX4qgbJDgmQ": "Atualmente não há verificação de informações, adicione-as primeiro", "lDKrZEHddhAqje9P-dx2D": "não certificado", "ACgMBoX8dES2ll2wNJlXu": "Certificação em andamento", "_6ZBaztE9i7WPTNBpU-JV": "Falha na autenticação", "NWB1XTgWnDZdVXApDMGP_": "Em análise pelo provedor de serviços superior", "eeLhrjq3zdRq8RmncZ7Re": "Falha na revisão", "f2J3ZgEdW0dBUMf-usvcc": "Certificação", "Mbh9kHVbIMxc_aykJBsDr": "re-certificação", "wPblFbX-4CmURcOYNxNPM": "Ver exemplo", "-UdcRA2f8GflwWkRatINL": "Revise o número do pedido", "KT8cy99eeIOwTPqi3IjgK": "Foto de identificação", "2B9eM3k51NkPbpIJauFfj": "candidato", "V5V_Z02EeDLZJVkjL5yB9": "revisor", "H_WXP1q8rO_zGjM7z2l0R": "Parecer de auditoria", "DgQ6Po_e9_AEGCu1oDKXl": "Status de aprovação", "KlZQCdiEceUY54vxcapV-": "Nome verdadeiro novamente", "r4G785OKstjScU_Nw3EzO": "A autenticação de nome real está em análise. Não a envie novamente.", "GWiBNGprbSzdB2Z986MsC": "A autenticação de nome real foi aprovada. Não envie novamente.", "oJu4xk2bHPcEt3j1zcnqU": "status do aplicativo", "i-SpeLRCOCbLglqTGD0u-": "sob revisão", "OUv3PKk3G_Cc_gdEyENKW": "exame aprovado", "m9T-6rAOChrVMG0YDV8CF": "Auditoria não aprovada", "GrOaJg9APCHrf5k--_U_D": "passar", "WuZQiTqcBj7cGod0nj7LO": "<PERSON><PERSON><PERSON>", "-NuVs-wS7J4GYTOwjFuMh": "Comentários de revisão são obrigatórios", "et_LYLHFrM04XhGjP46tP": "Insira a taxa de compartilhamento da conta", "K-fRsoopgmoiVxQIwvZFl": "A proporção de compartilhamento de conta não está dentro da faixa distribuível", "lEttzd9lDWHeMmVtBmrNU": "Revisão pessoal de nome real", "QHCtboh3seN6ui33CZmWz": "tratar", "OLvc_EzZFYBlBhNIXnjZD": "Nome verdadeiro da empresa", "Whfkt63jRh6VQ4s6QphEa": "Nome verdadeiro pessoal", "sIND263kTy5EADi1pj1pP": "Re<PERSON><PERSON> conta", "specilUser-enduser": "usuário final", "lhZ_M3uoptVK76DILnZ2l": "De acordo com as regras operacionais da APP, o preço de venda não pode ser alterado", "regaliErrorTip": "Giveaway falha, falha do dispositivo foi baixado como uma tabela", "bOBwT_f2edDC27PM3NQgy": "{0} itens selecionados", "Gad1mLkSTY0eVBR4fnhNI": "lista de cerca", "_uAM6l-rr2rggms5zdveJ": "Cerca selecionada", "AqxN0F8RcW0DD7-uAWTI5": "Selecione o cliente", "evYC9JPWCRXiYcDMbZvWx": "informações pessoais", "NoFYSU79Nfj9dt-xcgrQ8": "O formato do formulário está errado. Consulte \"Modelo de formulário\" para fazer o upload novamente.", "xbF35iFGLVAJnswRVH0-u": "Associação de dados bem-sucedida", "Bwg2kt_P1yhAb5-hjPOgO": "anormalidade de associação icídica", "ek_N_GSAZAG9NyQXKbnJE": "Algumas associações de dados são anormais. Consulte o formulário de download para obter detalhes.", "GbKT46C8x66GgpjgBjOqn": "O tamanho do arquivo não pode exceder {0}{1}", "SwOWzfqnAh0jCThFr4A6v": "{0} arquivos selecionados", "4dGkAxc8S8l2COYbcDF-g": "Baixar modelo", "s02oAL3-DrwNUsBNxn6Bw": "Para evitar falha na associação, preencha o IMEI e o ICCID conforme modelo de formulário. Não são permitidas mais de 500 associações de IMEI e ICCID por vez.", "BMMOrNpblCgFpy0mjOb1Z": "Por favor, carregue os arquivos. Somente arquivos nos formatos xls e xlxs são suportados. O tamanho do arquivo é ≤100 KB.", "c7A7o4pVd1rpivRvna5Gh": "Associação de lote IMEI ICCID", "4weboexar_U03Ho0q5Shn": "A associação de dados está em andamento. Não opere a página", "3JZI34eFq9qolsBRyHs7W": "Dados do usuário_{0}.xls", "zdWLkJqxc7V4XODM1RAbn": "carregando", "remoteControlTip": "Quando o tempo da desconexão do controlo a distância é ajustado vazio ou 0s, significa a desconexão contínua", "remoteControlOne": "Controlo a distância 1", "remoteControlTwo": "Controlo a distância 2", "controlTime": "Controle o tempo", "restore": "recuperação", "registrationTime": "Tempo de inscrição", "equipmentService": "Classe de serviço de equipamentos", "softwareServices": "Classe de serviços de software", "deviceActivation": "Classe de ativação de dispositivo", "setProfit": "Definir a proporção de hidratação", "packageIncluded": "Incluído no pacote", "serviceAddTips": "Subseqüente provedor de serviços de classificação será realizada de acordo com a proporção apresentada, confirmar a apresentação?", "ZEGgtLzv8abXPKrlPdzUC": "Renovação automática", "ADC7SxVEyPAWy5cdn2lKb": "Após a modificação do preço do pacote de renovação automática, as deduções subsequentes serão baseadas no preço modificado. Confirmar a modificação?", "EFUKd-0PFs2kje_Z0c1h9": "avisar", "9G-UExFllfV8X9i978qOe": "Após a retirada do pacote de renovação automática das prateleiras, nenhuma dedução será feita quando o pacote atual do usuário expirar. Tem certeza de removê-lo?", "gou": "cachorro", "mao": "gato", "ucYI8ufupwygMJHhm1DQU": "Aviso de informação de alarme", "desc": "Significa que", "driverRequired": "Nome do condutor- campo obrigatório", "driverRFIDRequired": "Número do cartão do motorista- número RFID, preenchimento opcional", "connectRequired": "Formulário de contato- preenchimento opcional", "emailRequired": "Email- preenchimento opcional", "remarkRequired": "Observações- preenchimento opcional", "GrQZ6_ZOP74ym8FTQlT6z": "Pago, esperando para ser creditado", "S6Pgt6-_Vw551atSMNdO9": "Falha na transação", "enoughTip": "Cartão vitalício insuficiente", "package": "vers<PERSON>", "standardEdition": "Edição padrão disponível", "freeVersion": "Versão gratuita disponível", "ImportVersion": "Versão para importação", "serviceCycle": "Período de serviço", "standardEditionTip": "Edição standard (todas as funcionalidades da plataforma estão abertas)", "freeVersionTip": "Versão gratuita (os recursos básicos estão abertos para a vida)", "UpgradeBatch": "Lote de atualização", "DeviceVersion": "Versão do dispositivo", "remove": "remover", "ServiceVersion": "Versão do serviço", "baseTip": "As seguintes funções básicas estão abertas para a vida", "nvx3Eu71rcjx9HzshOkfZ": "Ponto de importação de um ano", "3umrb4aXbfmzceFs9R4Lp": "Actualizar", "baseVipTip": "Posicionamento em tempo real, cerca circular, playback da trilha (7 dias), instrução do equipamento, visão geral da operação, visão geral do alarme", "upResult": "Resultados da atualização", "upToastError": "Por favor, adicione um dispositivo de atualização", "runTimeAll": "Duração total da viagem", "distanceAll": "Total de viagens (vezes)", "popularModels": "Modelos quentes modelos", "otherModels": "Outros modelos disponíveis", "6sWTyQZG3FPA7c6FT3VyC": "Estatísticas de umidade", "b7e_Uxgecy2CNFWjkOaJG": "Gráfico de análise de umidade", "DWo-vUEFZ0QNf-S0qokc1": "Status de posicionamento", "rnIm0BaHQUeveQZGjQ5-y": "É posicionamento eficaz, dispositivo prendido, mostra se conectado à eletricidade externa", "x4U0kj1KZo88WNcFhVS-2": "Estação base/GPS/WiFi/Bluetooth", "LgYLxSVSYDF0RSJiIbO6O": "Sinal de força", "4mc0BBksNeCOzN13HG2Kq": "Status do ACC", "vEYkN4a5nw7Mvi0ycE09T": "Hor<PERSON>rio de início e término", "accTotalTime": "ACC duração total do fogo", "VjZM92-uovWbEuTGQX-Qt": "Redefinir em", "ONfwsGz2suFI3faXq7qOq": "Um sucesso parcial", "MGzpzjvhwPnlJ2_B_WuwB": "Redefinir o estado", "D18M1wrudyshM_m7yv-nZ": "Redefinir um registro", "upgradeTip": "Após a atualização da versão do dispositivo, você pode experimentar mais funções! Versão traseira não suportada após a atualização", "lycRATEE2FhUXs2E-l_NW": "Gravação de áudio", "q-mRStgBB2oIPAQj7EXBt": "Duração da gravação", "-SbPXykJz9GwiZN2raM0L": "Foi removido de", "modelConfiguration": "Configuração do modelo", "modelManagement": "Gestão de modelos", "modelName": "Nome do modelo", "category": "categorias", "myCompanyTip": "Se a categoria do modelo é nosso modelo, os usuários podem escolher este modelo na preferência ao importar o dispositivo. A lista do modelo é mostrada primeiramente para a seleção. É igual aos modelos quentes e pode ser aberta somente para o usuário do alvo", "companyModel": "Nosso modelo da empresa", "baseTypeTip": "Se a categoria do modelo é modelo base, o usuário pode escolher este modelo preferencialmente ao importar o dispositivo, aberto a todos os usuários", "baseType": "Base modelo modelo", "modelId": "Identificação do modelo", "port": "port", "videoEquipment": "Equipamento de vídeo", "modelAlias": "Nome do modelo", "originalModel": "Modelo modelo original", "domainName": "<PERSON><PERSON><PERSON><PERSON>", "generalAlarm": "Alarme universal para", "超速报警": "Alarme de velocidade excessiva", "静止阈值": "<PERSON><PERSON> repouso", "离线报警": "Alarme off-line", "ACC报警": "Alarme para crna", "疲劳驾驶报警": "Alarme de condução fadiga", "怠速报警": "Alarme de velocidade inativa", "温度报警": "Alarme de temperatura (apenas PC)", "低压报警": "alerta de baixa <PERSON>", "离线判断": "Julgamento fora de linha", "录音": "gravação", "追踪(秒定)": "Traçado (em segundos)", "温度感应器": "Sensor de temperatura", "监听(视频)": "Es<PERSON>and<PERSON> (vídeo)", "对讲(视频)": "Intercomunicação (vídeo)", "油量设置": "Configuração do volume de óleo", "车况": "Um carro", "I/O设置": "Configurações de e/s", "实时视频": "Vídeo em tempo real", "视频回放": "Reprodução de vídeo", "companyModelMessage": "Nosso modelo, o usuário pode escolher este modelo na preferência ao importar o dispositivo, a lista modelo mostra primeiramente para a seleção, igual ao modelo quente, aberto somente para o usuário do alvo", "baseTypeMessage": "Se a categoria do modelo é modelo base, o usuário pode escolher este modelo preferencialmente ao importar o dispositivo, aberto a todos os usuários", "customModel": "Personalize o modelo", "timedTasksTip": "A tarefa de agendamento foi configurada com sucesso para {0} dispositivos. Os resultados da operação estão abaixo", "timedTasksToolTip": "Intervalo de tempo de desligamento de óleo, executar a instrução de desligamento de óleo concluída, desligamento de óleo automático, aguarde até o tempo de corte para terminar a desligamento de óleo, executar a instrução de abertura de óleo, abrir automaticamente o óleo", "设置结果": "Definir o resultado", "批量设置定时任务": "Configuração em lote para tarefas cronometradas", "定时任务设置": "Configuração de tarefas cronometradas", "执行类型": "Tipo de execução", "日期范围": "Intervalo de datas", "选择日期": "Escolha uma data", "时间范围": "Intervalo de tempo", "是否开启": "<PERSON><PERSON><PERSON> ou não", "选择时间范围": "Escolha um intervalo de tempo", "请选择执行类型": "Selecione um tipo de execução", "kHt0e3FCxBfFaaS8fs2o9": "Na cerca sem energia óleo", "QmAH9PaQGsFNjcHUM3n5d": "Na eletricidade da ruptura do óleo", "eL2A3XxsMOLN0LfDsxexr": "Deixar a energia de corte de óleo, entrar na energia de corte de óleo, só pode marcar um", "cNZ24bjJRWQo6Y-kbZs7V": "<PERSON><PERSON> incorreta, por favor, digite novamente", "rzbvqLXmVrJcwetdcqj1N": "Esta configuração de permissões de usuário já existe", "vjQ5tSfzfzJZkLEG4pRuV": "Informações para animais", "-QiINVEAEQs1lOyxoY2-U": "mestre", "V7ZEeKcoq4ZacqGumFBso": "Nome do animal", "EF_z9YeHVGT1BuC0Xxt9R": "Endereço da casa", "BpPwm6MYFDChvmC_ZNEFm": "O intervalo de tempo máximo não pode exceder 7 dias", "0j5o0HN0PNLvrneCrbb43": "Os campos básicos", "e-YOvR7f7jEgWnfpsPHIN": "Última informação de energia", "1DjgiuZQ8BexU2yv9t_wp": "Última informação de tensão", "5JuYDTW-zrpUmNxJw_nSd": "O campo base não pode estar vazio", "受限驾驶报警": "Alarme de condução restrita", "受限驾驶报警提示": "Ative para disparar um alarme quando o veículo se move durante os horários definidos", "操作结果": "Resultados da operação", "sNuyqv3TTfegG8OtSl7nU": "Por favor, selecione/insira a conta do prestador de serviços", "7kCT4o4MOEYlu09rJb2Q6": "Conta do fornecedor de distribuição", "xUIxHiKX3S634h2ZT-Hei": "Por favor, selecione/insira o número da conta do provedor de distribuição", "dC7w9wyuS8ypXXErkJ-QS": "ID do provedor de distribuição", "-gm8iiFKWgzDomRd-mtiS": "Caixa de correio push", "rmrECn8K92VTFKS9hwdkb": "Após a abertura, o usuário irá empurrar o conteúdo do feedback para o e-mail especificado depois de usar a função de feedback", "qzvBp0BIfI3fGjokogkL0": "Você pode inserir várias caixas de correio, com símbolos; Separem-se.", "tQHLaMWzMo2JN4I2BZ1jm": "Configuração de feedback", "员工号Tip": "Número de funcionário- preenchimento opcional", "身份证": "Número de identificação- campo obrigatório", "群组名称Tip": "Nome do grupo- selecione o campo", "身份证号": "Número de identificação", "员工号": "Número de funcionários", "群组名称": "Nome do grupo", "3000Tip": "O número de dispositivos verificados superou 3000, por favor, marque novamente ou exporte em lote", "团队": "equipa", "地区": "região", "库存仓": "Armazém em stock", "团队Tip": "Equipa- campos obrigatórios", "地区Tip": "Região- campos obrigatórios", "库存仓Tip": "Armazém- campos obrigatórios", "确定删除吗": "Excluir com certeza", "防盗拾音提示": "Intervalo de tempo recomendado para seleção não superior a 30 dias", "录音勾选提示": "Por favor, marque o registro de gravação em primeiro lugar, antes de realizar esta operação", "录音提示": "Apenas arquivos de gravação com status normal podem ser baixados", "防干扰报警": "Alarme anti-interferência", "77_wwzNTTHafPEOC0QLk-": "O login com a senha inicial da conta IMEI foi detectado.", "b3aU4VIlinEYLMqxOzRUw": "Para evitar vazamento de informações do dispositivo, altere a senha agora! Se as informações do dispositivo vazarem sem alterações atempadas, a plataforma não assume a responsabilidade legal relevante.", "zflDZ6jTRVCKTu5u3tpKp": "Clique para mudar sua senha", "8pEMx5x-VOk4MOLoRZRuv": "carro", "aBF16On3BfsZNCUJ7A4yI": "piloto", "BDiR1RKwGe_Gkkjd42Suc": "Dentro do carro", "aaRIAHINadX98uPLHcfWD": "carruagens", "OkTXgR5JsQCfmgiR-pPei": "ADSA", "0NL__z7IrwP4XRWDx9a8W": "DMS", "YpT7EDmLyZivmHVHmVfBV": "BSD", "2EYnIDOIt8Y1ZBMp4phrT": "Selecione uma província", "06KhnrOrA4Bs_1vwHy2w2": "Por favor selecione uma cidade", "KYOUEl9w_JqXiNDpynhBS": "Por favor, selecione uma região/cidade", "timeType": "Formato do tempo", "timeTypeTip": "Atenção, o formato de tempo de comutação que será aplicado em todos os momentos da plataforma é exibido", "24Hour": "Sistema 24 horas", "12Hour": "Sistema de 12 horas", "1zn8t2V58hA4zxG_Y7jeu": "O criador", "s4Towo3SLRRMYC574wkLg": "<PERSON><PERSON><PERSON><PERSON><PERSON> da f<PERSON>a", "RDM65k10RlfRQk4fHEbjd": "Após a abertura, o limiar de alarme é inserido. Se a tensão externa for inferior a esse limiar, o alarme de baixa tensão será acionado pela plataforma.", "fCvirGBsZoJLXRJHHHZbd": "Por favor insira um valor inteiro positivo", "RqtauiLCXvZ1erD4k81hD": "O valor de interruptor do alarme não pode ser nulo.", "取消成功": "Cancelado com sucesso", "确定删除设备视频": "Definitivamente excluir vídeo do dispositivo?", "视频时间段": "Período de vídeo", "创建人": "O criador", "下载视频": "Baixar o vídeo", "10分钟提示": "Duração máxima de um download de vídeo 10 minutos", "设备上传视频提示": "O carregamento de vídeo pelo dispositivo consome o tráfego do SIM. Tenha cuidado!", "上传中": "Em upload em", "请先查询设备回放列表": "Por favor, verifique a lista de reprodução do dispositivo primeiro", "取消失败": "Cancelamento falhado", "查看进度": "Veja o progresso", "视频存证提示": "A tarefa de upload do dispositivo foi criada com sucesso. <PERSON><PERSON> tarde, por favor, vá para \"vídeo de depósito\" para baixar o arquivo de vídeo!", "时间错误提示": "Lapso de tempo errado", "仅支持查询仅一个月的数据": "Suporte apenas para consulta de dados de um mês recente", "选填项": "Selecione os campos", "必填项": "Campos obrigatórios", "更多信息": "Mais informações (opcional)", "填写": "preencher", "视频存证tip": "Certificado de vídeo: o vídeo gravado na nuvem expira 1 mês após a geração. Faça o download e salve a tempo!", "yGQ6XcdO8Sy64ocojoYZl": "Tempo de áudio original", "94iJTFe_HpSBynofR58Ti": "Classe de serviço de armazenamento de trajetória", "fsYFvBFucZiu_oWbNvZ22": "Classe de retorno da plataforma", "油量总览": "Visão geral do óleo", "偷油次数": "Tempos de roubo de óleo", "偷油量": "Quantidade de óleo roubado", "偷油次数提示": "O alarme de roubo de óleo está ativado em \"configurações de nível de óleo\". Depois de definir o valor do alarme de mudança de nível de óleo, a plataforma conta os tempos de roubo de óleo", "偷油量提示": "O alarme de roubo de óleo está ativado em \"configurações de nível de óleo\". Depois de definir o valor do alarme de mudança de nível de óleo, a plataforma estatísticas de roubo de óleo", "总油耗": "Consumo total de combustível", "总加油量": "Quantidade total de reabastecimento", "总偷油量": "Total de óleo roubado", "偷油总量": "<PERSON><PERSON> r<PERSON> total", "偷油报警": "Alarme do roubo do óleo", "油量报警值提示": "Após a abertura, ajuste o valor do alarme quando o óleo está diminuindo. Quando a taxa da redução do óleo > o valor do alarme, a plataforma acionará o alarme do roubo do óleo cada 1min", "总量": "Montante total", "加油": "Vamos lá", "偷油": "R<PERSON>bar gasolina", "油量骤减": "Redução de óleo", "总行驶里程": "Quilometragem total percorrida", "油量明细": "Detalhes da quantidade de óleo", "油量事件": "Eventos de volume de óleo", "TXWIRMSEWbj4vtHr7DUJv": "Uma sub tag", "温湿度报警详情": "Detalhes do alarme da temperatura e da umidade", "fsnMITM1wU8xV2PKzLWjZ": "Estatísticas de temperatura e umidade", "O0qntBrpdv8l2SC3pG09l": "Estatísticas dinâmicas de temperatura e umidade", "nz2_Ad5OYtByfOB1QF3Vl": "Estatísticas de alarme de temperatura e umidade", "ObM5FY6uCuadK3X9CPiCl": "Digite o nome do dispositivo", "NyEHBemDgp87PEzlnyLVc": "Por favor, insira o apelido /ID da sub-tag", "WT2DYWziS2YIrcleR2tGT": "Temperatura média (°C)", "ETgTh7hRkghCY7YNBf6I8": "Temperatura máxima (°C)", "2dy9zO3qN7emQTgGASZpk": "Temperatura mínima (°C)", "Uys4ymT9Nt1lR31ln20t_": "Umidade média (%)", "aM_5tWEss1bMoV08rT5Dn": "Umidade máxima (%)", "dVDmsVU8O1mAWZAWej8uF": "Umidade mínima (%)", "ufw1tWMtXV3ffNamDz5xU": "Resumo dos alarmes", "Fo0DGwbxnBAlVn0y2ck32": "Detalhes do alarme", "KUJ4j-738X9s_wvGrcaRX": "Número total de alarmes", "_-k0Jh2WNEm1YsmgIBnmd": "Duração total do alarme", "-F6HE4fAdtZPFvdc63MRN": "Tipos de temperatura excessiva", "QO0rjr3U04jQP_3aNJSJg": "Duração em temperatura excessiva (min)", "XghrPNgWiK990yE5fPxJ0": "Tempo de início de temperatura excessiva", "uFtXUo1GQdbTBo5ngcwx0": "Tempo final de temperatura excessiva", "lIYpSzkBAnha_7zYd70o4": "Endereço de temperatura excessiva", "fbT5E73inb0AGt14hJy4I": "Tempos de temperatura ultra alta", "Fgh4tYRD97bN5_Q2AzTWT": "Duração total em uht (min)", "bSqSifScjo2TKle1XmMKS": "Tempos de temperatura ultra baixa", "3chnb5MVuzr2-XHeDkCxA": "Duração total da temperatura ultra baixa (min)", "uGZs9lFwECt-z6ucB3Gj-": "Temperatura ultra alta", "l6q5r2x6VZq5zNJ91t7hi": "Temperatura ultra baixa", "EULZSW-P16RFs_3B-vk2x": "Temperatura (° c)", "PBPHCzoG8bgpY_K0XQs2v": "Umidade do ar (%)", "子标签": "Uma sub tag", "关联时间": "Tempo de associação", "关联中": "Em associação com", "子标签ID": "ID de sub tag", "子标签名称": "Nome da sub tag", "取消关联时间": "Cancelar tempo de associação", "标签关联记录": "Tag registros associados", "电量": "electricidade", "导入修改": "Importação de modificações", "更新标签": "Atualize suas tags", "批量设置温度报警": "Alarme de temperatura de configuração em lote", "关联记录": "Um registro associado", "温度报警提示": "Quando {0} e fora dos requisitos de temperatura, o alarme é acionado após {1}min", "取消关联提示": "Selecione os dados que você deseja desassociar", "清空设备提示": "Sim não confirmar esvaziar o dispositivo todos associados", "是否确认取消关联": "Sim não confirmar cancelar associação", "更新标签成功": "Tag atualizado com sucesso", "子标签输入提示": "Digite um ID de sub-tag em uma linha (até 10)", "子标签空提示": "Não pode estar em branco, insira o ID da sub-tag", "请勿输入重复值": "Não insira valores duplicados", "请输入正确值": "Por favor, insira o valor correto", "温度报警设置": "Configurações de alarme de temperatura", "低温报警提示": "Exemplo de explicação: defina o alarme de baixa temperatura ≤20°C, em seguida, quando a temperatura atinge-123°C a 20°C, e além dos requisitos de temperatura, o alarme será acionado após 30 minutos de duração;", "高温报警提示": "Exemplo de descrição: defina o alarme de alta temperatura ≥20°C, em seguida, quando a temperatura atinge 20°C a 127°C, e além dos requisitos de temperatura, o alarme será acionado após 30 minutos de duração;", "持续多久后产生报警": "Quanto tempo a temperatura dura após gerar o alarme de temperatura", "输入数值提示": "Insira um valor entre {0} e {1}", "不能大于180分钟": "<PERSON><PERSON> mais de 180 minutos", "高温报警值需大于低温报警值": "O valor de alarme de alta temperatura precisa ser maior do que o valor de alarme de baixa temperatura", "标签更新失败": "Por favor, atualize a página novamente ou clique em \"atualizar guia\" para obter os dados", "子标签温度报警": "Sub-tag alarme de temperatura", "XlMy7U3zd-o82bjczCtb3": "A alta temperatura", "2yW2Lw-Pm4B3CHnUv6_RI": "Baixas temperaturas", "更新标签超时": "O rótulo de atualização expirou. Por favor, faça novamente", "最大的范围不能超过三个月": "O intervalo máximo não pode exceder tr<PERSON><PERSON> meses", "指令下发失败": "Falha na entrega de instruções", "标签导入修改成功": "Importação de tag modificada com sucesso", "标签导入修改": "A importação de etiquetas não foi modificada, por favor, faça novamente", "子标签取消关联成功": "Subtag desassociado com sucesso", "子标签取消关联失败": "A desassociação de subtag falhou, por favor, opere novamente", "标签更新成功": "Tag atualizado com sucesso", "取消删除": "Cancelar e excluir", "取消并删除": "Confirmar cancelar e excluir esta tarefa", "取消并删除tip": "Cancelar e excluir com sucesso", "确认删除": "Sim não confirmar a exclusão desta tarefa", "任务添加完成": "Tarefa adicionada concluída", "批量下载任务": "Tarefas de download em massa", "任务名称": "Nome da tarefa", "请前往任务列表查看": "Vá para a lista de tarefas", "执行时间": "Tempo de execução", "音频包数量": "Quantidade de pacotes de áudio", "任务ID": "Identificação da tarefa", "数量已超过15条": "Quantidade já mais de 15", "标签更新超时": "Por favor, atualize a página novamente ou clique em \"atualizar guia\" para obter os dados", "请输入任务名称": "Digite o nome da tarefa", "名称不能超过20个字符": "Nome não deve ter mais de 20 caracteres", "音频": "<PERSON><PERSON><PERSON>", "取消选中": "Desmarque a seleção", "是否确认取消选中": "Con<PERSON><PERSON>?", "已选中": "Já foi selecionado", "ACC开启": "ACC está aberto", "ACC关闭": "ACC está fechado", "nhLyAGMQLMPMAnlog-vFL": "Ao gold", "KOTAIifGVE4RneCwmKmqS": "Quando a<PERSON>, quando o dispositivo relatou a quantidade de óleo dentro de 5 minutos é inferior à quantidade de óleo do alarme, a plataforma empurra a mensagem de alarme de baixo óleo.", "低油报警": "Baixo alarme do óleo", "X6lIcAfgOp1sXkdgE_9OW": "Por favor, insira o baixo valor do alarme de óleo", "62qJdcSuv6iGqUcKFNdY3": "Números inteiros positivos de {0}~{1} necessários para o baixo valor de alarme de óleo", "低油报警值不可大于满箱油值": "O baixo valor do alarme do óleo não pode ser maior do que o valor do óleo do tanque cheio", "套餐版本": "Versão do pacote", "子音频名称": "Nome de áudio filho", "子音频时间": "Sub tempo de áudio", "合包记录": "Registro de fechamento", "3qycxyVXHpDIRbCj9IK8c": "Tempo de validade", "uQX-Oo4jyWWmFhVUIFDZa": "Salvar e publicar", "9HvYA8H1k0s5oYPTo_TRz": "O conteúdo do anúncio não pode estar vazio", "6mWY-LWd-3OLeGOmBOlvR": "Por favor selecione um idioma", "EcwH1eTZ4h47qTr03bT2-": "Por favor insira um título", "M27dQ-Dpq2WkJc3ntw1gg": "Tenha certeza de excluir o anúncio", "Xv7QA7Z7u7lCYx4f7NmWs": "Selecione uma plataforma para notificar", "是否解除绑定": "Desvinculação ou não?", "以下设备不存在": "Os seguintes dispositivos não existem e não podem ser adicionados", "新增设备": "Novos equipamentos adicionados", "批量解绑": "Desamarração em lote", "默认": "<PERSON><PERSON>", "瞬时速度": "Velocidade instantânea", "机油压力计算": "Cálculo da pressão do óleo", "节气门位置": "Posição do acelerador", "瞬时油耗": "Consumo de combustível instantâneo", "空气流量": "Fluxo de ar", "发动机实时负载": "Carga do motor em tempo real", "算法校准": "Calibração do algoritmo", "油量tab提示": "A plataforma calibrou os dados diários e regulares, e os dados retornados do volume de óleo foram consultados, incluindo os dados “calibrados” e “não calibrados”.", "油量tab提示Remark": "Nota: pode haver algum desvio antes e depois da calibração dos dados. Por favor, analise em combinação com o uso real", "油箱": "<PERSON><PERSON><PERSON><PERSON>", "关": "fechada", "gQI5MVNOUbZlMcC0kHzKK": "Status da ligação", "iVMYDWp97LRmLGrbbnnsD": "Ligado", "Oeez9vYceazcpBOlH3Xq-": "Não consolidado", "油耗": "Consumo de combustível", "请切换至任务列表下载文件": "Por favor, mude para a lista de tarefas para baixar o arquivo", "导出任务进行中": "Tarefa de exportação em andamento", "网页过期": "O link da página web expira após {0}.", "统计时间": "Tempo de estatística", "ExtensionTip": "De acordo com diferentes cenários de aplicação, os campos de cada relatório podem ser combinados para exportação", "统计": "estatística", "总加油次数": "Número de reabastecimento", "总加油量(L)": "Total de reabastecimento", "总偷油次数": "Tempos de roubo de óleo", "总偷油量(L)": "<PERSON><PERSON> r<PERSON> total", "油量分析": "Análise de volume de óleo", "设备名称不能为空": "O nome do dispositivo não pode estar vazio", "监控已结束": "O monitoramento terminou, clique em atualizar para continuar jogando o monitoramento", "唤醒失败": "Falha ao despertar, tente novamente em alguns minutos", "限速": "O limite de velocidade", "距离": "distância", "speedAlarmTip": "Quando a velocidade excede o limite definido, o sistema acionará o alarme de velocidade excessiva e repetirá o alerta em intervalos de 1 minuto. O alarme permanecerá até que a velocidade seja desligada automaticamente depois que a velocidade estiver abaixo do limite", "speedAlarmToast": "Suporte para consulta de dados de ontem e anteriores apenas", "durationTip": "A duração é um inteiro positivo", "riskLevel": "Classificação de risco", "deviceTotal": "Número total de equipamentos", "deviceOnline": "Equipamento de vídeo online", "riskEquipment": "Equipamentos de risco existencial", "highRiskEvents": "Eventos de alto risco hoje", "mediumRiskEvents": "Eventos de risco médio hoje", "lowRiskEvents": "Eventos de baixo risco hoje", "bigFullScreen": "Monitor tela grande", "notProcessed": "Não tratado não", "processed": "Em processamento", "resolved": "Já foi resolvido", "lowRisk": "Baixo risco de", "mediumRisk": "Risco médio risco", "highRisk": "Alto risco", "batchProcessing": "Processamento em lotes", "handleThing": "Selecione os dados que deseja processar", "handleContent": "Conteúdo em <PERSON>a", "handleType": "Modo de tratamento", "handleResult": "Resultado do processamento", "handleOk": "Processamento em lote bem sucedido", "phoneNotify": "Notificação por telefone", "wxNotify": "Notificação no wechat", "positives": "Um falso alarme", "interaction": "Interação com dispositivos", "processingContent": "Trabalhar com conteúdo", "exportData": "Exportar a evidência", "exportError": "Falha na exportação", "exportSuccess": "A evidência está sendo exportada. Faça o download na tarefa do evento", "EventTaskTip": "Tarefa de evento: depois de manipular a evidência de exportação no event center- detalhes do evento, a tarefa de exportação será realizada aqui. A tarefa expirará após 7 dias. Faça o download a tempo ou exporte novamente", "terminalContentTip": "Por favor, digite o conteúdo que o dispositivo lê. Digite até 200 caracteres", "bathHandleTip": "Máximo de 200 caracteres suportados. Você não pode continuar após 200 palavras", "terminalContent": "Le<PERSON> o conteúdo", "terminalPlay": "Transmissão do terminal", "processImmediately": "Para tratamento imediato", "Vikgp6W99LW5DLi9_vpHo": "Mais operações disponíveis", "fgRSP3L6Ua8B8bVBYLsMS": "Ver monitoramento em tela cheia", "unresolved": "Não resolvido", "unresolvedTip": "O incidente foi resolvido e a operação não pode continuar", "highRiskTip": "Pode ser uma ameaça direta à vida e requer atenção imediata.", "mediumRiskTip": "Existência de perigo previsível e necessidade de resposta rápida", "lowRiskTip": "Alerta de risco potencial, pode ser prevenido com antecedência", "realMonitoring": "Monitoramento em tempo real", "playContentSuccess": "Conteúdo transmitido com sucesso", "playContentError": "Falha na transmissão do conteúdo. Por favor, envie novamente", "hJ_bvQO9HYG8ge-S_IDO-": "Nenhum canal para operar na tela dividida", "AG-MTfxbowF8XTdMK8yYK": "Iniciar todo o monitoramento", "a64oSYjNagLD0Gg88CxK3": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>", "yokKefCUCFgzYsRo9BnzS": "<PERSON><PERSON><PERSON><PERSON> as monitor<PERSON><PERSON><PERSON><PERSON>", "q9r2Om1U7SO896v3LSVI0": "Interromper o monitoramento", "_uJmjZE1r8GDjxEZyeh-U": "Fim do monitoramento em tela cheia", "x0jvg0WV_R32p1KMHyujw": "Atualmente selecionado", "8_hhY_1Tx0o0w7Y1wdCRb": "O canal de reprodução atingiu o limite de 9 e não pode continuar a adicionar", "3JCIEcBT9HpfXFK0Fu_TN": "Limpar o monitoramento", "事件弹窗配置": "Configuração do evento pop-up", "开启后Tip": "Após a abertura, haverá pop-ups para lembrar quando ocorrerem eventos de verificação", "事件声音配置": "Configuração de som para eventos", "开启声音后Tip": "Após a abertura, haver<PERSON> um alerta audível quando o evento da verificação ocorre", "事件任务Tip": "A evidência é exportada. Faça o download em \"missão de vídeo- missão de evento\"", "9FY6SGcGYrCwFEcACJ5BV": "Preço por dia", "m8MBU7gaNdNDQorXi7_92": "Descrição do complemento", "FIgAdMCZbTjUXRp-BVY-9": "Por favor, defina todas as entradas com * primeiro", "A-DnuXZN2arqL1ZwkHLQ5": "Dicas para conflitos", "bjf4Pxo6-3enYjZX-8tyQ": "* o número máximo de anúncios pop-up que podem ser publicados simultaneamente para o espaço publicitário-alvo é 1", "ShF6ZmpbA1EijFMRApFIw": "* existe um conflito de tempo entre o anúncio atual e o anúncio publicado. Por favor, corrija antes de publicar", "TzYCTuYEh0mlda6hmKngc": "<PERSON><PERSON> sei.", "_5ENuHSGjo9AOnh9VZMSm": "Novos anúncios adicionados", "D2vixOeMA0xifby0Pqek1": "Número de usuários do anúncio:", "ck-6pjY9Q2p9eZYMfj3gz": "Para copiar um anúncio:", "sxNR6X6-YPM-rM3UYnGC_": "Nome do anúncio:", "fSSnMr-IMjus9Nm2qaloa": "O usuário alvo:", "7GGCLgtrmSuZ3yX7QKlUB": "Localização da publicidade:", "Zw7bEnWCItRO3OZR4f41u": "Equipamento associado:", "s8ggwKHE3jM4A355doFBv": "Período de publicidade:", "mziSJbA1dzcUsPsiiLLBE": "<PERSON><PERSON><PERSON>:", "Q_XGjoL48fZZVJzBlyXW7": "Imagens para publicidade:", "levGypCyH-rXOx1mmg65m": "Limitar a proporção:", "ND62G7W-ITK8yn7gYxUyU": "Tamanho limitado: 2Mb formato limitado: JPG/PNG", "eVbyEIX4agI-jM3BK9Ief": "Por favor insira um URL", "vT48eh78S-pz3WROKQ9tn": "Por favor insira o nome do anúncio", "XclqZtcCm0lDPV1UqYj3i": "Não deve ter mais de 20 caracteres", "wZpmnbEoDIWkbpBtxW5gU": "Por favor, selecione um local de anúncio", "d64gIF26q7WcJy1oOOKkL": "Por favor, selecione um período de publicidade", "lvS33ZKf9iiNf7JKvRJHO": "Por favor, selecione o tipo de anúncio", "Ae9AKKIZR7Z7EcvHe76aW": "Por favor, carregue uma imagem do anúncio", "zLoxHFKf86oWk8m9HKhm_": "Falha ao obter a lista de dispositivos", "e7akjZcWeEbSamhM97F5v": "Por favor, carregue uma imagem em PNG ou JPG", "wxzOyEnLiHt1bTxTUpCHM": "Por favor, carregue imagens com ≤2Mb", "BWhWUz0AlHOT-S0V0wK4i": "Número de usuários do anúncio 0 não pode ser salvo e publicado", "8cuHAuf6V9y1S0ktrujuE": "Lançamento com sucesso", "IobVQNnwBhgHS5pOxH4Vw": "Salve e publique com sucesso", "S2DqHrLgq8j-ObZQL40qH": "Salvar e publicar falhou", "MJzt0chtyypPJrJM2etk4": "<PERSON>alha ao salvar", "GJ8mxuRz8wIpmKeftNX8V": "Falha ao obter detalhes do anúncio", "dGvQSz_VHN0aEluf-2SYm": "Por favor, insira o formato correto do URL", "Bkq0Yczk7b865d7nN8M8W": "Nome do anúncio:", "k9HNUSJDi33dfFV3JFI0F": "A carregar...", "vt1MZfWXyMZzyFU7ZD6kc": "<PERSON><PERSON><PERSON><PERSON> imagem ainda", "8Y9n6AMUzmJgLppRkiPqC": "voltar", "ybYXmIEwb2gFT3jw0NhiZ": "retiradas", "dRz9guMYbTUs-t4KKVMoI": "Remover o anúncio sim não", "FPiSGK9vmdXLttuGBLCNy": "Falha ao obter uma lista de anúncios", "-BryKeYUPdPBoc7NnBDjn": "ID de anúncio inválido", "QMXVAEUa1l0LSMMq1P-GE": "Publicar o anúncio ou não", "9qwy42KyBhnFOtUfCP4L5": "Falha na publicação", "p8YaGbXkOnRHfH73h6uwL": "Confirmar a devolução de anúncios atuais?", "WUHni97FVPSbV261NuNyM": "Devolução bem sucedida", "52qKTZxNp_787WjWjlGIw": "Voltar para falhar", "Xflr8gmyOVSwPDsoDtigU": "Confirmar offline an<PERSON><PERSON><PERSON>?", "46L3Sa2n3_HRwD4n9EMdB": "Desligado com sucesso", "ta6lpPTi4NhVLI6x0xtqy": "Falha de desconexão", "4XmNWhQoR-eEcZdOzqJR9": "O valor de classificação deve ser um número entre 1 e 99", "jM1EyGoVHeSsOrb84SzPr": "A classificação foi salva", "QR-j-6cQyMmEYZUipjhag": "Classificar salvar falhou", "1jUjxmqgvba50alYQ9LBI": "ID do anúncio", "qKT-47crN5dDtrd4hPLo_": "Nome do anúncio", "OD9HmFY35w2-KGjqj9mSV": "Localização para publicidade", "zlQECz3Ehrie3BAaW7pLa": "Tipo de publicidade", "6IeAplUy8IVQGWPIsK9pY": "Ciclos de publicidade", "fVGJhvEZ-ZDSDhA0XchW_": "Imagens para publicidade", "rM1Ai797icksN5452hvqh": "triagem", "ZIlhD_KbnyMZiGVadFupW": "<PERSON><PERSON> a pessoa", "D7J2uzbGR9NdUHMzsYJET": "Projecto de", "uURJR8YrvL3Ha-nUf-EOH": "Para ser lançado", "u4qG7qA0CreU6kKaHGVjc": "Já está online", "sd5fGztwwaG_oY68aoZgd": "Já está offline", "lc73xHWPc169AIGWpQWED": "Todos os usuários", "PV07lSEjTue8rcXVfQvZW": "Todos os usuários móveis", "HYB2ZWBIbSoGGHFRq_rlo": "Todos os usuários do dispositivo", "p0LuUDlOJamobXWFBmvwq": "<PERSON><PERSON> t<PERSON>", "_0ruF2IV_hA9Zn4miCbgz": "Página de detalhes do equipamento", "B3sJW7aZoEUiyVh30YerS": "A última semana", "ZoUjvBsJRkXXrvCva1pLY": "No último mês", "-tjmITfzU3XeMoxOA-2N7": "Últimos 3 meses", "JrWkSlLps5yku5MOyYKnU": "<PERSON><PERSON><PERSON><PERSON>", "_YIgMRgV6UMh4EHG2MJaE": "Chinês tradicional chinês", "LNy00b-btNH2e2vSaDazn": "Língua espanhola", "l5_XZj_k7myJXWJsiNRJh": "Língua portuguesa português", "kHWK3fJ2TvdGRZXQrIcgC": "russo", "616Gu11GhAUHLm6tzMB06": "Em alemão", "TJ-4o27rDqFCGOPlOzqqa": "franc<PERSON>s", "BNtah_dKQVXGzDPA2no0n": "bolinhos", "e0alpg_MbQSLh3XDGIzmi": "Banner", "hkY4dtYbrsma9O2lxwpp0": "Últimos 7 dias", "aXr65zaaIK8D8KzkjjUMx": "Por favor, selecione o tipo de anúncio correto", "ipe0_s69Mr1o3M0-Vh_YQ": "Falha na leitura da imagem", "v_1dniF62eoQTZy9tRtjP": "Falha na leitura do arquivo", "CBX9YAaJlHGAGvxJjAX20": "Sem equipamento disponível", "cxagxiJXpUhAZ_HjQvqTX": "Falha na operação", "1eoXePBAdeMp4mp4IXK97": "Publicar um anúncio", "d0nknHU7xudZOANlToBfT": "Publicidade fora da linha", "F997Le_b3iMzyjviH1w_b": "Voltar para anúncios", "4SdxzgE6mEOHAhf-kAa1i": "Detalhes do anúncio", "S8KzvULqkjLUEUDggUP7O": "A informação básica", "sWZVMsKpDLm7Zo_TUC1zI": "An<PERSON><PERSON><PERSON> de <PERSON>", "1ozcMqmPXf7ML4ahZPcz-": "Confirmar a remoção deste anúncio?", "ehydDkOY8K6pbQMdrYdvw": "Confirma a publicação deste anúncio?", "TFWMEXmNyu3JWAhFrUI5c": "Confirmar o desligamento deste anúncio?", "9-KD5m4BVoNYl4cRLd00f": "Confirma a devolução do anúncio ao status de rascunho?", "tJI29weqgBdl7X8Q22xXm": "<PERSON><PERSON><PERSON><PERSON>, o anúncio atual não está online", "eRqgRC5edGZp2h9L4B0Wx": "Data de encerramento das estatísticas: {0}", "X0_QBixRCOYRGZyOIlRuw": "Visualizações acumuladas de anúncios", "5n4RaESXtvz5GiDax_JZt": "Visitantes acumulados do anúncio", "dwAD15TZTKz8D9Aeii6hE": "Falha na obtenção de dados", "EmaS6irOhwdMjUdKb7kpt": "Estado do anúncio:", "oFuc9t_2oQw5HruLy8wPn": "ID do anúncio:", "Faq4yaX0d6aAiKJuLoqDy": "Modelo do equipamento:", "z1M9X6X4Jc_lcHUZPY2yo": "Ordenar por:", "Mxt4sLL2QJGL5YrRL4TOQ": "Atualizado por:", "EiJhbLu2h-Rx5MX_YXwHW": "Tempo de atualização:", "Qq5z7_IdzCpASYspXX9xc": "Número de visitantes mapa de movimento", "fQKeYjglc9g0-0oWVktBZ": "Unidade: pessoa", "alO9uZXdsebWQhFqe3vS2": "Visitantes do anúncio", "n7TbeWPx6ERiuCLdi1xMo": "Visualizações mapa de movimento", "UiI3Qz1DnMzuUOsVXVsFz": "Unidade: secundário", "1ykDVrd_FE2ioXA_6at7N": "Visualizações de anúncios", "atBMWwI4wBA1fjp9rDMUL": "ID do anúncio:", "bmp6Q586E8NHP4TOjI9wy": "O usuário alvo:", "0PRcq7VkoU6VCeXMG6lv_": "Intervalo de datas do anúncio:", "pTu4TJ_3imdKY9kGkrkN4": "Localização da publicidade:", "OgP1t0z_sac6t8wWYfsip": "Estado do anúncio:", "uO5Hxlkc71BV8-Ipub1Lv": "Por favor, selecione o estado do anúncio", "8KO149E4QUo096F392XNf": "* o número máximo de anúncios de Banner que podem ser publicados simultaneamente em espaços publicitários segmentados é 3", "Lwkp0P2tS_8L9TCqgGY9H": "O período de seleção pode ser inferior a um mês. Por favor, selecione novamente", "LCpYvqukl9DTtyOJRGv5v": "Você ainda não assinou o contrato e não pode retirar o dinheiro. Entre em contato com seu provedor de serviços superior para concluir a assinatura do contrato.", "xhfKJiat7T-bprrBbtlKc": "O número de usuários alvo de lançamento é 0 e não pode ser publicado", "lmpTTGK79olDncBLWwHkE": "Número do contrato", "8QcKpYI9MVF-Y0PFV5nfI": "Por favor, preencha o número do contrato", "xCxLzzOADKKHqh3RXgLz_": "Apenas letras e números suportados", "NFsODE3FCSNwfl1kJhx4L": "Tipos de prestadores de serviços", "Kl8KHimuoV06FBms4UjzY": "Selecione um tipo de prestador de serviços", "djmIMlcXzVVOAmDmvw_vQ": "A ecologia", "hndIUBCh21ExxiafeHaPI": "ne<PERSON><PERSON><PERSON>", "LW0_DuWras1S6OvP-zQvd": "O nome", "CScSIqDVa1M_YVWDKHimX": "Tipos de prestadores de serviços:", "PZgd7E_28uEnsrKeQCiPQ": "Número de dispositivos", "JHsxhKvpTpi1f14chgJ1w": "A primeira compra", "5xMgAQOeDE6KWxd0K3bYl": "Assinatura de dispositivos", "PM9VYxX3Pi36qPhErNb9m": "Amigos e familiares guardiões", "NquTuvpdB1KBOo7X28kZM": "Por favor, carregue uma imagem com uma escala de 9:16", "bkT9w9uSvQJwn7GOyfxek": "Por favor, carregue uma imagem com a escala 343:60", "K-bOltnmVObygLynJo1Zl": "Quase meio ano", "package_type_manage": "Gestão de tipos de pacotes", "scene_manage": "Gestão de cenários", "baohantaocan_89eb25": "Tipos de pacotes incluídos: todos os tipos de pacotes sob segurança do veículo, monitoramento de animais de estimação, itens anti-perda, monitoramento doméstico", "baohantaocan_7675b9": "Pacotes incluídos: todos os tipos de pacotes no cenário guardião familiar e amigo", "suoyoubaohan_155ccc": "Todos os pacotes que incluem classe de ativação de serviço da plataforma", "qingquerenfu_fc4bcc": "Por favor, confirme se a proporção de classificação do prestador de serviços, exibição de quantidade e função de tabela de exportação está ligada ou desligada de acordo com sua demanda.", "fenzhangfuwu_06c3b5": "Número de conta do prestador de contas'+ ':", "zhichu-tuikuan": "Despesas- reembolsos", "duanxinxiaohao": "Consumo de SMS", "jinexianshi": "Exibição do valor", "caiwuguanli_d21d0c": "Gestão financeira do total da conta, total disponível, montante blo<PERSON>ado, retirada em todas as páginas; Detalhes de pagamentos e pagamentos — saldo; Detalhamento das contas — valor recebido, valor congelado, valor recebido; Detalhes do saque — valor total do saque; Gestão de prestadores de serviços- distribuição cumulativa, saldos de contas", "daochubiaoge": "Exportar uma tabela", "caiwuguanli_833465": "Gestão financeira função de exportação para todas as páginas", "hetongluru": "Entrada de contrato", "hetongluru_0d3219": "A entrada do contrato significa a entrada do número do contrato. Depois de clicar para confirmar a apresentação, o provedor de serviços pode iniciar a retirada normalmente.", "luru": "entrada", "tuikuanguanli": "Gestão de reembolsos"}