server{
			listen 8788;
			server_name test-internal.ihere.net;
			
			client_body_buffer_size 100M;
			client_max_body_size 200M;
			
			root html_ihere_internal;
			index index.html;
			
			access_log logs_ihere_internal/access.log main;
			error_log logs_ihere_internal/error.log;
		
		    location /test {
        return 200 "THIS IS 8789 + test-internal.ihere.net SERVER";
    }

			location ~ ^/gpsapi.*/.* {
				proxy_pass http://gpsapi;
          
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			}
			

			
			location ^~ /payCenter/ {
				proxy_pass http://payCenter;
				#aws
				#proxy_pass http://***********:8017;
            
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			}

			
			location ^~ /video/ {
				proxy_pass http://video;
            
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			}
			

			location ^~ /client/ {
			 proxy_pass http://client;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			}
			location ^~ /point/ {
			 proxy_pass http://point;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			}

			location ~ \.(jsp|do|xml)$ {
				limit_req zone=limitRate burst=20 nodelay;
				limit_conn totalCon 1000; 
                proxy_pass http://gpsnow;
                proxy_redirect off;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			}
			
			
			location / {  
			     add_header X-Debug-Location "client-internal-IP";
				add_header Access-Control-Allow-Origin *;
				add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
				add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

				if ($request_method = 'OPTIONS') {
					return 204;
				}
				
				proxy_cookie_path / "/; secure; SameSite=None";
			} 
	}

