<!-- 新增广告 -->
<template>
  <div style="height: 100%">
    <!-- 面包屑 -->
    <div class="header">
      <div class="breadcrumb">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/ad/manager' }">{{ $t('lg.limits.ad_manager') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('_5ENuHSGjo9AOnh9VZMSm') }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="user-count-info">
        <span>{{ userCount !== '' && userCount !== null && userCount !== undefined ? $t('D2vixOeMA0xifby0Pqek1') + userCount + '人' : '--' }}</span>
      </div>
    </div>
    <div class="add-ad-container">
      <div class="form-container">
        <el-tabs v-if="isAbroad()" v-model="currentTabLang" tab-position="left" class="lang-tabs" :before-leave="handleBeforeLeave">
          <el-tab-pane v-for="lang in languageOptions" :key="lang.value" :label="lang.label" :name="lang.value"> </el-tab-pane>
        </el-tabs>

        <div class="add-ad-form">
          <el-form ref="adForm" :rules="rules" :model="formData" :label-width="isCNLang ? '100px' : '150px'">
            <div class="editable-info">
              <el-form-item :label="$t('ck-6pjY9Q2p9eZYMfj3gz')">
                <el-select v-model="formData.copyAd" @change="handleCopyAdChange" :disabled="judgeDisable" style="width: 100%">
                  <el-option v-for="item in existAdList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('sxNR6X6-YPM-rM3UYnGC_')" prop="name">
                <el-input v-model="formData.name" :disabled="judgeDisable" maxlength="20" show-word-limit></el-input>
              </el-form-item>
              <el-form-item :label="$t('fSSnMr-IMjus9Nm2qaloa')" prop="targetUserType">
                <el-select v-model="formData.targetUserType" :disabled="judgeDisable" style="width: 100%" @change="handleTargetUserChange">
                  <el-option v-for="item in targetUserOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('7GGCLgtrmSuZ3yX7QKlUB')" prop="adPosition">
                <el-select v-model="formData.adPosition" :disabled="judgeDisable" style="width: 100%" @change="handleAdPositionChange">
                  <el-option v-for="item in adPositionOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </el-form-item>

              <el-form-item v-if="formData.adPosition === 'DEVICE_DETAIL_PAGE'" :label="$t('Zw7bEnWCItRO3OZR4f41u')" prop="selectedDevices">
                <el-select
                  v-model="formData.selectedDevices"
                  :disabled="judgeDisable"
                  multiple
                  filterable
                  collapse-tags
                  style="width: 100%"
                  :placeholder="$t('MTr2Ks8d0Ny8erQoUJ2kJ')"
                  @change="handleDevicesChange"
                >
                  <el-option-group v-for="group in deviceOptions" :key="group.label" :label="group.label">
                    <el-option
                      :disabled="item.disabled"
                      v-for="item in group.options"
                      :key="item.machineTypeId"
                      :label="item.machineTypeName"
                      :value="item.machineTypeId"
                    >
                    </el-option>
                  </el-option-group>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('s8ggwKHE3jM4A355doFBv')" prop="adPeriod">
                <el-date-picker
                  v-model="formData.adPeriod"
                  :picker-options="pickerOptions"
                  type="datetimerange"
                  range-separator="至"
                  :start-placeholder="$t('XbFegIO6XdtwVCLj3tHjn')"
                  :end-placeholder="$t('k3rb7GIYeArL-6QUB2jYR')"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :disabled="judgeDisable"
                  style="width: 100%"
                  @change="handleAdPeriodChange"
                ></el-date-picker>
              </el-form-item>

              <el-form-item :label="$t('mziSJbA1dzcUsPsiiLLBE')" prop="adType">
                <el-select v-model="formData.adType" :disabled="judgeDisable" style="width: 100%" @change="handleAdTypeChange">
                  <el-option v-for="item in adTypeOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('Q_XGjoL48fZZVJzBlyXW7')" :prop="currentTabLang === defaultLang ? `langData.${currentTabLang}.image` : ''">
                <el-upload
                  ref="adUpload"
                  class="image-upload"
                  list-type="picture-card"
                  :action="uploadAction"
                  :show-file-list="false"
                  :file-list="fileList"
                  :on-success="handleUploadImgSuccess"
                  :on-error="handleUploadImgError"
                  :on-change="handleImgChange"
                  :auto-upload="true"
                  :headers="uploadHeaders"
                  :class="formData.adType === 'POPUP' ? 'upload-card-popup' : 'upload-card-banner'"
                >
                  <el-image v-if="currentLangContent.image" :src="currentLangContent.image" class="preview-img" fit="cover"></el-image>

                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>

                  <div class="el-upload__tip" slot="tip">
                    <el-button v-if="currentLangContent.image" type="text" @click="handleRemoveImg">{{ $t('uvVmCHuLW4xeFcLm45zbc') }}</el-button>
                    <span v-else> {{ $t('levGypCyH-rXOx1mmg65m') + (formData.adType === 'POPUP' ? '9:16 ' : '343:60 ') + $t('ND62G7W-ITK8yn7gYxUyU') }} </span>
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item label="URL：" prop="url">
                <el-input v-model="currentLangContent.url" @change="handleUrlChange" :placeholder="$t('eVbyEIX4agI-jM3BK9Ief')"></el-input>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <div class="form-footer">
        <el-button :loading="isloading" type="primary" @click="handleSubmit">{{ $t('lg.logDict.save') }}</el-button>
        <el-button :loading="isloading" type="success" @click="handleSubmitAndPublish">{{ $t('uQX-Oo4jyWWmFhVUIFDZa') }}</el-button>
        <el-button @click="handleClose">{{ $t('lg.cancel') }}</el-button>
      </div>
    </div>
    <AdConflictDialog
      v-if="conflictDialogVisible"
      :visible.sync="conflictDialogVisible"
      :ad-list="conflictAdsList"
      :ad-type="formData.adType"
      @close="handleConflictDialogClose"
    />
  </div>
</template>

<script>
import { targetUserOptions, adPositionOptions, LANGUAGE_OPTIONS, adTypeOptions } from '@/views/Operation/AdManager/constant/config'
import { API_PREFIX, AD_PICKER_OPTIONS, AD_OPERATION_TYPES } from '@/views/Operation/AdManager/constant/adConstants'
import { _createAd, _updateAd, _fetchAdDetail, _fetchAdUserCount, _updateAdStatus, _fetchExistAdList } from '@/api/ads'
import { _getCommonDeviceList } from '@/api/device'
import { checkFileType, checkFileSize, checkImageRatio, processDeviceList, publishAd } from '@/views/Operation/AdManager/utils'
import { timeConvert } from '@/utils/date'
import { isAbroad } from '@/utils/judge.js'
import AdConflictDialog from '@/views/Operation/AdManager/components/AdConflictDialog.vue'
import { off } from '../../../../base-ui/dropdown/untils/dom'
export default {
  name: 'AddAd',
  components: {
    AdConflictDialog
  },
  data() {
    return {
      currentTabLang: isAbroad() ? 'en' : 'cn',
      dialogVisible: false,

      formData: {
        adPosition: '',
        adType: '',
        endTime: '',
        id: '',
        name: '',
        operationType: '',
        relateDevices: '',
        selectedDevices: [],
        startTime: '',
        targetUserType: '',
        adPeriod: [],
        copyAd: '',
        langData: {
          en: { image: '', url: '' },
          cn: { image: '', url: '' },
          hk: { image: '', url: '' },
          es: { image: '', url: '' },
          pt: { image: '', url: '' },
          ru: { image: '', url: '' },
          de: { image: '', url: '' },
          fr: { image: '', url: '' }
        }
      },
      targetUserOptions,
      adPositionOptions,
      adTypeOptions,
      deviceOptions: [], // 设备选项，按系列分组
      userCount: null, // 广告用户数
      rules: {
        name: [
          { required: true, message: this.$t('vT48eh78S-pz3WROKQ9tn'), trigger: 'blur' },
          { max: 20, message: this.$t('XclqZtcCm0lDPV1UqYj3i'), trigger: 'blur' }
        ],
        targetUserType: [{ required: true, message: this.$t('lg.chosetarget'), trigger: 'change' }],
        adPosition: [{ required: true, message: this.$t('wZpmnbEoDIWkbpBtxW5gU'), trigger: 'change' }],
        selectedDevices: [
          {
            required: true,
            message: this.$t('MTr2Ks8d0Ny8erQoUJ2kJ'),
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.formData.adPosition === 'DEVICE_DETAIL_PAGE' && (!value || value.length === 0)) {
                callback(new Error(this.$t('MTr2Ks8d0Ny8erQoUJ2kJ')))
              } else {
                callback()
              }
            }
          }
        ],
        adPeriod: [{ required: true, message: this.$t('d64gIF26q7WcJy1oOOKkL'), trigger: 'change' }],
        adType: [{ required: true, message: this.$t('lvS33ZKf9iiNf7JKvRJHO'), trigger: 'change' }],
        'langData.en.image': [
          {
            required: true,
            message: this.$t('Ae9AKKIZR7Z7EcvHe76aW'),
            trigger: 'change'
          }
        ]
      },
      fileList: [],
      deviceList: [], // 所有设备列表
      isloading: false,
      pickerOptions: AD_PICKER_OPTIONS,
      existAdList: [],
      conflictDialogVisible: false,
      conflictAdsList: [],
      languageOptions: LANGUAGE_OPTIONS
    }
  },

  computed: {
    isCNLang() {
      // 统一语言判断
      return this.$cookies.get('lang') === 'cn'
    },
    judgeDisable() {
      // 国外环境，只有当前语言tab为英文时，才允许编辑,其他语言默认值为英文tab的值
      return isAbroad() && this.currentTabLang !== 'en'
    },
    uploadAction() {
      return `../..${API_PREFIX}/client/common/upload.do`
    },
    uploadHeaders() {
      return {
        token: this.$cookies.get('token')
      }
    },
    defaultLang() {
      return isAbroad() ? 'en' : 'cn'
    },
    currentLangContent() {
      return this.formData.langData[this.currentTabLang]
    }
  },

  methods: {
    isAbroad,
    initLangContents() {
      LANGUAGE_OPTIONS.forEach(lang => {
        this.$set(this.formData.langData, lang.value, { image: '', url: '' })
      })
    },

    async fetchDeviceList() {
      try {
        const response = await _getCommonDeviceList()
        if (response.ret === 1 && response.data) {
          const { options, deviceList } = processDeviceList(response.data)
          this.deviceOptions = options
          this.deviceList = deviceList
          //   console.log('this.deviceList', this.deviceList)
        }
      } catch (error) {
        this.$message.error(this.$t('zLoxHFKf86oWk8m9HKhm_'))
      }
    },
    handleDevicesChange(value) {
      this.formData.relateDevices = value.join(',')
      //选中全部设备和无设备时，清空其他选项选中并且禁用其他选项
      if (value.includes('-1') || value.includes('0')) {
        if (value.includes('-1')) {
          this.formData.selectedDevices = ['-1']
        } else {
          this.formData.selectedDevices = ['0']
        }
        this.deviceOptions.forEach(item => {
          item.options.forEach(option => {
            option.disabled = true
          })
        })
      } else {
        this.deviceOptions.forEach(item => {
          item.options.forEach(option => {
            option.disabled = false
          })
        })
      }
      this.calculateUserCount()
    },
    handleTargetUserChange() {
      this.calculateUserCount()
    },
    handleAdPositionChange() {
      if (this.formData.targetUserType) {
        this.calculateUserCount()
      }
      this.$nextTick(() => {
        this.$refs.adForm.validateField('relateDevices')
      })
    },
    async calculateUserCount() {
      try {
        const params = {
          targetUserType: this.formData.targetUserType,
          deviceModel: this.formData?.selectedDevices?.length > 0 ? this.formData.selectedDevices.join(',') : ''
        }
        if (params.deviceModel === '') {
          delete params.deviceModel
        }

        const res = await _fetchAdUserCount(params)

        if (res.ret === 1 && res.data !== undefined) {
          this.userCount = res.data.userCount
        }
      } catch (error) {
        this.userCount = 0
      }
    },

    handleAdPeriodChange(value) {
      if (value && value.length === 2) {
        this.formData.startTime = value[0]
        this.formData.endTime = value[1]
      } else {
        this.formData.startTime = ''
        this.formData.endTime = ''
      }
    },

    handleBeforeLeave(tab, oldTab) {
      if (oldTab === 'en') {
        // 只验证英文相关的字段
        const fieldsToValidate = ['langData.en.image', 'name', 'targetUserType', 'adPosition', 'adPeriod', 'adType']
        if (this.formData.adPosition === 'DEVICE_DETAIL_PAGE') {
          fieldsToValidate.push('relateDevices')
        }

        // 逐个验证必填字段
        let valid = true
        fieldsToValidate.forEach(field => {
          this.$refs.adForm.validateField(field, error => {
            if (error) {
              valid = false
            }
          })
        })
        if (!this.validateUrl(this.formData.langData[this.currentTabLang].url)) {
          valid = false
        }
        if (!valid) {
          this.$message.error(this.$t('FIgAdMCZbTjUXRp-BVY-9'))
          this.$nextTick(() => {
            this.currentTabLang = oldTab
          })
          return false
        }
      }
    },
    handleImgChange(file, fileList) {
      if (!fileList.length) {
        this.currentLangContent.image = ''
        return
      }
      const lastFile = fileList[fileList.length - 1]
      // 校验格式
      if (!checkFileType(lastFile)) {
        this.$message.error(this.$t('e7akjZcWeEbSamhM97F5v'))
        fileList.pop()

        this.currentLangContent.image = ''
        return
      }
      // 校验大小
      if (!checkFileSize(lastFile)) {
        this.$message.error(this.$t('wxzOyEnLiHt1bTxTUpCHM'))
        fileList.pop()

        this.currentLangContent.image = ''
        return
      }
      // 校验比例
      checkImageRatio(lastFile, this.formData.adType)
        .then(() => {
          this.currentLangContent.image = ''
          this.fileList.push(lastFile)
        })
        .catch(errMsg => {
          this.$message.error(errMsg)
          fileList.pop()

          this.currentLangContent.image = ''
        })
    },
    handleUploadImgSuccess(response, file, fileList) {
      if (response.ret === 1 && response.data && response.data.viewUrl) {
        const currentLang = this.currentTabLang
        const imageUrl = response.data.viewUrl
        setTimeout(() => {
          // 更新当前语言的图片
          this.$set(this.formData.langData[currentLang], 'image', imageUrl)
        }, 100)
      }
    },
    handleUploadImgError(error, file, fileList) {
      this.currentLangContent.image = ''
      this.$message.error(this.$t('PzBG2cs4d9h1G-yy4y-JY'))
    },

    handleRemoveImg() {
      this.currentLangContent.image = ''
      this.fileList = []
    },
    // 组装langContents数组
    assembleLanguageContents() {
      const langContents = []
      // 遍历语言映射，构建langContents数组
      Object.keys(this.formData.langData).forEach(lang => {
        const content = this.formData.langData[lang]
        // 只添加有内容的语言
        if (content.image || content.url) {
          langContents.push({
            image: content.image,
            lang: lang,
            url: content.url
          })
        }
      })
      return langContents
    },
    async handleAdSubmit(isPublish = false) {
      if (this.isloading) return
      if (!this.validateUrl(this.formData.langData[this.currentTabLang].url)) {
        return
      }
      // 发布时检查用户数
      if (isPublish && this.userCount === 0) {
        this.$message.error(this.$t('BWhWUz0AlHOT-S0V0wK4i'))
        return
      }

      this.isloading = true
      // 验证英文表单（必填项）
      this.currentTabLang = this.defaultLang
      this.$refs.adForm.validate(async valid => {
        if (!valid) {
          this.isloading = false
          return false
        }
        try {
          const langContents = this.assembleLanguageContents()
          const params = {
            adPosition: this.formData.adPosition,
            adType: this.formData.adType,
            endTime: timeConvert(this.formData.endTime),
            name: this.formData.name,
            operationType: isPublish ? AD_OPERATION_TYPES.PUBLISH : AD_OPERATION_TYPES.SAVE,
            startTime: timeConvert(this.formData.startTime),
            targetUserType: this.formData.targetUserType,
            langContents: langContents,
            relateDevices: this.formData?.selectedDevices?.length > 0 ? this.formData.selectedDevices.join(',') : ''
          }
          let saveRes = ''
          if (this.formData.id) {
            params.id = this.formData.id
            saveRes = await _updateAd(params)
          } else {
            //广告的发布逻辑与其他组件有些不同，需要单独处理，不能直接复用公共的publishAd方法
            saveRes = await _createAd(params)
          }
          if (saveRes.ret === 1) {
            this.$message.success(isPublish ? this.$t('IobVQNnwBhgHS5pOxH4Vw') : this.$t('YPoyixx0lWnv9IoARFDls'))
            this.$router.push('/ad/manager')
          } else if (saveRes.code === '50330' && Array.isArray(saveRes.data)) {
            // 显示冲突广告弹窗
            this.conflictAdsList = saveRes.data
            this.conflictDialogVisible = true
          } else {
            this.$message.error(saveRes.msg)
          }
        } catch (error) {
          this.$message.error(isPublish ? this.$t('S2DqHrLgq8j-ObZQL40qH') : this.$t('MJzt0chtyypPJrJM2etk4'))
        } finally {
          this.isloading = false
        }
      })
    },
    async handleSubmit() {
      await this.handleAdSubmit(false)
    },

    async handleSubmitAndPublish() {
      await this.handleAdSubmit(true)
    },
    handleClose() {
      this.$router.back()
    },
    setFormData(detail) {
      if (detail.relateDevices && Array.isArray(detail.relateDevices)) {
        // 详情接口返回对象数组，需要提取machineTypeId
        detail.selectedDevices = detail.relateDevices.map(device => device.machineTypeId)
        this.$set(this.formData, 'selectedDevices', detail.selectedDevices)
        // 转换为逗号分隔的字符串存储
        this.$set(this.formData, 'relateDevices', detail.selectedDevices.join(','))
      } else {
        this.$set(this.formData, 'selectedDevices', [])
        this.$set(this.formData, 'relateDevices', '')
      }

      const langData = {}
      this.languageOptions.forEach(lang => {
        langData[lang.value] = { image: '', url: '' }
      })
      if (detail.langContents && Array.isArray(detail.langContents)) {
        detail.langContents.forEach(item => {
          const lang = item.lang
          if (langData[lang]) {
            langData[lang] = {
              image: item.image,
              url: item.url
            }
          }
        })
      }

      this.formData = {
        ...detail,
        langData: { ...this.formData.langData, ...langData },
        adPeriod: detail.startTime && detail.endTime ? [timeConvert(detail.startTime, 'local'), timeConvert(detail.endTime, 'local')] : [],
        operationType: AD_OPERATION_TYPES.SAVE,
        startTime: timeConvert(detail.startTime, 'local'),
        endTime: timeConvert(detail.endTime, 'local')
      }
      delete this.formData.langContents
    },

    async handleQueryDetail(id) {
      try {
        const res = await _fetchAdDetail(id)
        if (res.ret === 1 && res.data) {
          const detail = res.data
          this.setFormData(detail)
          this.calculateUserCount()
        }
      } catch (error) {
        this.$message.error(this.$t('GJ8mxuRz8wIpmKeftNX8V'))
      }
    },
    async _fetchExistAdList() {
      const res = await _fetchExistAdList()
      if (res.ret === 1 && res.data) {
        this.existAdList = res.data
      }
    },
    async handleCopyAdChange(id) {
      if (id) {
        const res = await _fetchAdDetail(id)
        delete res.data.id
        if (!isAbroad()) {
          res.data.langContents = res.data.langContents.filter(item => item.lang === 'cn')
        }
        if (res.ret === 1 && res.data) {
          this.setFormData(res.data)
        }
      } else {
        // 如果取消选择，重置表单
        this.resetForm()
      }
    },
    handleAdTypeChange(value) {
      Object.keys(this.formData.langData).forEach(lang => {
        this.formData.langData[lang].image = ''
        this.formData.langData[lang].url = ''
      })
    },
    handleUrlChange(value) {
      this.validateUrl(value)
    },
    validateUrl(value) {
      value = value.trim()

      if (!value) return true
      const urlRegex = /^(((ht|f)tps?):\/\/)?([^!@#$%^&*?.\s-]([^!@#$%^&*?.\s]{0,63}[^!@#$%^&*?.\s])?\.)+[a-z]{2,6}\/?/
      if (!urlRegex.test(value)) {
        this.$message.error(this.$t('dGvQSz_VHN0aEluf-2SYm'))
        return false
      }
      return true
    },
    resetForm() {
      this.formData = {
        adPosition: '',
        adType: '',
        endTime: '',
        id: '',
        name: '',
        operationType: '',
        relateDevices: '',
        startTime: '',
        targetUserType: '',
        adPeriod: [],
        copyAd: '',
        langData: {
          en: { image: '', url: '' },
          cn: { image: '', url: '' },
          hk: { image: '', url: '' },
          es: { image: '', url: '' },
          pt: { image: '', url: '' },
          ru: { image: '', url: '' },
          de: { image: '', url: '' },
          fr: { image: '', url: '' }
        }
      }
      this.formData.selectedDevices = []
      this.fileList = []
      this.currentTabLang = this.defaultLang
      this.$refs.adForm && this.$refs.adForm.resetFields()
    },
    handleConflictDialogClose() {
      this.conflictDialogVisible = false
    },
    //判断当前广告的设备类型是不是包含所有的设备
    isAllDevice() {
      return this.deviceList.length === this.formData.selectedDevices.length
    },
    // 如果当前广告的设备类型是包含所有的设备，则将设备类型设置为全部设备
    formatFormDeviceOption() {
      if (this.isAllDevice()) {
        this.formData.selectedDevices = ['-1']
        this.deviceOptions.forEach(item => {
          item.options.forEach(option => {
            option.disabled = true
          })
        })
      } else if (this.formData.selectedDevices.length === 0) {
        this.formData.selectedDevices = ['0']
        this.deviceOptions.forEach(item => {
          item.options.forEach(option => {
            option.disabled = true
          })
        })
      }
    }
  },
  beforeDestroy() {
    // 释放图片URL，防止内存泄漏
    Object.values(this.formData.langData).forEach(content => {
      if (content.image) {
        try {
          URL.revokeObjectURL(content.image)
        } catch (e) {
          // 忽略异常
        }
      }
    })
  },
  mounted() {
    this._fetchExistAdList()
  },
  async created() {
    // 初始化语言内容
    this.initLangContents()
    this.currentTabLang = this.defaultLang
    if (!this.isAbroad()) {
      //国内环境当前显示的内容为中文，加一个中文的必填校验，国外环境只有英文是必填项
      this.rules = {
        ...this.rules,
        'langData.cn.image': {
          required: true,
          message: this.$t('Ae9AKKIZR7Z7EcvHe76aW')
        }
      }
    }
    // 先获取设备列表
    await this.fetchDeviceList()

    // 检查是否为编辑模式，如果是则获取广告详情
    const id = this.$route.query.id
    if (id) {
      await this.handleQueryDetail(id)
      this.formatFormDeviceOption()
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.breadcrumb {
  height: 20px;
  border-radius: $containerRadius;
  margin: 15px 20px -8px 20px;
  width: 500px;
}
.user-count-info {
  flex: 1;
  height: 20px;
  border-radius: $containerRadius;
  margin: 15px 20px -8px 20px;
  width: 500px;
}
.add-ad-container {
  height: calc(100% - 52px);
  box-sizing: border-box;
  margin: 16px;
  padding: 16px;
  background: #ffffff;
  overflow: auto;
  border-radius: $containerRadius;

  .form-container {
    display: flex;
  }

  .lang-tabs {
    ::v-deep .el-tabs__item {
      text-align: center;
      height: auto;
      padding: 5px 10px 5px 0px;
      &.is-active {
        font-weight: normal;
      }
    }
  }

  .add-ad-form {
    margin-top: 20px;
    max-width: 500px;
    :deep(.el-form-item__content) {
      margin-left: 150px !important;
    }
  }
  .form-footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-start;
  }
  .image-upload {
    text-align: center;
    position: relative;
    display: flex;

    flex-direction: column;
    .el-upload {
      cursor: pointer;
      &:hover {
        .el-upload__text,
        .el-icon-upload {
          color: #0068ff;
        }
      }
    }
  }
  ::v-deep .el-upload--picture-card {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px auto;
  }
  .upload-card-banner {
    ::v-deep .el-upload--picture-card {
      width: 360px;
      height: 90px;
    }
  }
  .upload-card-popup {
    ::v-deep .el-upload--picture-card {
      width: 180px;
      height: 320px;
    }
  }

  .preview-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
