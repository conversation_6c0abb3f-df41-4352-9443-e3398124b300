<template>
  <el-dialog :title="$t('A-DnuXZN2arqL1ZwkHLQ5')" :visible.sync="dialogVisible" width="500px" :close-on-click-modal="false" @close="handleClose">
    <div class="conflict-dialog">
      <div class="conflict-tips">
        <p>{{ adType === 'POPUP' ? $t('bjf4Pxo6-3enYjZX-8tyQ') : $t('8KO149E4QUo096F392XNf') }}</p>
        <p>{{ $t('ShF6ZmpbA1EijFMRApFIw') }}</p>
      </div>
      <div class="conflict-list">
        <div v-for="(item, index) in adList" :key="index" class="conflict-item">
          <div class="ad-status" :class="{ online: item.status === 'ONLINE', offline: item.status === 'OFFLINE', 'not-online': item.status === 'NOT_ONLINE' }">
            <span class="status-dot"></span>
            {{ getStatusText(item.status) }}
          </div>
          <div class="ad-name">{{ item.name }}</div>
          <div class="ad-time">{{ timeConvert(item.startTime, 'local') }} 至 {{ timeConvert(item.endTime, 'local') }}</div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleClose">{{ $t('TzYCTuYEh0mlda6hmKngc') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { statusMap } from '../constant/config'
import { timeConvert } from '@/utils/date'

export default {
  name: 'AdConflictDialog',
  data() {
    return {
      timeConvert
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    adList: {
      type: Array,
      default: () => []
    },
    adType: {
      type: String,
      default: 'POPUP'
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    getStatusText(status) {
      const code = statusMap[status]
      return code
    }
  }
}
</script>

<style lang="scss" scoped>
.conflict-dialog {
  .conflict-tips {
    color: #606266;
    font-size: 14px;
    margin-bottom: 15px;
    p {
      margin: 5px 0;
    }
  }

  .conflict-list {
    max-height: 300px;
    overflow-y: auto;

    .conflict-item {
      padding: 10px 16px;
      border-radius: 8px;
      background-color: #f5f7fa;
      margin-bottom: 10px;
      position: relative;
      transition: all 0.3s ease;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      .ad-name {
        font-weight: bold;
        font-size: 14px;
        margin-bottom: 5px;
        padding-right: 75px; // 为状态标签留出空间
      }

      .ad-time {
        color: #606266;
        font-size: 13px;
        margin-bottom: 5px;
      }

      .ad-status {
        position: absolute;
        top: 10px;
        right: 16px;
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 12px;
        font-weight: 500;
        display: flex;
        align-items: center;
        transition: all 0.2s ease;
        min-width: 60px;
        justify-content: center;

        .status-dot {
          display: inline-block;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          margin-right: 4px;
        }
      }
      .not-online {
        color: #fff;
        background-color: rgba(64, 158, 255, 0.9);
        box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);

        .status-dot {
          background-color: #fff;
        }
      }
      .online {
        color: #fff;
        background-color: rgba(103, 194, 58, 0.9);
        box-shadow: 0 2px 4px rgba(103, 194, 58, 0.3);

        .status-dot {
          background-color: #fff;
        }
      }
      .offline {
        color: #fff;
        background-color: rgba(245, 108, 108, 0.9);
        box-shadow: 0 2px 4px rgba(245, 108, 108, 0.3);

        .status-dot {
          background-color: #fff;
        }
      }
    }
  }
}
</style>
