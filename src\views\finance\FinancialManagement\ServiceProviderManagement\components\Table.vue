<template>
  <div class="swd-table-container">
    <!-- 金额 -->
    <div class="amount" v-if="userInfo.userType == 0 && configObj.showAdminAmount">
      <h4>
        <template>
          <span>{{ $t('5TuLCfhzC3c4Fhk9bFXFe') }}：</span>
          <span style="color: #FB772B;margin-right:20px">{{ accumulateAmount }}</span>
        </template>
        <span>{{ $t('SCx72TSY1VcnknS0ks5lJ') }}：</span>
        <span style="color: #FB772B">{{ amount }}</span>
      </h4>
    </div>
    <!-- table -->
    <el-table
      fit
      ref="deviceTable"
      v-loading="dataLoading"
      :data="tableData.data"
      class="swd-table"
      :height="tableHeight"
      row-key="id"
      :header-row-class-name="setClassName"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column :label="$t('lg.clientName')" prop="name">
        <template slot-scope="{ row }">
          <span class="swd-table-cell-btn" @click="showDialog(row, 1)" v-if="userInfo.userType == 0">
            {{ row.name }}
          </span>
          <span v-else>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('qlaqPAd2iRvHO0ZwbK-za')" prop="serviceProviderAccount"> </el-table-column>
      <el-table-column :label="$t('lg.serviceProvide') + 'ID'" prop="id"> </el-table-column>
      <el-table-column :label="$t('vyx_f6IfvRThEn7vSa7uh')" prop="levelStr"> </el-table-column>
      <el-table-column :label="$t('UBGf6jO1VlyC5cZG6G_Vv')" prop="parentName"> </el-table-column>
      <el-table-column :label="$t('5TuLCfhzC3c4Fhk9bFXFe')" prop="accruingAmountStr" v-if="configObj.showAdminOrProviderAmount"> </el-table-column>
      <el-table-column :label="$t('KgSLgX2QXc5P4ebGJhoCr')" align="center">
        <el-table-column :label="$t('equipmentService')" prop="deviceServiceProportionStr" align="center">
          <template #header>
            <span>{{ $t('equipmentService') }}</span>
            <el-tooltip :content="$t('baohantaocan_89eb25')" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column :label="$t('softwareServices')" prop="softwareServiceProportionStr" align="center">
          <template #header>
            <span>{{ $t('softwareServices') }}</span>
            <el-tooltip :content="$t('baohantaocan_7675b9')" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column :label="$t('deviceActivation')" prop="deviceActiveProportionStr" align="center">
          <template #header>
            <span>{{ $t('deviceActivation') }}</span>
            <el-tooltip :content="$t('suoyoubaohan_155ccc')" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column :label="$t('jG2Um06j_7e9GgAO6u6x6')" prop="balanceStr" v-if="configObj.showAdminOrProviderAmount">
        <template slot-scope="{ row }">
          <span class="swd-table-cell-btn" v-if="userInfo.userType === 0" @click="showDialog(row, 2)"> {{ row.balanceStr }}</span>
          <span v-else> {{ row.balanceStr }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('L6xr2tfBSDAIDoOf-18gW')" prop="realNameCheck">
        <template slot-scope="{ row }">
          <span> {{ row.realNameCheck | realNameCheckFilter(te) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('lg.operator')" prop="action" v-if="configObj.showAdminAmount" fixed="right">
        <template slot-scope="{ row }">
          <span v-if="isShowNewServiceProviderEdit(row.edit) && row.id !== '1'" class="cursor-point ljdw-color-blue" @click="handleEdit(row)">{{
            $t('lg.edit')
          }}</span>
        </template>
      </el-table-column>
      <template slot="empty">
        <p class="empty-container">
          {{ dataLoading ? '' : $t('lg.noData') }}
        </p>
      </template>
    </el-table>
    <!-- pagination -->
    <div class="swd-pagination-container">
      <el-tooltip
        v-if="configObj.exportSwitch"
        popper-class="device-client-icon"
        class="item"
        effect="dark"
        :content="$t('8Hy4f3sEGqYcZA0E2Tgwm')"
        placement="top"
      >
        <el-button type="primary" class="swd-download-btn" :loading="exportLoading" @click="downloadTableData">
          <svg-icon v-if="!exportLoading" icon-class="client_download" class="download-icon"></svg-icon>
        </el-button>
      </el-tooltip>
      <base-pagination
        :current-page.sync="searchParams.pageNo"
        :page-sizes="[10, 15, 20, 30]"
        :page-size="searchParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableData.total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </base-pagination>
    </div>
  </div>
</template>

<script>
import { _getServiceTableList, _exportServiceTableList, _getServiceTotalAccount } from '@/api/order.js'
import { mapGetters } from 'vuex'
import { getIsShowNewServiceProvider } from '../config'
import { userTypeMap } from '@/assets/map/userMap'

export default {
  inject: ['configObj'],
  data() {
    return {
      dataLoading: false,
      exportLoading: false,
      tableData: {
        data: [],
        total: 0
      },
      searchParams: {
        pageNo: 1,
        pageSize: 15
      },
      accumulateAmount: '--',
      amount: '--',
      isShowNewServiceProvider: getIsShowNewServiceProvider(),
      permissionArr: []
    }
  },
  computed: {
    ...mapGetters(['currencySymbol', 'currencyParams', 'userInfo', 'permissions']),
    tableHeight() {
      return this.userInfo.userType == 0 && this.configObj.showAdminAmount ? 'calc(100% - 84px)' : 'calc(100% - 52px)'
    }
  },
  filters: {
    realNameCheckFilter(val, te) {
      const filterMaps = {
        0: te('lg._no'),
        1: te('OLvc_EzZFYBlBhNIXnjZD'),
        2: te('Whfkt63jRh6VQ4s6QphEa'),
        3: te('OLvc_EzZFYBlBhNIXnjZD') + '、' + te('Whfkt63jRh6VQ4s6QphEa')
      }
      return filterMaps[val] || '-'
    }
  },
  mounted() {
    this.permissionArr = this.permissions
      .filter(item => {
        return item.type === 2
      })
      .map(item => item.perms)
  },
  methods: {
    setClassName() {
      return 'c-service__table__tr'
    },
    isShowNewServiceProviderEdit(isCanEdit) {
      const { userInfo } = this
      return userInfo.userType === userTypeMap.admin || isCanEdit
    },
    handleEdit(record) {
      this.$emit('edit', record)
    },
    // 查询
    search(info) {
      this.searchParams = {
        ...{
          pageNo: 1,
          pageSize: 15
        },
        ...info
      }

      this.getServiceTableList()
      if (this.userInfo.userType === 0) {
        this.getServiceTotalAccount()
      }
    },
    /* downloadExcel */
    async downloadTableData() {
      if (this.exportLoading === true) {
        return
      }
      this.exportLoading = true
      try {
        let params = JSON.parse(JSON.stringify(this.searchParams))
        let res = await _exportServiceTableList(params)
        let a = document.createElement('a')
        let blob = new Blob([res], { type: 'application/ms-excel' })
        let objectUrl = URL.createObjectURL(blob)
        a.setAttribute('href', objectUrl)
        let fileName = new Date()
        fileName = fileName.getFullYear() + '' + (fileName.getMonth() + 1) + fileName.getDate()
        a.setAttribute('download', `${fileName}.xlsx`)
        a.click()
        this.exportLoading = false
      } catch (error) {
        this.exportLoading = false
        throw new Error(error)
      }
    },
    /* changeCurrentPage */
    handleCurrentChange(page) {
      this.searchParams.pageNo = page
      this.getServiceTableList()
    },
    /* changeTableRowSize */
    handleSizeChange(size) {
      this.searchParams.pageSize = size
      this.getServiceTableList()
    },
    /* getTableData */
    async getServiceTableList() {
      this.dataLoading = true
      try {
        let res = await _getServiceTableList(this.searchParams)
        if (res.ret === 1) {
          this.tableData.data = res.data.data || []
          this.tableData.total = res.data.total || 0
          this.$nextTick(() => {
            this.$refs.deviceTable.doLayout()
          })
        } else {
          this.$message.error(res.msg)
        }
        this.dataLoading = false
      } catch (error) {
        this.dataLoading = false
        throw new Error(error)
      }
    },
    // 获取合计金额
    async getServiceTotalAccount() {
      try {
        let res = await _getServiceTotalAccount({ currency: this.currencyParams })
        if (res.ret === 1) {
          this.accumulateAmount = res.data.accruingAmountStr
          this.amount = res.data.totalBalanceStr
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        throw new Error(error)
      }
    },
    showDialog(row, type) {
      this.$emit('showDialog', row, type)
    },
    // 翻译函数
    te(arg) {
      const hasKey = this.$t(arg)
      if (hasKey) {
        const result = this.$t(arg)
        return result
      }
      return arg
    }
  }
}
</script>

<style lang="scss" scoped>
.swd-table-container {
  flex: 1; //自动填充剩余高度
  height: 0; //flex子项大小在内容大小过大时，只能自适应撑开，不会自适应压缩https://blog.csdn.net/u012557814/article/details/*********
  .amount {
    line-height: 22px;
    margin-bottom: 10px;
  }
  .el-table {
    width: 100% !important;
    border: 0;
    ::v-deep .el-table__header,
    ::v-deep .el-table__body {
      width: 100% !important;
    }
    ::v-deep th.el-table__cell.is-leaf,
    ::v-deep .el-table__row > td {
      border-right: 0;
    }
    &::after,
    &::before {
      display: none;
    }
  }
}
.swd-pagination-container {
  position: relative;
  padding: 10px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: auto;
  .swd-download-btn {
    position: absolute;
    right: 0;
  }
}

// 对齐根节点 无论有无子节点
::v-deep .el-table__row:not([class*='el-table__row--level-']) {
  td:first-child {
    padding-left: 24px;
  }
}
::v-deep .el-table__placeholder {
  margin-right: 3px;
}
</style>

<style lang="scss">
.c-service__table__tr .is-center {
  border-right: 1px solid #ebeef5 !important;
  border-left: 1px solid #ebeef5 !important;
  &:nth-child(2) {
    border-right: 0 !important;
    border-left: 0 !important;
  }
}
</style>
