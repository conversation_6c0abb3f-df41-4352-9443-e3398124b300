<template>
  <el-dialog
    :title="isEdit ? '编辑套餐类型' : '新增套餐类型'"
    :visible.sync="dialogVisible"
    width="900px"
    @close="handleClose"
    :close-on-click-modal="false"
    class="add-edit-dialog"
  >
    <div class="form-container">
      <!-- 左侧语言切换Tab -->
      <el-tabs v-if="isAbroad()" v-model="currentLang" tab-position="left" class="lang-tabs" :before-leave="handleBeforeLeave">
        <el-tab-pane v-for="lang in languageOptions" :key="lang.value" :label="lang.label" :name="lang.value"></el-tab-pane>
      </el-tabs>

      <div class="right-form">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="auto" label-position="left" style="width: 100%">
          <el-row>
            <el-col :span="12">
              <!-- 名称 - 所有语言都可编辑 -->
              <el-form-item label="名称" :prop="`langData.${currentLang}.packTypeName`">
                <el-input v-model="form.langData[currentLang].packTypeName" placeholder="请输入套餐类型名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属场景" prop="sceneId">
                <el-select :disabled="disabled" v-model="form.sceneId" placeholder="请选择所属场景" @change="handleSceneChange" style="width: 100%">
                  <el-option v-for="item in sceneOptions" :key="item.sceneId" :label="item.sceneName" :value="Number(item.sceneId)" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 单选框，选中之后id push到form.functionList -->
          <el-form-item label="增值服务项" prop="functionList">
            <el-checkbox-group v-model="form.functionList" :disabled="disabled">
              <el-checkbox v-for="item in functionOptions" :key="item.funcId" :label="item.funcId" v-model="item.funcId">
                {{ item.funcName }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-row>
            <el-col :span="12">
              <!-- 套餐大类  100：设备服务类、101：软件服务类、102：设备激活类 -->
              <el-form-item label="套餐大类" prop="packClassify">
                <el-select :disabled="disabled" v-model="form.packClassify" placeholder="请选择套餐大类" style="width: 100%">
                  <el-option v-for="item in packClassifyOptions" :key="item.value" :label="item.label" :value="Number(item.value)" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="排序号" prop="sort">
            <div class="sort-number">
              <el-input-number :disabled="disabled" v-model="form.sort" controls-position="right" :min="0" :controls="true" />
            </div>
          </el-form-item>

          <div class="search-box">
            <el-input :disabled="disabled" prefix-icon="el-icon-search" v-model="deviceSearchKeyword" placeholder="输入设备型号进行搜索" clearable></el-input>
          </div>
          <div class="device-selection-container">
            <div v-if="!form.sceneId" class="form-tip">
              <span class="form-tip">请先选择场景</span>
            </div>
            <div v-else class="select-all">
              <el-checkbox :disabled="disabled" v-model="selectAllDevices" @change="handleSelectAllDevices">全选当前结果</el-checkbox>
            </div>
            <div class="device-grid">
              <div v-for="device in filteredDevices" :key="device.machineTypeId" class="device-item">
                <el-checkbox :disabled="disabled || device.used" v-model="device.selected" @change="handleDeviceSelect">{{
                  device.machineTypeName
                }}</el-checkbox>
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { _createPackageType, _updatePackageType } from '@/api/packageType'
import { _getMachineTypeForScene, _fetchSceneList } from '@/api/scene'
import multiLanguageMixin from '@/mixins/multiLanguage'
import { _getFunctionsSelector } from '@/api/order.js'
import { isAbroad } from '@/utils/judge'
import { getOptionsByDictCode } from '@/utils/dict'

export default {
  name: 'AddEditDialog',
  mixins: [multiLanguageMixin],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      deviceSearchKeyword: '',
      selectAllDevices: false,
      form: {
        langData: {},
        sceneId: '',
        // 套餐类型名称, 传给后端做模糊查询用，国内用langData.cn.packTypeName的值，国外用langData.en.packTypeName的值
        packTypeName: '',
        functionList: [],
        sort: 0,
        machineTypeIds: [],
        packClassify: ''
      },
      sceneOptions: [],
      deviceOptions: [],
      functionOptions: [],
      packClassifyOptions: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    disabled() {
      return !this.onDefaultTab //mixin的computed中设置了onDefaultTab，如果当前语言不是默认语言，则禁用
    },
    rules() {
      return this.setupLangValidationRules(
        {
          sceneId: [
            {
              required: true,
              message: '请选择所属场景',
              trigger: 'blur'
            }
          ],
          sort: [
            {
              required: true,
              message: '请输入排序号',
              trigger: 'blur'
            }
          ]
        },
        ['packTypeName'],
        {
          packTypeName: '请输入套餐类型名称'
        }
      )
    },
    filteredDevices() {
      if (!this.deviceSearchKeyword) {
        return this.deviceOptions
      }
      const keyword = this.deviceSearchKeyword.toLowerCase()
      return this.deviceOptions.filter(device => device.machineTypeName.toLowerCase().includes(keyword))
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.form.langData = this.initLangData(['packTypeName'])
          this.initFormData()
        }
      },
      immediate: true
    }
  },

  methods: {
    getOptionsByDictCode,
    async initFormData() {
      try {
        // 先加载场景选项
        await this.fetchSceneOptions()
        await this.fetchFunctionOptions()
        await this.fetchPackClassifyOptions()
        // 然后初始化表单数据（编辑模式下会在formatEditDataToFormData中获取功能选项）
        if (this.editData && Object.keys(this.editData).length > 0) {
          this.formatEditDataToFormData()
        }
      } catch (error) {
        console.error('初始化表单数据失败:', error)
        this.$message.error('初始化表单数据失败，请刷新重试')
      }
    },

    formatEditDataToFormData() {
      // 处理多语言数据
      const langData = { ...this.form.langData }
      if (this.editData.packTypeInternationals && Array.isArray(this.editData.packTypeInternationals)) {
        this.editData.packTypeInternationals.forEach(item => {
          if (item.businessType === 8 && item.lang) {
            // businessType 8 为套餐类型名称
            if (!langData[item.lang]) {
              langData[item.lang] = {}
            }
            langData[item.lang].packTypeName = item.content
            langData[item.lang].packTypeId = item.id // 保存ID用于更新
          }
        })
      }
      const sceneId = this.editData.sceneId

      // 处理设备列表 - 将machineTypeList转换为machineTypeIds
      const machineTypeIds =
        this.editData.machineTypeList && Array.isArray(this.editData.machineTypeList) ? this.editData.machineTypeList.map(item => item.machineTypeId) : []
      //处理增值服务
      const fnIdList = this.editData.functionList.map(item => item.funcId)

      // 更新表单数据
      this.form = {
        ...this.form,
        langData,
        sceneId,
        functionList: fnIdList || [],
        sort: this.editData.sort || 0,
        machineTypeIds,
        packClassify: this.editData.packClassify || '',
        packTypeId: this.editData.packTypeId, // 保存套餐类型ID用于编辑
        machineTypeList: this.editData.machineTypeList || [] // 保存原始设备列表
      }

      // 同时获取设备列表和功能选项
      this.fetchDeviceOptions()
      this.fetchFunctionOptions()
    },
    async fetchSceneOptions() {
      const res = await _fetchSceneList({})
      const data = res.data.records || []
      data.forEach(item => {
        const sceneId = item.sceneId
        const sceneName = item.sceneInternationals.find(i => i.lang === this.defaultLang && i.businessType === 7)?.content || '--'
        this.sceneOptions.push({
          sceneId,
          sceneName
        })
      })
    },
    async fetchDeviceOptions() {
      try {
        const response = await _getMachineTypeForScene({
          sceneId: this.form.sceneId,
          type: '2'
        })
        this.deviceOptions = response.data.map(device => ({
          ...device,
          selected: this.form.machineTypeList ? this.form.machineTypeList.some(d => d.machineTypeId === device.machineTypeId) : false
        }))
        //v2.9.0 套餐版本，后端返回的设备列表的used字段标识该设备是否被某个套餐使用（包含当前套餐本身）,选项通过used字段来标识是否禁用
        //如果直接将used为true的设备设置为disable，会导致套餐类型编辑时，无法选择设备
        //这里需要将当前套餐自身的设备的used设置为false，所以需要将当前套餐自身的设备列表过滤出来，然后设置used为false
        const currentMachineTypeIds = this.form.machineTypeIds
        this.deviceOptions.map(device => {
          if (currentMachineTypeIds.includes(device.machineTypeId)) {
            device.used = false
          }
          return device
        })
      } catch (error) {
        console.error('获取设备列表失败', error)
        this.$message.error('获取设备列表失败，请刷新重试')
        this.deviceOptions = []
      }
    },
    async fetchFunctionOptions(sceneId = null) {
      try {
        const res = await _getFunctionsSelector()
        this.functionOptions = res.data || []
        console.log('functionOptions', this.functionOptions)
      } catch (error) {
        console.error('获取功能选项失败:', error)
        this.functionOptions = []
      }
    },
    async fetchPackClassifyOptions() {
      this.packClassifyOptions = await getOptionsByDictCode('PACK_CATEGORY')
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.formRef.validate(async valid => {
        console.log('form', this.form)
        if (valid) {
          this.loading = true
          try {
            // 构建提交数据
            const selectedDevices = this.deviceOptions.filter(device => device.selected)

            const params = {
              sceneId: this.form.sceneId,
              machineTypeIds: selectedDevices.map(device => device.machineTypeId),
              functionList: this.form.functionList,
              packClassify: this.form.packClassify,
              // 构建多语言数据
              packTypeInternationals: (() => {
                const result = []
                for (const lang in this.form.langData) {
                  const langItem = this.form.langData[lang]
                  if (langItem.packTypeName) {
                    const item = {
                      businessType: 8, // 业务类型：套餐类型名称
                      content: langItem.packTypeName,
                      lang: lang
                    }
                    // 编辑时传ID
                    if (this.isEdit && langItem.packTypeId) {
                      item.id = langItem.packTypeId
                    }
                    result.push(item)
                  }
                }
                return result
              })(),
              sort: this.form.sort,
              packTypeName: isAbroad() ? this.form.langData.en.packTypeName : this.form.langData.cn.packTypeName
            }

            // 编辑时传递套餐类型ID
            if (this.isEdit && this.form.packTypeId) {
              params.packTypeId = this.form.packTypeId
            }
            // 根据是否编辑调用不同的API
            if (this.isEdit) {
              await _updatePackageType(params)
            } else {
              await _createPackageType(params)
            }

            this.$message.success(this.isEdit ? '修改成功' : '添加成功（默认会员中心启用关闭）')
            this.handleClose()
            this.$emit('success')
          } catch (error) {
            console.error('保存失败', error)
            this.$message.error('保存失败')
          } finally {
            this.loading = false
          }
        }
      })
    },
    handleSceneChange(val) {
      this.form.machineTypeIds = []
      this.form.functionList = []
      this.fetchDeviceOptions()
    },

    // 处理全选设备
    handleSelectAllDevices(val) {
      this.filteredDevices.forEach(device => {
        if (!device.used || device.sceneId === this.form.sceneId) {
          device.selected = val
        }
      })
    },
    handleDeviceSelect() {
      // 更新全选状态
      const availableDevices = this.filteredDevices.filter(device => !device.used || device.sceneId === this.form.sceneId)
      const selectedDevices = availableDevices.filter(device => device.selected)
      this.selectAllDevices = availableDevices.length > 0 && selectedDevices.length === availableDevices.length
    }
  }
}
</script>

<style lang="scss" scoped>
.add-edit-dialog {
  ::v-deep .el-dialog__body {
    max-height: 800px;
    overflow-y: auto;
    padding: 20px;
  }
}

.form-container {
  display: flex;
  .right-form {
    flex: 1;
  }
  ::v-deep .el-tabs--left,
  .el-tabs--right {
    overflow: visible;
  }
}

.lang-tabs {
  ::v-deep .el-tabs__header .el-tabs__nav-scroll .el-tabs__item {
    text-align: center;
    height: auto;
    padding: 5px 10px 5px 0px;
    &:nth-child(2) {
      margin-left: 0px !important;
    }
    &.is-active {
      font-weight: normal;
    }
  }
}

.form-tip {
  color: #909399;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
}

.sort-number {
  display: flex;
  align-items: center;
  :deep(.el-input-number.is-controls-right .el-input-number__increase) {
    border-radius: 0 8px 0 0;
  }
  :deep(.el-input-number.is-controls-right .el-input-number__decrease) {
    border-radius: 0 0 8px 0;
  }
}

.sort-controls {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
}

.sort-controls .el-button {
  padding: 4px;
  margin: 0;
}

.device-selection-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
}

.search-box {
  margin-bottom: 15px;
}

.select-all {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.device-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
  max-height: 250px;
  overflow-y: auto;
}

.device-item {
  padding: 5px;
}

@media (max-width: 768px) {
  .device-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
