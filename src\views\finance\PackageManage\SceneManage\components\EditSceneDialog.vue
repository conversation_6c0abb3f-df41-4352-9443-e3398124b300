<template>
  <el-dialog title="编辑场景" :visible.sync="dialogVisible" width="900px" @close="handleClose" :close-on-click-modal="false" class="edit-scene-dialog">
    <div class="form-container">
      <!-- 左侧语言切换Tab -->
      <el-tabs v-if="isAbroad()" v-model="currentLang" tab-position="left" class="lang-tabs" :before-leave="handleBeforeLeave">
        <el-tab-pane v-for="lang in languageOptions" :key="lang.value" :label="lang.label" :name="lang.value"></el-tab-pane>
      </el-tabs>

      <div class="right-form">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px" label-position="left" style="width: 100%">
          <!-- 场景名称 -->
          <el-form-item label="场景名称" :prop="`langData.${currentLang}.sceneName`">
            <el-input v-model="form.langData[currentLang].sceneName" placeholder="请输入场景名称" />
          </el-form-item>

          <!-- 排序号 - 只有英语环境下可编辑 -->
          <el-form-item label="排序号" prop="sort">
            <el-input-number v-model="form.sort" :min="0" :controls="true" :disabled="disabled" />
          </el-form-item>

          <!-- 设备关联 - 只有英语环境下可编辑 -->
          <el-form-item label="关联设备">
            <div class="search-box">
              <el-input prefix-icon="el-icon-search" :disabled="disabled" v-model="deviceSearchKeyword" placeholder="输入设备型号进行搜索" clearable />
            </div>
            <div class="device-selection-container">
              <div class="select-all">
                <el-checkbox :disabled="disabled" v-model="selectAllDevices" @change="handleSelectAllDevices">全选当前结果</el-checkbox>
              </div>
              <div class="device-grid">
                <div v-for="device in filteredDevices" :key="device.machineTypeId" class="device-item">
                  <el-checkbox v-model="device.selected" :disabled="device.used || disabled" @change="handleDeviceSelect">
                    {{ device.machineTypeName }}
                  </el-checkbox>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { _updateScene, _getMachineTypeForScene } from '@/api/scene'
import multiLanguageMixin from '@/mixins/multiLanguage'

export default {
  name: 'EditSceneDialog',
  mixins: [multiLanguageMixin],
  props: {
    visible: {
      type: Boolean,
      default: false
    },

    editData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      deviceSearchKeyword: '',
      selectAllDevices: false,
      form: {
        sceneId: null,
        langData: {},
        sort: 0,
        machineTypeList: [],
        enableStatus: false
      },
      deviceOptions: []
    }
  },
  computed: {
    disabled() {
      return !this.onDefaultTab //mixin的computed中设置了onDefaultTab，如果当前语言不是默认语言，则禁用
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    rules() {
      //mixin中设置了基本的多语言验证规则，这里只需要设置场景名称的验证规则
      return this.setupLangValidationRules({}, ['sceneName'], {
        sceneName: '请输入场景名称'
      })
    },
    filteredDevices() {
      if (!this.deviceSearchKeyword) {
        return this.deviceOptions
      }
      const keyword = this.deviceSearchKeyword.toLowerCase()
      return this.deviceOptions.filter(device => device.machineTypeName.toLowerCase().includes(keyword))
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          try {
            this.form.langData = this.initLangData(['sceneName', 'sceneNameId'])
            this.initForm()
            this.fetchDevices()
          } catch (error) {
            console.error('Error initializing form:', error)
            this.$message.error('初始化表单失败，请重试')
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化表单
    initForm() {
      this.resetCurrentLang()
      if (this.editData) {
        const langData = this.form.langData
        this.form = {
          ...this.editData,
          langData
        }
        if (this.editData.sceneInternationals) {
          this.formatLangData(this.editData.sceneInternationals)
        }
      }
    },
    // 将多语言数据格式化为langData方便使用, langData是多语言数据数组,
    // 格式是"sceneInternationals": [
    // 			{
    // 				"businessType":7, //1.套餐名称2.套餐内容 3.广告url 4. 广告image 5.身份类型 6.身份选项 7.场景 8.套餐类型
    // 				"content":"123",
    // 				"id":"1942772209437147137",
    // 				"lang":"cn"
    // 			}
    // 		]
    formatLangData(langData) {
      if (!langData || !Array.isArray(langData)) {
        return
      }
      langData.forEach(item => {
        if (item.businessType === 7 && item.lang && this.form.langData[item.lang]) {
          this.form.langData[item.lang].sceneName = item.content || ''
          this.form.langData[item.lang].sceneNameId = item.id // 保留原有的ID
        }
      })
    },
    // 获取设备列表
    async fetchDevices() {
      try {
        const response = await _getMachineTypeForScene({
          sceneId: this.form.sceneId,
          type: '1'
        })
        this.deviceOptions = response.data.map(device => ({
          ...device,
          selected: this.form.machineTypeList ? this.form.machineTypeList.some(d => d.machineTypeId === device.machineTypeId) : false
        }))
        //v2.9.0 套餐版本，后端返回的设备列表的used字段标识该设备是否被某个套餐使用（包含当前套餐本身）,选项通过used字段来标识是否禁用
        //如果直接将used为true的设备设置为disable，会导致套餐类型编辑时，无法选择设备
        //这里需要将当前套餐自身的设备的used设置为false，所以需要将当前套餐自身的设备列表过滤出来，然后设置used为false
        const currentMachineTypeIds = this.form.machineTypeList.map(item => item.machineTypeId)
        console.log('currentMachineTypeIds', currentMachineTypeIds)
        this.deviceOptions.map(device => {
          if (currentMachineTypeIds.includes(device.machineTypeId)) {
            device.used = false
          }
          return device
        })
      } catch (error) {
        console.error('获取设备列表失败', error)
        this.$message.error('获取设备列表失败，请刷新重试')
        this.deviceOptions = []
      }
    },

    // 处理全选设备
    handleSelectAllDevices(val) {
      this.filteredDevices.forEach(device => {
        if (!device.used || device.sceneId === this.form.sceneId) {
          device.selected = val
        }
      })
    },

    // 处理设备选择
    handleDeviceSelect() {
      // 更新全选状态
      const availableDevices = this.filteredDevices.filter(device => !device.used || device.sceneId === this.form.sceneId)
      const selectedDevices = availableDevices.filter(device => device.selected)
      this.selectAllDevices = availableDevices.length > 0 && selectedDevices.length === availableDevices.length
    },

    // 重置表单
    resetForm() {
      this.form = {
        sceneId: null,
        langData: this.initLangData(['sceneName', 'sceneNameId']),
        sort: 0,
        machineTypeList: [],
        enableStatus: false
      }
      this.deviceSearchKeyword = ''
      this.selectAllDevices = false
      this.$refs.formRef && this.$refs.formRef.resetFields()
    },

    // 关闭弹窗
    handleClose() {
      this.resetForm()
      this.$emit('update:visible', false)
    },

    // 提交表单
    async handleSubmit() {
      this.$refs.formRef.validate(async valid => {
        if (valid) {
          this.loading = true
          try {
            // 构建提交数据
            const selectedDevices = this.deviceOptions.filter(device => device.selected)
            const params = {
              sceneId: this.form.sceneId,
              machineTypeList: selectedDevices.map(device => ({
                machineTypeId: device.machineTypeId
              })),
              // 构建多语言数据
              sceneInternationals: (() => {
                const result = []
                for (const lang in this.form.langData) {
                  const langItem = this.form.langData[lang]
                  if (langItem.sceneName) {
                    const item = {
                      businessType: 7, // 业务类型：场景名称
                      content: langItem.sceneName,
                      lang: lang
                    }
                    // 只有编辑已存在的数据时才传ID
                    if (langItem.sceneNameId) {
                      item.id = langItem.sceneNameId
                    }
                    result.push(item)
                  }
                }
                return result
              })(),
              sort: this.form.sort
            }
            await _updateScene(params)
            this.$message.success('保存成功')
            this.handleClose()
            this.$emit('success')
          } catch (error) {
            console.error('保存失败', error)
            this.$message.error('保存失败')
          } finally {
            this.loading = false
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-scene-dialog {
  ::v-deep .el-dialog__body {
    overflow-x: hidden;
  }
}

.form-container {
  display: flex;
  .right-form {
    flex: 1;
  }
  ::v-deep .el-tabs--left,
  .el-tabs--right {
    overflow: visible;
  }
}

.lang-tabs {
  ::v-deep .el-tabs__header .el-tabs__nav-scroll .el-tabs__item {
    text-align: center;
    height: auto;
    padding: 5px 10px 5px 0px;
    &:nth-child(2) {
      margin-left: 0px !important;
    }
    &.is-active {
      font-weight: normal;
    }
  }
}

.search-box {
  margin-bottom: 10px;
}

.device-selection-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;

  .select-all {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .device-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;

    .device-item {
      .occupied-tip {
        color: #909399;
        font-size: 12px;
      }
    }
  }
}
</style>
