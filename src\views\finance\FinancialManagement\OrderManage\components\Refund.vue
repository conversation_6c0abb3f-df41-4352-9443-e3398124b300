<template>
  <div class="refund-detail">
    <div class="refund-detail-title">退款信息</div>
    <el-form ref="refForm" label-width="90px" :inline="true" :model="form" :rules="rules">
      <el-form-item class="form-item-applyAmount" label="退款金额：" prop="applyAmount">
        <el-input v-model.trim="form.applyAmount" clearable placeholder="请输入退款金额"></el-input>
        <span class="tips">退款订单不能超过订单金额</span>
      </el-form-item>
      <br />
      <div class="sms-info" v-if="orderInfo1.packageType === 16">
        <div class="sms-info-item ">
          <span class="sms-info-label">套餐短信：</span>
          <span class="sms-info-content ">{{ orderInfo1.totalSum }}</span>
        </div>
        <div class="sms-info-item ">
          <span class="sms-info-label">短信余额：</span>
          <span class="sms-info-content ">{{ orderInfo1.totalSum ? orderInfo1.totalSum - orderInfo1.usedSum : '--' }}</span>
        </div>
        <br />
        <div class="sms-info-item ">
          <span class="sms-info-label">套餐价格：</span>
          <span class="sms-info-content ">{{ orderInfo1.salePrice }}</span>
        </div>
        <div class="sms-info-item ">
          <span class="sms-info-label">套餐余额：</span>
          <span class="sms-info-content ">{{ packageBalance }}</span>
        </div>
        <div class="sms-info-tips">短信告警类退款金额不建议超过订单内的套餐余额</div>
        <div class="sms-info-tips">套餐余额= 套餐价格 - [ (套餐价格/套餐短信) *(套餐短信-短信余额) ]</div>
      </div>
      <el-form-item class="form-item-equity" label-width="115px" label="会员权益收回：" prop="equityRecovery">
        <!--  0：旧订单 1：新订单 （2.9.0 退款新增） -->
        <base-alert style="width: 716px;" v-if="orderInfo1.orderSign === 0">
          <span>一旦勾选退掉该权益，用户<span style="color: #FD736E;">所有订单</span> 里含有该权益的都会被收回。且不可回退</span>
        </base-alert>
        <base-alert style="width: 716px;" v-else>
          <span>一旦退款完成，该笔订单包含的权益和相应的剩余服务时长都会被收回，且不可回退。</span>
        </base-alert>
        <el-checkbox v-if="orderInfo1.orderSign === 0" :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
        <el-checkbox-group v-if="orderInfo1.orderSign === 0" v-model="form.equityRecovery" @change="handleCheckedChange">
          <el-checkbox v-for="item in recoveryList" :key="item.funcId" :label="item.funcId">{{ item.funcName }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <br />
      <el-form-item label="备注：" label-width="65px" prop="remark">
        <el-input type="textarea" style="width:660px" v-model="form.remark" :rows="3" placeholder="请输入" maxlength="200"></el-input>
      </el-form-item>
      <br />
      <el-form-item class="form-item-file" label="附件：" label-width="64px" prop="file">
        <BtnUpload
          ref="BtnUpload"
          url="/client/common/upload.do"
          :multiple="true"
          :coverUpload="false"
          :beforeUpload="beforeReceiptUpload"
          :tipMessage="'支持上传的文件类型:.rar .zip .doc .docx .pdf .jpg，单个文件不超过10Mb'"
          @on-success="handleInvoiceUrlSuccess"
          @on-remove="handleInvoiceRemove"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import BtnUpload from '@/components/Upload/BtnUpload.vue'
import { _getEquityRecoveryList, _orderRefund, _orderReRefund } from '@/api/order.js'
export default {
  components: { BtnUpload },
  props: {
    orderInfo: {
      type: Object,
      default: () => {}
    },
    orderInfo1: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        applyAmount: '',
        equityRecovery: [],
        remark: '',
        files: []
      },
      rules: {
        applyAmount: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请输入退款金额'))
              } else if (isNaN(Number(value))) {
                callback(new Error('请输入正确的金额'))
              } else if (Number(value) > Number(this.orderInfo1.amount)) {
                callback(new Error('退款金额不能超过订单金额'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        equityRecovery: [{ required: false }],
        remark: [{ required: true, message: '请输入内容', trigger: 'change' }]
      },
      recoveryList: [],
      checkAll: false,
      isIndeterminate: false
    }
  },
  computed: {
    packageBalance() {
      // 套餐价格 - [ (套餐价格/套餐短信) *(套餐短信-短信余额) ]
      if (this.orderInfo1.salePrice) {
        return Math.floor((this.orderInfo1.salePrice - (this.orderInfo1.salePrice / this.orderInfo1.totalSum) * this.orderInfo1.usedSum) * 100) / 100
      } else {
        return '--'
      }
    }
  },
  mounted() {
    this.getEquityRecoveryList()
  },
  methods: {
    handleCheckAllChange(val) {
      this.isIndeterminate = false
      this.form.equityRecovery = val ? this.recoveryList.map(item => item.funcId) : []
    },
    handleCheckedChange(value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.recoveryList.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.recoveryList.length
    },
    handleSubmit(reRefund = false) {
      this.$refs['refForm'].validate(valid => {
        console.log(valid, 'valid--------------')
        if (valid) {
          this.handleRefund(reRefund)
        }
      })
    },
    async handleRefund(reRefund = false) {
      let params = {
        orderId: this.orderInfo.id,
        ...this.form,
        equityRecovery: this.form.equityRecovery.join(',')
      }
      const { ret, data, msg } = reRefund ? await _orderReRefund(params) : await _orderRefund(params)
      if (ret === 1) {
        this.$message.success('提交成功')
        this.$emit('success')
      } else {
        this.$message.error(msg || '提交失败')
      }
    },
    async handleInvoiceUrlSuccess({ type, file, data }) {
      this.form.files.push({
        fileName: data.fileName,
        url: data.viewUrl
      })
    },
    handleInvoiceRemove() {
      this.form.files = []
    },
    beforeReceiptUpload(file) {
      let type = file.type || (file.raw && file.raw.type) || ''
      console.log(type, 't----------')
      // let isValid = ['application/pdf'].indexOf(type)
      let isValid = true
      if (file.size / 1024 / 1024 > 10) {
        this.$message.warning('文件大小不能超过10MB')
        return false
      }
      return true
    },
    async getEquityRecoveryList() {
      const { ret, data } = await _getEquityRecoveryList({ busTradeNo: this.orderInfo.outTradeNo })
      if (ret === 1) {
        this.recoveryList = data
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.refund-detail {
  &-title {
    font-weight: 500;
    margin-bottom: 20px;
  }
  .el-form {
    padding-left: 15px;
  }
  .form-item-applyAmount {
    ::v-deep .el-form-item__content {
      position: relative;
      .tips {
        font-weight: 400;
        font-size: 12px;
        color: #707eae;
        line-height: 14px;
        position: absolute;
        right: -159px;
        top: 10px;
      }
    }
  }
  .form-item-equity {
    ::v-deep .el-form-item__content {
      padding-left: 10px;
    }
    .el-checkbox-group {
      margin-top: -10px;
    }
  }
  ::v-deep .form-item-file {
    .el-upload-list__item-name {
      max-width: 500px;
    }
  }
  .sms-info {
    &-item {
      display: inline-block;
      width: 200px;
      line-height: 15px;
      margin-bottom: 18px;
      color: #1b2559;
    }
    &-tips {
      color: #707eae;
      line-height: 15px;
      margin-bottom: 8px;
    }
  }
}
</style>
