<template>
  <!-- 搜索表单 -->
  <div class="search-form">
    <el-form :inline="true" :model="searchConditions" class="search-form-inline">
      <el-form-item label="场景名称">
        <el-select v-model="searchConditions.sceneId" placeholder="请选择场景名称" style="width: 200px" @change="handleChange">
          <el-option v-for="item in sceneOptions" :key="item.sceneId" :label="item.sceneName" :value="item.sceneId" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备型号">
        <el-select
          v-model="searchConditions.machineTypeIds"
          placeholder="请选择设备型号"
          clearable
          multiple
          filterable
          collapse-tags
          style="width: 250px"
          @change="handleChange"
        >
          <div style="padding: 8px 20px; border-bottom: 1px solid #e4e7ed;">
            <el-checkbox :value="isAllSelected" @change="handleSelectAll">
              全选
            </el-checkbox>
          </div>
          <el-option-group v-for="group in deviceOptions" :key="group.label" :label="group.label">
            <el-option v-for="item in group.options" :key="item.machineTypeId" :label="item.machineTypeName" :value="item.machineTypeId" />
          </el-option-group>
        </el-select>
      </el-form-item>
      <el-form-item label="启用状态">
        <el-select v-model="searchConditions.enableStatus" placeholder="请选择启用状态" style="width: 200px" @change="handleChange">
          <el-option label="启用" :value="true" />
          <el-option label="禁用" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { _fetchSceneList } from '@/api/scene'
import { _getMachineType } from '@/api/packageType'
import { isAbroad } from '@/utils/judge'
import { processDeviceListForMultiSelect, isAllDevicesSelected } from '@/utils/deviceHelper'

export default {
  data() {
    return {
      searchConditions: {
        machineTypeIds: [] // 改为数组形式存储多选的设备ID
      },
      sceneOptions: [],
      deviceOptions: [], // 分组后的设备选项
      allDeviceIds: [] // 所有设备ID列表，用于全选判断
    }
  },
  computed: {
    defaultLang() {
      return isAbroad() ? 'en' : 'cn'
    },
    // 是否全选
    isAllSelected() {
      return isAllDevicesSelected(this.searchConditions.machineTypeIds, this.allDeviceIds)
    }
  },
  methods: {
    handleSearch() {
      this.$emit('search', this.searchConditions)
    },
    handleReset() {
      // 清空自己的搜索条件
      this.searchConditions = {
        machineTypeIds: []
      }
      // 通知父组件进行重置
      this.$emit('reset')
    },

    // 统一处理条件变化
    handleChange() {
      this.handleSearch()
    },

    // 处理全选/取消全选
    handleSelectAll(checked) {
      if (checked) {
        // 全选：将所有设备ID赋值给选中数组
        this.searchConditions.machineTypeIds = [...this.allDeviceIds]
      } else {
        // 取消全选：清空选中数组
        this.searchConditions.machineTypeIds = []
      }
      this.handleChange()
    },

    async fetchSceneOptions() {
      const res = await _fetchSceneList({})
      const data = res.data.records || []
      data.forEach(item => {
        const sceneId = item.sceneId
        const sceneName = item.sceneInternationals.find(i => i.lang === this.defaultLang && i.businessType === 7)?.content || '--'
        this.sceneOptions.push({
          sceneId,
          sceneName
        })
      })
    },

    async fetchDeviceOptions() {
      try {
        const res = await _getMachineType()
        if (res.ret === 1 && res.data) {
          // 使用新的工具函数处理设备数据，实现分组
          const { options, allDeviceIds } = processDeviceListForMultiSelect(res.data)
          this.deviceOptions = options
          this.allDeviceIds = allDeviceIds
        }
      } catch (error) {
        console.error('获取设备列表失败:', error)
        this.$message.error('获取设备列表失败，请刷新重试')
      }
    }
  },
  created() {
    this.fetchSceneOptions()
    this.fetchDeviceOptions()
  }
}
</script>
<style lang="scss" scoped>
.ml-30 {
  margin-left: 30px;
}
.swd-search-header-container-right {
  float: right;
}
</style>
