<template>
  <el-form class="c-abroad-form" :inline="true">
    <!-- 邮箱 -->
    <el-form-item :label="$t('lg.email') + ':'">
      <el-input :placeholder="$t('MJMdS_gSDGYTeKt7KPuKb')" clearable v-model.trim="form.email" @input="input"></el-input>
    </el-form-item>
    <!-- imei -->
    <el-form-item :label="$t('TlgmpSebIew_qw82Z7273') + ':'">
      <el-input :placeholder="$t('3aj3cEG2NB2VfffUFAdC_')" clearable v-model.trim="form.imei"></el-input>
    </el-form-item>
    <!-- timePicker -->
    <el-form-item :label="$t('registrationTime') + ':'">
      <base-date-picker
        class="data-picker-wdth"
        v-model="pickDateArr"
        type="datetimerange"
        range-separator="~"
        :start-placeholder="$t('XbFegIO6XdtwVCLj3tHjn')"
        :end-placeholder="$t('k3rb7GIYeArL-6QUB2jYR')"
        :default-time="['00:00:00', '23:59:59']"
        :format="systemDateFormat"
        prefix-icon="el-icon-date"
        @change="handleDateChange"
        :picker-options="pickerOptions"
      >
      </base-date-picker>
    </el-form-item>
    <!-- btn -->
    <el-form-item>
      <base-button type="primary" @click="handleSearch">
        {{ $t('lg.query') }}
      </base-button>
      <base-button color="#606266FF" type="default" icon="reset" class="device-button__reset" @click="handleReset">
        {{ $t('lg.reset') }}
      </base-button>
    </el-form-item>
    <el-form-item style="float: right;">
      <el-tooltip popper-class="device-client-icon" class="item" effect="dark" :content="$t('8Hy4f3sEGqYcZA0E2Tgwm')" placement="top">
        <el-button type="primary" :loading="exportLoading" style="float:right;" @click="$emit('downloadTableData')">
          <!-- <svg-icon v-if="!exportLoading" icon-class="client_download" class="download-icon"></svg-icon> -->
          {{ $t('8Hy4f3sEGqYcZA0E2Tgwm') }}
        </el-button>
      </el-tooltip>
    </el-form-item>
  </el-form>
</template>

<script>
import { mapState } from 'vuex'
import pickerOptionMixinx from '@/mixins/pickerOptions.js'

import { DateFromLocalToUTC } from '@/utils/common.js'
import dayjs from 'dayjs'

export default {
  mixins: [pickerOptionMixinx],
  props: {
    form: {
      type: Object,
      default: () => {}
    },
    exportLoading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      systemDateFormat: state => state.user.systemDateFormat
    })
  },
  data() {
    return {
      isCNLang: null, // 中文状态
      pickDateArr: [
        dayjs()
          .subtract(2, 'days')
          .format('YYYY-MM-DD 00:00:00'),
        dayjs().format('YYYY-MM-DD 23:59:59')
      ]
    }
  },
  created() {
    this.isCNLang = this.$cookies.get('lang') === 'cn' || this.$cookies.get('lang') === 'tw' ? true : false
  },
  methods: {
    input(value) {
      this.form.email = value.replace(/[\u4E00-\u9FA5\uF900-\uFA2D]/g, '')
    },
    // 获取设备服务商id
    // this.$refs.UserTree.inputClear() // 重置设备服务商
    getSelectOperateUser(user) {
      this.form.serviceProviderId = user.id
    },
    handleDateChange(e) {
      if (this.pickDateArr) {
        this.form.startTime = DateFromLocalToUTC(this.pickDateArr[0])
        this.form.endTime = DateFromLocalToUTC(this.pickDateArr[1])
      } else {
        this.form.startTime = null
        this.form.endTime = null
      }
    },
    handleSearch() {
      if (this.form.phoneNumber && !/^[0-9]*$/.test(this.form.phoneNumber)) {
        this.$message.warning(this.$t('xzJrxRX1dNU-Rb_0eqU-7'))
        return
      }
      if (this.form.imei && !/^[0-9a-zA-Z]*$/.test(this.form.imei)) {
        this.$message.warning(this.$t('_L3SCIlbztecHYB9zhGmZ'))
        return
      }
      this.$emit('search')
    },
    handleReset() {
      // this.$refs.UserTree.inputClear() // 重置设备服务商
      this.pickDateArr = []
      this.form.phoneNumber = ''
      this.form.serviceProviderId = this.$cookies.get('bisUserId')
      this.form.imei = ''
      this.form.startTime = null
      this.form.endTime = null
      this.$emit('search')
    }
  }
}
</script>

<style lang="scss" scoped>
.data-picker-wdth {
  width: 210px;
}

.el-form-item.el-form-item--small {
  margin-right: 30px;
}
.input-with-select {
  width: 246px;
}
.c-abroad-form {
  margin-bottom: 20px;
}
</style>
