<template>
  <div style="height: 100%;overflow-y: auto;">
    <div class="breadcrumb">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/ad/manager' }">{{ $t('lg.limits.ad_manager') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ $t('4SdxzgE6mEOHAhf-kAa1i') }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="ad-detail-container">
      <div class="ad-detail-content">
        <el-tabs v-model="activeTab">
          <el-tab-pane :label="$t('S8KzvULqkjLUEUDggUP7O')" name="basic">
            <basic-info
              :ad-info="adInfo"
              @publish="handlePublish"
              @edit="handleEdit"
              @delete="handleDelete"
              @offline="handleOffline"
              @rollback="handleRollback"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('sWZVMsKpDLm7Zo_TUC1zI')" name="analysis">
            <data-analysis :ad-info="adInfo" :ad-status="adInfo.status" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <AdConflictDialog
      v-if="conflictDialogVisible"
      :visible.sync="conflictDialogVisible"
      :ad-type="adInfo.adType"
      :ad-list="conflictAdsList"
      @close="handleConflictDialogClose"
    />
  </div>
</template>

<script>
import BasicInfo from './BasicInfo.vue'
import DataAnalysis from './DataAnalysis.vue'
import { _fetchAdDetail, _deleteAd, _updateAdStatus } from '@/api/ads'
import AdConflictDialog from '@/views/Operation/AdManager/components/AdConflictDialog.vue'
import { publishAd, offlineAd, rollbackAd } from '@/views/Operation/AdManager/utils'
export default {
  name: 'AdDetail',
  components: {
    BasicInfo,
    DataAnalysis,
    AdConflictDialog
  },
  data() {
    return {
      activeTab: 'basic',
      adInfo: {},
      conflictDialogVisible: false,
      conflictAdsList: []
    }
  },
  mounted() {
    this.getAdDetail()
  },
  methods: {
    async getAdDetail() {
      try {
        const id = this.$route.query.id
        if (!id) {
          this.$message.error(this.$t('GJ8mxuRz8wIpmKeftNX8V'))
          this.$router.replace('/ad/manager')
          return
        }
        const res = await _fetchAdDetail(id)
        this.adInfo = res.data || null
      } catch (error) {
        this.$message.error(this.$t('GJ8mxuRz8wIpmKeftNX8V'))
      }
    },
    handleEdit() {
      this.$router.push(`/ad/addedit?id=${this.adInfo.id}`)
    },
    async handleDelete() {
      try {
        await this.$confirm(this.$t('1ozcMqmPXf7ML4ahZPcz-'), this.$t('lg.notification'), {
          type: 'warning'
        })
        await _deleteAd(this.adInfo.id)
        this.$message.success(this.$t('cvyH4iJuWCiLfB_Wsn4Nn'))
        this.$router.push('/ad/manager')
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(this.$t('cUE4imnis7N3pb9QDkywH'))
        }
      }
    },
    async handlePublish() {
      try {
        await this.$confirm(this.$t('ehydDkOY8K6pbQMdrYdvw'), this.$t('lg.notification'), {
          type: 'warning'
        })
        await publishAd({
          id: this.adInfo.id,
          onSuccess: () => {
            this.$message.success(this.$t('8cuHAuf6V9y1S0ktrujuE'))
            this.getAdDetail()
          },
          onError: msg => {
            this.$message.error(msg || this.$t('9qwy42KyBhnFOtUfCP4L5'))
          },
          onConflict: conflictList => {
            this.conflictAdsList = conflictList
            this.conflictDialogVisible = true
          }
        })
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(this.$t('9qwy42KyBhnFOtUfCP4L5'))
        }
      }
    },
    async handleOffline() {
      try {
        await this.$confirm(this.$t('TFWMEXmNyu3JWAhFrUI5c'), this.$t('lg.notification'), {
          type: 'warning'
        })
        await offlineAd({
          id: this.adInfo.id,
          onSuccess: () => {
            this.$message.success(this.$t('46L3Sa2n3_HRwD4n9EMdB'))
            this.getAdDetail()
          },
          onError: msg => {
            this.$message.error(msg || this.$t('ta6lpPTi4NhVLI6x0xtqy'))
          }
        })
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(this.$t('ta6lpPTi4NhVLI6x0xtqy'))
        }
      }
    },
    async handleRollback() {
      try {
        await this.$confirm(this.$t('9-KD5m4BVoNYl4cRLd00f'), this.$t('lg.notification'), {
          type: 'warning'
        })
        await rollbackAd({
          id: this.adInfo.id,
          onSuccess: () => {
            this.$message.success(this.$t('WUHni97FVPSbV261NuNyM'))
            this.getAdDetail()
          },
          onError: msg => {
            this.$message.error(msg || this.$t('52qKTZxNp_787WjWjlGIw'))
          }
        })
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(this.$t('52qKTZxNp_787WjWjlGIw'))
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.breadcrumb {
  height: 20px;
  border-radius: $containerRadius;
  margin: 15px 20px -8px 20px;
}
.ad-detail-container {
  box-sizing: border-box;
  margin: 16px 16px 0 16px;
  padding: 16px 16px 0 16px;
  background: #fff;
  border-radius: 4px;
  // ::v-deep .el-tabs__content {
  //   height: calc(100vh - 210px);
  //   overflow-y: auto;
  // }
  .ad-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }

    .operation-btns {
      .el-button {
        margin-left: 12px;
      }
    }
  }
}
</style>
