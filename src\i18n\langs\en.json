{"language": "英语（此字段不使用，备注）", "lg": {"sun": "Sun.", "mon": "Mon.", "tue": "<PERSON><PERSON>.", "wed": "Wed.", "thu": "<PERSON><PERSON>.", "fri": "Fri.", "sat": "Sat.", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "loginTimeout": "Login time out!", "_year": "y", "_month": "m", "_day": "d", "_hour": "h", "_minute": "m", "_second": "s", "_yes": "yes", "_no": "no", "error503": "Frequent operation, please try again later!", "carlogremark1": "Newly generated ordinary import points to", "carlogremark2": "Newly generated lifetime lead-in points to", "carlogremark3": "Creat annual card for", "carlogremark4": "Create lifelong card for", "carlogremark5": "Import one year of equipment to", "carlogremark6": "Import lifetime equipment to", "carlogremark7": "Renew the annual card to", "carlogremark8": "Renew the lifetime card to", "carlogremark9": "Transfer ordinary lead-in points to", "carlogremark10": "Transfer the lifelong induction point to", "carlogremark11": "Tranfer annual card to", "carlogremark12": "Tranfer lifelong card to", "carlogremark13": "Newly generated common lead-in point", "carlogremark14": "Newly generated lifetime lead-in point", "carlogremark15": "Create annual card", "carlogremark16": "Create lifelong card", "carlogremark17": "Transfer common lead-in point", "carlogremark18": "Transfer lifelong induction point", "carlogremark19": "Transfer annual card", "carlogremark20": "Transfer lifelong card", "inputContent": "Please enter content", "card": "Card", "customer": "Customer", "importPoint": "Import Card", "renew": "<PERSON>w", "cardgenerate": "Card Generate", "cardtrans": "Card Transfer", "cardback": "Get card back", "superior": "Subordinate superior", "commonImportPoint": "Import Card", "lifetimeImportPoint": "Import Card to lifelong", "annualcard": "Annual card", "lifelongcard": "Lifelong card", "cardtype": "Card type", "incomeexpend": "statement of income and expenditure", "inandouttype": "Income/ Expenditure Type", "balance": "Balance", "carbalance": "Card balance", "chosetarget": "Please choose target customer", "generateIsZero": "The number of cards generated is 0 !", "transIsZero": "The number of cards transferred is 0!", "recycleIsZero": "The number of cards recycled is 0!", "transoneyeartip": "The number of import card transferred in a year cannot be greater than the balance!", "translifelongtip": "The number of Import card to lifelong cannot be greater than the balance!", "transannualcardtip": "The number of transferred annual cards cannot be greater than the balance!", "translifetimecardtip": "The number of transferred lifetime cards cannot be greater than the balance!", "recycleoneyeartip": "The number of recycled import cards in a year cannot be greater than the balance!", "recyclelifelongtip": "The number of lifelong import cards recovered cannot be greater than the balance!", "recycleannualcardtip": "The number of recycled annual cards must not exceed the balance!", "recyclelifetimecardtip": "The number of lifelong cards must not exceed the balance!", "yearCard": "Annual Card", "lifetimeOfCard": "Lifelong Card", "generatesuccess": "Successfully generated for $", "transsuccess": "Successfully transferred for $", "recyclesuccess": "Successfully recycled for $", "saleBatch": "Sales batch", "operateAccount": "The operating account", "targetAccount": "Target account", "saleTime": "Sale Time", "transNo": "Transfer Batch", "operatebyself": "Cannot operate on itself", "importRecord": "Import Record", "saleRecord": "Sales Record", "transRecord": "Transfer Record", "importBatch": "Import batch", "operateUser": "The operating user", "targetUser": "Target Customer", "batchNo": "Batch Number", "importTotal": "Total", "checkdetails": "Check Details", "query": "Enquiry", "saleTotal": "total number of sales", "selectStartTime": "Please select a start time", "selectEndTime": "Please select an end time", "totalDevice": "The total number of devices", "originaluser": "Original customer", "originalaccount": "Original customer account", "enterRole": "Please enter the role name", "inputuser": "Please input username", "details": "Details", "reqresult": "Request result", "logDetail": "Log details", "logNumber": "Log Number", "uName": "User Name", "uType": "User Type", "reqmethod": "Request method", "reqparam": "Request parameter", "month": "Month", "oneyear": "Annual", "lifetime": "Lifelong", "income": "Income", "pay": "Expenditure", "imei": "Device No.(IMEI)", "machineType": "Model", "machineName": "Device Name", "imeiOrClientOrAccount": "IMEI/Name/Account", "imeiOrUserEmpty": "IMEI Or User Empty!", "device": "<PERSON><PERSON>", "user": "User", "delAccount": "Delete account", "resetPswFailure": "Rest password failed", "notification": "Notification", "isResetPsw_a": "Sure reset the {0} password?", "resetPsw": "Reset Password", "virtualAccountTipsText": "When creating a virtual account, it is the alias account of the currently logged-in dealer account. You can set permissions for the virtual account. To create a virtual account for an User, you can first change the User type to a reseller, then log in with that reseller, create a virtual account, and then change the type back to the User.", "password": "Password", "oldPsw": "Old Password", "newPsw": "New Password", "confirmPsw": "Confirm Password", "pswNoSame": "Password no same", "pswUpdateSuccess": "Password update success!", "oldPwdWarn": "Please enter a old password", "newPwdWarn": "Please enter a new password", "pwdConfirmWarn": "Please confirm new password", "pswCheckTip": "The suggestion is a combination of 6-20 digits, letters and symbols", "pwdCheckTips1": "Suggestions are 6-20 letters,numbers or symbols", "pwdCheckTips2": "The password you entered is too simple", "pwdCheckTips3": "Your password can be more complex", "pwdCheckTips4": "Your password is secure", "pwdLevel1": "Simple", "pwdLevel2": "Middle", "pwdLevel3": "Secure", "clientName": "Customer Name", "loginAccount": "<PERSON><PERSON> Account", "status": "Status", "showAll": "Show All", "startUsing": "Enable", "stopUsing": "Disable", "search": "Search", "reset": "Reset", "newAdd": "New", "viewLimitConf": "View permission settings", "viewLimit": "View permissions", "newSysAcc": "New system account", "editSysAcc": "Edit authority  account", "virtualAcc": "Virtual account", "oriVirtualAcc": "Original virtual account", "virtualTip": "The virtual account module has been upgraded to a system account module, please create a new system account", "view": "View", "delete": "Delete", "edit": "Edit", "remark": "Remark", "createTime": "Create Time", "modifyTime": "Modify Time", "confirm": "Confirm", "serial": "No.", "title": "Title", "upload": "Upload", "all": "All", "unread": "Unread", "readed": "Read", "type": "Type", "feedback": "<PERSON><PERSON><PERSON>", "bulletin": "Platform announcement", "about": "About", "reply": "reply", "date": "Datetime", "setRead": "<PERSON>", "setAllRead": "All Read", "messageCenter": "Notification Center", "backpage": "Return to superior", "from": "From", "gpsnow": "WhatsGPS", "pleaseChoose": "Please Choose", "success": "Success", "fail": "Fail", "transfer": "Move", "newGeneration": "New", "consume": "Consume", "give": "Give", "limitconfig": "Permission settings", "role": "Roles", "rolename": "Role Name", "addRole": "New role", "editRole": "Edit role", "deleteRole": "Delete Role", "delRoleTip": "Delete this Role?", "delAccountTip": "Delect account?", "newAccountTip1": "The authority  account is similar to the old virtual account, which is the sub-account of the administrator.Administrators can create authority accounts and assign different roles to authority accounts, so that different accounts can see different contents and operations on the platform.", "newAccountTip2": "Process of creating authority account:", "newAccountTip31": "1. On the role management page, ", "newAccountTip32": "create a new role", "newAccountTip33": " and configure permissions for the role", "newAccountTip4": "2. On the authority account management page, create a new authority account and assign roles to the account.", "newRoleTip1": "Administrators can create roles and configure different operation permissions for different roles to meet business needs in different scenarios.", "newRoleTip2": "For example, configure whether a financial role has the permission to locate and monitor, whether it has the permission to add customers, whether it has the permission to modify device information, etc.", "runtime": "Running time", "locTime": "GPS time", "speedNum": "Speed(km/h)", "barCode": "Bar code", "belongCustom": "Membership", "sweepCodeTime": "Scan time", "startLoc": "Start Location", "endLoc": "End Location", "totalMileage": "Total Mileage", "totalOverSpeed": "Total Overspeed(times)", "totalStop": "Total Stop(times)", "totalOil": "Total Oil", "mileageNum": "Mileage(Km)", "stopTimes": "Parking(Times)", "offlineTime": "Range", "averageSpeed": "Average Speed", "averageOil": "Average Fuel Consumption", "fuelTimes": "Fuel Times", "fuelTotal": "Fuel Total", "fuelDate": "Fuel Date", "refuelingTime": "Refueling Time", "choseDate": "Select Date", "noData": "No Data", "machineCount": "<PERSON><PERSON> Amount", "openAccQuery": "ACC Check", "temperature1": "Temperature1", "temperature2": "Temperature2", "temperature3": "Temperature3", "run": "Driving", "selected": "Selected", "sets": "sets", "directionarray": {"0": "Due North", "1": "Northeast", "2": "Due East", "3": "Southeast", "4": "Due South", "5": "Southwest", "6": "Due West", "7": "Northwest"}, "pointedarray": {"0": "Undefined", "1": "GPS", "2": "LAC", "3": "LAC Location", "4": "WIFI Location", "5": "Differential Positioning"}, "pointType": {"0": "Point Type", "1": "Satellite Location", "2": "Compass Location", "3": "LBS Location", "4": "WIFI Location", "5": "Differential Positioning"}, "alarmType": {"0": "Alert <PERSON>", "1": "Vibration Alert", "2": "Power off Alert", "3": "Low Battery Alert", "4": "SOS Alert", "5": "Overspeed Alert", "6": "Geofence Out Alert", "7": "Displacement Alert", "8": "Low Battery Alert", "9": "Out Of Area Alert", "10": "Disassemble Alert", "11": "Disassemble Alert", "12": "Magnetic Sensing Alert", "13": "Disassemble Alert", "14": "Bluetooth Alert", "15": "Signal Shielding Alert", "16": "False Base Station Alert", "17": "Geofence In Alert", "18": "Geofence In Alert", "19": "Geofence Out Alert", "20": "Door-Open <PERSON><PERSON>", "21": "Fatigue Driving Alert", "22": "Entry Mortgage Point", "23": "Exit Mortgage Point", "24": "Mortgage Point Parking", "25": "Terminal Offline", "26": "Geofence In Alert", "27": "Geofence Out Alert", "28": "Geofence In Alert", "29": "Geofence Out Alert", "30": "Main Tank Fuel Alert", "31": "ACC on alert", "32": "ACC off alert", "33": "Collis<PERSON> Alert", "34": "Late For Work Alert", "35": "Leave Work Early Alert", "36": "<PERSON> Alert", "37": "Tilt alert", "40": "High Temperature Alert", "45": "Low Temperature Alert", "50": "Overvoltage Alert", "55": "Low voltage alert", "60": "Parking <PERSON><PERSON>", "70": "Rapid acceleration alert", "71": "Rapid deceleration alert", "72": "Sharp turn alert", "73": "Out of route alert", "74": "Line overspeed alert", "75": "Parking overtime alert", "76": "Voice alert", "77": "Terminal pull out alert", "78": "Terminal insertion alert", "79": "Trailer alert", "80": "answer the phone", "81": "Smoking alert", "82": "distracted driving", "83": "abnormal driver", "84": "forward collision alert", "85": "lane deviation", "86": "too close for the car distance", "87": "video signal loss", "88": "video frame occlusion", "89": "storage unit failure", "90": "Fatigue Driving Alert", "91": "Forward collision Alert", "92": "AC on alert", "93": "AC off alert", "94": "Car door open alert", "95": "Car door close alert", "96": "Not wearing seat belts alert", "97": "Idle speed alert", "98": "Bluetooth disconnection", "99": "Urgent assistance", "101": "Restricted driving warning", "102": "Ordinary fence alert", "107": "Rear approach alert", "108": "Left rear proximity alert", "109": "Right rear proximity alert", "110": "P1 battery warning", "111": "Jammer signal alert", "114": "<PERSON><PERSON> overspeed alarm", "116": "Subtag high temperature alert", "117": "Subtag low temperature alert", "118": "Rollover <PERSON><PERSON>", "119": "Entry notice", "120": "Notice of departure", "121": "Insufficient Fuel Alert", "122": "Slight Collision Alert", "123": "Medium Collision Alert", "124": "Serious <PERSON><PERSON><PERSON>", "125": "Aux Tank Fuel Alert"}, "bsLogType": {"车辆管理": "Fleet management", "打卡管理": "Attendance Management", "用户管理": "Customer managemen", "菜单管理": "Menu management", "角色管理": "Role managerment", "设备管理": "Devices", "录音管理": "Recording management", "围栏管理": "Fence management", "轨迹管理": "History playback", "告警管理": "Alerts management", "用户卡券": "Card", "远程控制": "remote control", "指令管理": "Commands management", "电子围栏": "Geofence", "消息提醒": "Notes", "虚拟用户": "Virtual User", "公告管理": "Announcement management", "热点管理": "FAQ", "角色模块": "Role module", "登录": "<PERSON><PERSON>g"}, "cardType": {"0": "Type", "1": "Import Card", "2": "Import card  to lifelong", "3": "<PERSON><PERSON> card", "4": "Lifelong Card"}, "userType": {"0": "Admin", "1": "Distributor", "2": "End User", "5": "Car user"}, "cardState": {"0": "Normal", "1": "Empty Number", "2": "<PERSON>er", "3": "Excessive Downtime", "4": "Unopened Package", "5": "Waiting For The Machine", "6": "GPRS To Be Opened"}, "errorCode": {"error90010": "The device is not online and the custom command failed to be sent!", "error70003": "Remote control value cannot be empty", "error70006": "Does not support or does not have the authority to issue this command", "error20001": "Vehicle ID cannot be empty"}, "logDict": {"other": "Other", "insert": "Insert", "update": "Update", "save": "Save", "delete": "Delete", "grant": "<PERSON>", "export": "Export", "import": "Import", "select": "Select", "trans": "Trans", "sale": "Sale", "renew": "<PERSON>w", "control": "Control", "login": "<PERSON><PERSON>"}, "limits": {"appdownload": "Here", "ACC_statistics": "ACC Statistics", "Account_Home": "Overview", "Add": "New", "Add_POI": "Add  POI", "Add_customer": "Add User", "Add_device_group": "Add device group", "Add_fence": "Add Geo-Fence", "Add_sharing_track": "Add sharing track", "Add_system_account": "New authority account", "Alarm_details": "<PERSON><PERSON>", "Alarm_message": "Alert message", "Alarm_overview": "<PERSON><PERSON>", "Alarm_statistics": "Alert Report", "All_news": "Station news", "activity_board": "Upcoming Events", "Associated_equipment": "Associate <PERSON><PERSON>", "Available_points": "Balance", "Barcode_statistics": "Barcode Statistics", "Batch_Import": "Batch Import", "Batch_renewal": "<PERSON><PERSON>", "Batch_reset": "Bulk Reset", "Bulk_sales": "<PERSON><PERSON> Sale", "Call_the_police": "<PERSON><PERSON><PERSON>", "Customer_details": "Customer Details", "Customer_transfer": "Move User", "Delete_POI": "Delete  POI", "Delete_account": "Delete Account", "Delete_customer": "Delete User", "Delete_device": "Delete Device", "Delete_device_group": "Delete device group", "Delete_fence": "Delete Fence", "Delete_role": "Delete Role", "Device_List": "Device List", "Device_grouping": "Manage Group", "Device_transfer": "Move Device", "Due_reminder": "Customised Reminders", "Edit_details": "Edit Details", "Equipment_management": "Devices", "My_clinet": "Devices", "Fence": "GeoFence", "Fence_management": "Fence Management", "Generate": "Create", "Generate_lead-in_points": "Create Import Card", "Generate_renewal_points": "Create Renew Card", "Have_read": "<PERSON>", "Idle_speed_statistics": "Idle Speed Statistics", "Import": "Importar", "Import_Device": "Add New IMEI", "Industry_Statistics": "Industry Statistics", "Location_monitoring": "Monitor", "Log_management": "Log", "Mark_read": "<PERSON>", "Menu_management": "<PERSON><PERSON>", "config_management": "Configuration Management", "custom_model": "Custom model", "Message_Center": "Notification Center", "Mileage_statistics": "Mileage Report", "Modify_POI": "Modify POI", "Modify_device_details": "Modify", "Modify_device_group": "Modify device group", "Modify_role": "Modify role", "Modify_sharing_track": "Modify sharing track", "Modify_user_expiration": "Batch Modify Expiry Date", "More": "More", "My_business": "Business", "My_client": "Customers", "New_role": "New role", "New_users": "Add User", "Oil_statistics": "Fuel Statistics", "POI_management": "POI management", "Points_record": "Card History", "Push": "Notification", "Quick_sale": "Quick Sale", "Renew": "<PERSON>w", "Replay": "Playback", "Role_management": "Role", "Run_overview": "Report", "Running_statistics": "Report", "Sales_equipment": "<PERSON><PERSON>", "Set_expiration_reminder": "Expiration", "Share_track": "Share history playback", "regional_statistic": "Region", "Sharing_management": "Share Management", "User_management": "User Management", "Speeding_detailed_list": "Overspeed Details", "Statistical_report": "Report", "Stay_detailed_list": "Parking Details", "System_account_management": "Authority Account", "Temperature_statistics": "Temperature Statistics", "Transfer": "Move", "Transfer_group": "Move Group", "Transfer_point": "Move Import Card", "Transfer_renewal_point": "Move Renew Card", "Trip_statistics": "Trip Report", "Status_statistics": "Status statistics", "Static_statistics": "Static statistics", "Driver_alarm": "Driver Behavior Al<PERSON>", "Driver_analysis": "Driver Analysis", "Unlink": "Cancel Geo-Fence", "View": "View", "View_POI": "View POI", "View_device_group": "View device group", "View_fence": "<PERSON>", "View_role": "View role", "View_sharing_track": "View sharing track", "Virtual_account": "Virtual account", "Voltage_analysis": "Voltage Analysis", "Voltage_statistics": "Voltage Statistics", "batch_deletion": "<PERSON><PERSON> Delete", "change_Password": "Change Password", "delete": "Delete", "edit": "Edit", "instruction": "Commands", "login": "<PERSON><PERSON>", "modify": "Update", "monitor": "Monitor", "my_account": "Home", "reset_Password": "Reset Password", "share_it": "Share Location", "sub_user": "Sub-user", "sub_customers": "Lower Customers", "track": "Tracking", "Custom_Order": "Custom Command", "GeoKey": "<PERSON><PERSON><PERSON><PERSON>", "GeoKey_Update": "Update", "GeoKey_Delete": "Delete", "GeoKey_Add": "Add", "GeoKey_View": "View", "feedback_manager": "<PERSON><PERSON><PERSON>", "feedback_list": "View", "feedback_handle": "Processing feedback", "proclamat_manager": "Announcement", "proclamat_manager_list": "View announcement", "proclamat_manager_update": "Modification announcement", "proclamat_manager_delete": "Delete announcement", "proclamat_manager_save": "New announcement", "device_update_batch_model": "Modify device model", "login_manager": "Login management", "asset_manager": "<PERSON><PERSON>", "package_manager": "Package Manager", "order_manager": "Order Manager", "financial_management": "Financial Management", "finance_center": "Finance Center", "device_record": "Record", "auth_manager": "Authority", "system_manager": "System", "point_generate": "Card generate", "point_recovery": "Get card back", "point_transfer": "Card Transfer", "device_transfer_record": "Transfer record", "device_sale_record": "Sales record", "device_import_record": "Import record", "user_point_list": "Card", "Info_Instruction": "Instructions", "Info_Question": "FAQ", "Info_AllCenter": "Notification Center", "user_point_log_list": "Check details", "car_maintain_list": "Maintenance", "REPLACE": "Replace IMEI", "punch_record_list": "Attendance Management", "Station_message": "Message", "Update_bulletin": "Update announcement", "Personal_Center": "Personal center", "task_list": "Data Export", "Write_Account": "Fill In The Account", "Find_Method": "Retrieve Method", "Contact_Provider": "Contact Provider", "device_report_offline": "Offline Statistics", "Select_Equipment": "Select Equipment", "Offline_statistics": "Offline Statistics", "Driving_Behavior": "Driving Behavior", "drive_analysis": "Driving Behavior", "Fence_statistics": "Fence Statistics", "line_manage": "Line Management", "Line_statistics": "Line Statistics", "machine_type_manage": "Device Management", "carfince_first": "Home-Auto Finance", "carfince_risk_control": "Risk control center", "carfince_monitor": "Monitoring Platform", "carfince_two_bet_manager": "Second Deposit Management", "carfince_tow_bet_point_setting": "Second Betting Point Setting", "carfince_tow_bet_alarm": "Second Call Police", "carfince_permanent_manager": "Resident Management", "carfince_permanent_verify": "Resident Management Audit", "carfince_permanent_statistics": "Resident Statistics", "carfince_permanent_alarm": "Resident <PERSON><PERSON>", "carfince_abnormal_aggregation": "Abnormal Aggregation", "carfince_parking_timeout": "Parking Timeout", "carfince_fence_alarm": "<PERSON><PERSON>", "carfince_my_client": "My Business Auto Finance", "carfince_customer_management": "Customer Management", "topic_list": "Problem Management", "topic_save": "Add a question", "topic_update": "Modify the question", "topic_delete": "Delete question", "Find_Password": "Recover Password", "Video_Monitor": "Video", "Time_Video": "Live Video", "Playback_Video": "Video Playback", "Video_Evidence": "Video Evidence", "Video_Task": "Video Tasks", "EventTask": "Event Tasks", "bigFullScreen": "Dashboard", "Capture_Center": "Capture Center", "Event_Center": "Event Center", "package_list": "Package List ", "package_proxy": "Package Agent ", "package_setting": "Package Settings", "charge_stat": "Charge statistics", "person_punch": "Personnel punch in", "driver_punch": "Driver punch in", "report_task": "Report task", "package_edit": "Package modification", "device_import": "Device import", "device_reset": "<PERSON><PERSON>", "income_detail": "statement of income and expenditure", "withrawal_detail": "Withdrawal details", "bill_detail": "Details of ledger account", "order_manage": "Order Manager", "service_order": "Service provider order", "gift_record": "Gift record", "import_record": "Import record", "provider_manage": "Service provider management", "amazon_order": "Amazon tracking number", "withrawal_verify": "Payout review", "device_info": "Device Info", "car_manager_upgrade": "Batch upgrade", "car_CarUpgrade_upgrade": "Upgrade Record", "Channel_Manage": "Channel Management", "operation_manager": "Operation Management", "ad_manager": "Ad Management", "app_update": "APP Management"}, "cancel": "Cancel", "cancelSpace": "Cancel", "submit": "Submit", "submitSpace": "Submit", "resetSpace": "Reset", "uploadType": "Please upload files of type .jpg .png .jpeg .gif", "uploadSize": "Upload file cannot be larger than 3M", "uploadAvatar": "Upload Avatar cannot be larger than 1M", "submitfail": "Submission Failed！", "fbUploadTip": "Please select feedback type", "alerttitle": "The title can not be blank!", "alertcontent": "Feedback cannot be empty!", "submitsuccess": "Submitted successfully! We will process your feedback as soon as possible~", "contact": "Contact Information", "contact_p": "Fill in your phone or email", "uploadImg": "Upload Image", "fbcontent_p": "Briefly describe the questions and comments you want to feedback, and we will continue to improve for you.", "fbcontent": "Questions and opinions", "fbType": "Feedback type", "fbType1": "Counseling", "fbType2": "Malfunction", "fbType3": "User experience", "fbType4": "New feature suggestions", "fbType5": "Others", "fbtitle": "Question and opinion title", "serviceProvide": "Service Provider", "linkMan": "Contact", "linkPhone": "Tel/Mob", "email": "Email", "address": "Address", "commonProblem": "FAQ", "instructions": "Instructions", "feeds": "<PERSON><PERSON><PERSON>", "fotbidPassword": "Change Password", "exitStytem": "Exit", "account": "Account", "operaTime": "Operating time", "ipaddr": "IP address", "businessType": "Business Type", "operateType": "Operation Type", "operator": "Operate", "time": "Time", "primaryKind": {"1": "<PERSON><PERSON>", "2": "Custom Reminders", "3": "Maintenance Reminder", "4": "Renewal Notice", "5": "Expiration Reminder", "6": "<PERSON><PERSON><PERSON>", "7": "Notice"}, "alarmType[119]": "Enter the notification"}, "remind": "Remind", "importRemind": "The equipment is being initialized. Please wait ten minutes before operating this batch of equipment to avoid equipment abnormalities.", "importRemindFile": "List of failed equipment for donation", "idleSpeedInputError": "The idle alert value is incorrectly entered", "batchImportTemplate": "Driver management batch import templates", "batchImportTip": "You can import driver information in bulk by uploading a form, the form file must be entered according to the template file header format. At the same time, please ensure that the driver number is correct and has been set to the device through the instruction;", "batchAlarmSettings": "<PERSON><PERSON>", "pay": "pay", "batchInstructions": "Batch Command Sending", "generalFunctions": "Common functions", "offlineJudgment": "Offline judgment", "offlineJudgmentTipOne": "Supports setting the offline determination time of the device. After the device is disconnected from the platform for a preset time, the platform determines that the device is offline.", "offlineJudgmentTipTwo": "If there is no setting, by default the wired device will be judged as offline if no heartbeat positioning packet is uploaded within 10 minutes; the wireless device will be judged as offline if no heartbeat positioning packet is uploaded within 30 minutes.", "expandFunctionality": "Expand functions", "expandFunctionalityTip": "Before setting up, please confirm that the device supports this type of extended function.", "resultErrorTip": "{0} devices were successfully set up, but {1} devices failed to be set up for the following reasons.", "scheduledTime": "Planning time", "plannedMileage": "Planned miles", "maintenanceStatus": "Maintenance status", "comprehensiveMaintenance": "Comprehensive maintenance", "mileageMaintenance": "Mileage maintenance", "dateMaintenance": "Date maintenance", "warned": "Already warned", "endUser": "End user", "maintenanceType": "Maintenance type", "alertDate": "Warning date", "scheduledDate": "Planned date", "offlineInputError": "Offline judgment of alert value input error", "oilOutage": "Cut off engine", "serviceNumberOrder": "Service provider's order", "purchaseQuantity": "Purchase Quantity", "iWYURze5gw6IuatRIrbE9": "Load failed", "u8042nAxHpYgsu7a58sQ6": "Transfer", "O3NI5gV9UN7-mT9gi-KHC": "Create", "elr13plxYiqSiRBQuS4As": "Return", "_c1C4zcPDX-ZWed2eRbdA": "<PERSON>ll", "TlgmpSebIew_qw82Z7273": "IMEI", "pp7ggW3s8XBX7RhFkk1_v": "User Expired", "12YEhb_OD-ZHwKO7vWITk": "Add to", "7fJLSw_PZ8o4rUM051emO": "Model", "xe_DoA8ge_gmef88_vh4p": " Platform expired", "fEKIzjDvXOhTv3JkjXuKc": "Add device", "NEfgPcoBVZIP3f585kbxV": "Renewal Type", "DrMjlHHpaxp_yFcEvOcQi": "Operation Tips: After resetting, the test data such as device activation time and trajectory will be cleared, and the device status online or offline is reset to inactive.", "DsMzz4HiiZh_zjcZCg7sv": "Quantity", "grwMQULKZ0LrPztl-Z7wr": "Purchase、Total", "6VQ3tYGZgExw1rZnn8Cbj": "Stock", "PfpLQ5G3zI6xZpZlialvg": "World", "zj98NBvqfCTJqrwUaGhji": "Devices Deployment", "Z8MQX4KbfoflrmZEy5Vj6": "Subordinate users", "JH4HcH8lq4j75he7Raack": "Device status", "fCdSYzcuCLxP0iO32z_7j": "Include Sub-account", "pKGbb1Y6o7498BtbB9dWf": "<PERSON><PERSON>", "d4TPm1qg_yKyoFhGhc49y": "Total activation", "zMqE-woZfLlDun2A-ZmhU": "Weekly new activation", "ZiuUTHENQjPPZw4IEUVOK": "Version update announcement", "QoEq85osl9LN1AmO58BiM": "Online device trend chart", "YI-CDEbvHMaK4Qo-P0C54": "This month", "mngS5pANeQga3_X0KfIkm": "This Quarter", "3QA6lHuTpmOtPdjicAlLd": "Year", "OfQYeUUlRnfql7Er4dYXT": "Sales top10", "0cT7QoohrYcbqyDW-BGwX": "7 Days", "TImMByZR7gX-T5SE4wW_C": "7 Days", "8XStVR83GO-UD-vuXceyN": "30 Days", "0gU5oOZxYWY5c8diw5GGt": "Yesterday", "w6SqBL1DnKWu5v2QWD_Qd": "<PERSON><PERSON> Add", "8XaFdQXQQQExEY6VjBIoV": "Currently {0}", "QHewpO-a1QEPaAwvF6Xzr": "Refresh", "1NfCtPMIHa5qxyRCS84S1": "Please enter the IMEI number, currently: {0}", "iAVxi7DIE0592dotpSI-W": "Please enter IMEI number", "Qd6gmq65WiHFshYKYFr4F": "Please enter 15-digit IMEI number！", "h89Z-UD7uIkZ6egxwKDSM": "3 Months", "8yaI2Muj-dOcsIgudaSgP": "12 Months", "Sg3DiQowXqC8D35OICtD8": "Are you sure you want to sell {0} devices to {1}?", "DBBhTdEjXuithUnfBj4uF": "Please enter the device", "MRqCFQeb7tjZ4vzCQR9Rn": "Reason", "dGiezzpH2ifwm00fwd6Ou": "Renewal Failed", "RnDDKP0_zNeMqMvt83zyQ": "The current {0} is less than {1}, not enough to pay! Please delete some device or add card!", "jVw447QvAeRR4NrTNp5wI": "{0}device renew successfully", "HahpWiGqvHlj74U8VvXkS": "{0} device sell successful, {1} device sell failed. The specific reasons are as follows:", "SwtmYZpc1RnK8sKvik1ND": "Are you sure you want to reset this {0} device?", "2gK1OvSK1N490nqe6BmML": "Reset Success", "mAyV3e2y7beLqKbttMEyP": "Are you sure you want to import {1} devices of model {2} for {0}?", "ZrFQNTbU10MZPjYNkDZ0c": "{0} data has been imported successfully, but {1} failed to import the data. The specific reasons are as follows:", "ZvBsO_CTD_qH1mggQdOEz": "<PERSON><PERSON>", "Fjjy4njX1Z-5GjVk2Ry8x": "Device Model", "V1DtS-jLSC38n1jMuvxaX": "Expiry time after renewal", "jhzwcMuCQSBt0s7QsRL6J": "Are you sure you want to renew {0} devices?", "93mxIMhUPjOdxz1mAg5VT": "Are you sure you want to renew {0} devices for {1}?", "8_eOeIb1VOROEQ4EsuJGG": "Successfully renewed {0} devices {1}, the validity period is as follows", "t__tQ1COBdJfeegyNEut4": "Only Oneself", "V5ni5q4iTECCMrUfdW93x": "No IMEI number exists", "5WGqM6dwWgfZNerSr08_j": "{0} devices were imported successfully", "5P76CIxqHeL97BTdshJjp": "Serial number input", "hrJVpqMEc8TKXFgGT79a_": "{0} devices were successfully sold", "xtan0lEAvk31wH5vywZIy": "Superior", "myzCJIAtsYrsBH7YLrbMC": "Add Sub-account", "8EX2meYNZa_kdhW80e8L_": "Distributor", "zjj2zR3u_qvaZPmcIQmZc": "<PERSON><PERSON>", "EGou8TaGJwBFQKJoHe1Gy": "IMEI Existing", "PdDwaW3ZMpo7a1kMh1QTQ": "Model", "iMVebDXUsA5c_fxl671kL": "Quantity", "RFQZq8sjjZ5vjV5m6CAZH": "proportion", "M-1KNMPFUhwUz1A54afuJ": "The login account has already existed", "vYM6cI35WYG617ajkDXbg": "Please select a superior user", "8L8DMNF_TiVeobqbKjzNp": "Please enter customer name", "CVRtg6QBo3_I3SdSEnOFN": "Please enter login account", "HIYRKGjbiHPM1vChmwTtS": "Login account cannot be 15 digits！", "_Ir0v6itPxvLoVX8jyypM": " Please enter password", "gjf_Ayv-A3bIgDrLUyaLl": "Confirm Password", "wqK1AUIRFGiB5eHofupGL": "Password Not Atypism!", "fXrxuyIkLD3QEgjPvKuJa": "Please select a customer", "MTr2Ks8d0Ny8erQoUJ2kJ": "Please select model", "pleaseSelectPackVersion": "Please select a package version", "60Cg-7z2oV7N30quX6B1c": "Please select platform expiration time", "lfSrcQrITYhRu4d4uMcDs": "Import results", "uIEq4TWD9LFaCJEFLt84g": "Number format error", "qyDjiNshB1P2cTPWxQcIA": "Please enter a fixed number", "n25N1-n4SySYruwj301zO": "Please enter the starting value", "8K5x8ehQZuA1mdBtxsMeY": "Please enter the end value", "t1vi6s7lcTAtqd_7kwEOT": "One IMEI For One Line", "aFq5tmk1n281NDkCSp8X6": "Membership", "E9BGOCAvkhyQewmSWvUHr": "Successfully renew for 1 year", "w0vVvEoUupXMqiJV9fCF2": "No need to renew the lifetime card", "Z-IXWYRO5QljfIOj9i_sa": "One year", "O_ft1yy-v3_td6h9X6_CF": "Sales results", "ZFrlcab8jFZcpRNRcCnMA": "The user expiration time is longer than platform expiration time imei number", "HmTLpP09V2cIjAWRcUHzQ": "Repeat IMEI", "eY-kEiHnxN-wrVrq1fVX3": "4th Floor,No.121,KeCheng Building,Science Road,Luogang District,Guangzhou", "dr1VBKYi7Au5f9nwUBwd_": "High", "yn6gOaagimB00Dh3sw0Xw": "Low", "09dzRv47cdB8ngpXdmrNA": "Area", "ZxAVznWm0Arp2GO-C5elp": "Percentage", "7fVMapQ6M_RkSzO5jDN18": "Rank", "KQBFZQ0Voyfhg6fQXT_Rn": "Name", "1JpPsfqrgrwbDs32GzHmo": "Sales(Set)", "qmmSQeOd7o-k-CKy8cGQ1": "Renewal result", "cilqXZDxJIkYAni2p6D18": "Good morning", "ySCL835cERmc-2ui46kJg": "Good afternoon", "CP6KtfpD2nRp4YsehmLV4": "Good evening", "XhZ_mUXUaEoscKh4O__52": "Sales quantity", "B-oqbyA9EYX3_b604CHoI": "Device model details", "i6izKntdBPjj1CVgTxoFv": "Total Customers", "h8y6BrYJM46qcer2hAm-e": "Direct Customers", "LMGhuoa55f6P3zBo-I6_h": "The number of devices online in the past week", "yHUUUDenNX-ZDlHH-h8Bf": "Can't span months", "c9Ois07PLUg8-LT8ypZpx": "Import failed", "qNXcCC7yboHklZOmNBtso": "The amount of devices online in the past 30 days", "oC3aVgjq_fLItbKC4dzC0": "No executable IMEI number", "6LW1YZbmhuEq6LyLyYwqR": "Insufficient new card", "_L3SCIlbztecHYB9zhGmZ": "The IMEI number is illegal", "mXaZcmYFfm7x55jOUq3WJ": "IMEI already exists", "qmNtvRFOkHtbTvTeWLK0j": "IMEI that failed to import the vehicle", "c-aC4vVpgvcu0VyfYe2dU": "Device Information", "GmeIr45NbbW42QwT7mmrf": "<PERSON> Sell", "Wylya-4JRSp679tGf_q4g": "Customer management", "nd_7J9mswRt2IfBGNjIWa": "Geofence Setting", "bo540fd46Pfi59A-x8C_R": "<PERSON><PERSON><PERSON>", "51VD4AjMhSFl6EBZnYW3g": "Dispatch Command", "bWqxJSaBirj0pJXbd4ubc": "Only view Moniter", "uTfF4X61wgUVv37Uhu0aw": "Update Success", "hiO2JmZ3oYl5uM8mflDcL": "Modifiable Function", "lD-unyqzZNFq1t9SB0eNy": "Permission Range", "1yPhethvo0Yf9j-z3fQaZ": "Account", "igFoXwa2BzYkyYAJDqU9R": "Do you want to reset password to {0}?", "XsvtYpl8vyei8PJ4zNqBV": "{0} password has been reset to 123456", "6czW74k8BArZqrVE40tKG": "Rest password failed", "KK1ozzOjBr5DpFPKLt1C6": "Do you want to delete {0}?", "HzV5269cO5xVNwIaK3De_": "Delete virtual account", "yXpq6xUfZ9hRjfzRQLSzh": "Card Type", "EshFaJOQM_Oh3ICyukZP-": "none-existed IMEi", "mN6Jwf0wh5RTbxZiGQCoR": "Devices", "sdeE2Xfr5yaOVDKBpwToH": "Growth TOP10", "7-o5VuFdLBdBRPbue62gr": "pcs", "flK64mYHgrj-k7-D2XupZ": "Online", "dtFn22xfEx789uFKvaG_n": "Offline", "dzaHeFci7OhejMnw1GSix": "inactivated", "lkPusWocD2jptbcRGKhFR": "Add virtual account", "Z9YgHkSg2uB840tCIrZyC": "people", "7C1maetdK8mVxRyzBW2H6": "Non-direct users", "2Qfae-GXcMhGQxMT0Ds6A": "Sold", "qs5MKLWLhFEFf1W5CGOCn": "Growth(set)", "kt7tkMjYVOmcxeQ8WIe42": "Unsupported card type", "vlKiesuPzDnB0GMEntyQf": "Total", "dh_0Cvxs6eGNbLMY-7v8Y": "My Account", "nHg2M0jmyailtWvemNIU2": "Subordinate", "hYo3Cd35MbLWBTXRKT_HP": "No announcement yet", "_bu8UzncFEL9nkg5qPBDs": "There are illegal characters", "3RP2ACHSyN8l-084R5ia9": "Announcement", "lOnLduR3l_8WRTpPLViDr": "<PERSON><PERSON>", "D_CFBCrWWcdam_cGkm90t": "common user", "HPZ9-4BkksYDf9bOYA7Bq": "Customers", "dGJYSHKNhX1So1amUzfdO": "Advanced", "0HfLeevl06T5-c-6Hybn9": "Batch Move", "RIiHXilwzDZZXOC0RFjS4": "Modify Information", "pWAH6OlawcOyPzpCQiD3t": "Modify Model", "fi97wBDxt-kI86UwsAgTt": "IMEI Number", "2WVzsA1lduRG2ofkXI1gv": "SIM Card", "p533TIv1HaDSMgWw1t4a_": "Plate No.", "gEN4YPGMoNz8HYKg2kgMi": "Import Time", "vys-I8F3EiJEhY84SJ2hE": "Activation Date", "ZofyuSsvgF1XR16Ey32FU": "SIM card number", "zhx-pYPkX0douf0ybSGBM": "Vehicle Information", "AmK-1t3nyDtFLUWuuGc7m": "Plate Number", "5NW3PUUAmaqAZxIV48c2V": "Fuel Consumption", "8CyJDVju_fn2eLYBfCFa5": "Overspeed Alert", "2J4Y_8V79GZz8oVP1uRLy": "ACC Alert", "dlW7RH6fkSItw_Mtz2Ykw": "Speed", "Jet1u7xx2NupyskORg_e-": "Expired", "Ht-5xfyfOJaPLfPI1Ina2": "Expired {0} days", "M_dYy2C4ldXbU0GcSh8XQ": "Unused", "IikV2EDWehRNB_DuPyoAi": "More", "ihcJF8i_680UPUSADlQBQ": "Tracking", "FNZ9KgOe3_nZPHLr-gemV": "Playback、Replay ", "nG52x-1HCtQNlZdzdGke_": "Command", "nuMj_JYBMK5JmtZA19Q7D": "<PERSON><PERSON>", "_LruCTL_GmFwkLYdKRGPL": "Share", "d2rfxOdgIK8sx-aXn_UjT": "Details", "JvE2ZJEopyBOH0nltAgpC": "Move To Group", "tJFFxuGRgmWrx8jXwGawx": "Manage Group", "E2cJxJ0qcSBiNzRMKbaj0": "Map", "IKjGxwah7gczqSoJQWeJJ": "Satellite", "eq8ubUOnx-iIN5d8flNTZ": "Traffic", "p5s_fNJw6Bfo4E59dkz8t": "Ruler", "YmEYZtN_qZQbAvfoi-EnT": "Please enter address / latitude and longitude", "sxg1Ago7Wgz102blS1Uw0": "Draw Fence", "sgH0wIb6CRMhrPErfzZNo": "Round", "YRsd0eFEx-XpEoHM4bQOf": "Polygon", "vMpgu5wFuK5er9Dlkz9fF": "Fence Name", "FMD-CUGF0TJ-UFTwcQV2j": "Association", "3bKE4RZjKEEnP-6Lvd3QO": "Group", "g0Z4WYTOSoNVw6icvU8Xa": "Icon", "a3JOJwEQkkXHUNJ9B8gzq": "Upload File", "oNh27yZ5e5LDkyOhuFrCO": "Example download", "YRfNHk3LACKCdKZKpdGFt": "You can import POI by uploading Excel file with related information. Please follow the format of example to prepare the file", "993p62YTFUY0OgmGiplEj": "Name:Required, no more than 32 characters", "SXm03GuPs_W8_bHDUIiOO": "Icon: required, enter 1,2,3,4", "WdUDofXEM4vPVut2ggyll": "Latitude：Required", "ihJPRdZfKZWbwqQg3TC6p": "Longitude：Required", "GPiUGvJyTduYFCJS0VF_W": "Group name: Optional, no more than 32 characters. If the group name is not filled in, the POI point belongs to the default group. If the filled group name is consistent with the created group name, the POI point belongs to the created group. The name of the group has not been created, the system will add the group", "K98R9Kl6t3eFtWbQBdzh-": "Remarks: Optional, no more than 50 characters", "ntUmxqogGfJUuLkjvK6ZV": "Alert <PERSON>", "CA1jOSCwNEQbOx07cHlZm": "<PERSON><PERSON>", "6ed5szctmCrXayO-9xgWW": "No clearable alert information", "A7xrOFhybj9Js9R7Oulub": "Baidu Map", "TrFNV69OZRAH0okN4sumS": "Baidu Satellite", "2yf5VqhJTX_NvDljHWexs": "Google Map", "rc-VB0jPoY98SMUVoUe7C": "Bing Map", "r9Z0vOw35FkxLAXFabw2R": "Google Satellite", "goF8T8dHU6gao6PljrtNi": "Static", "OCvfOWyVHgRIOLEA021Kl": "Moving", "CzvhWCze3pQCK5M_1KoaB": "Expired", "K_s5NFaeLyxrujarrzfMk": "Wireless", "XoaUHxf1yCHUm8G-ttmOA": "Wired", "oeP8MwRcMAnrw4rbs7DY3": "On", "dMl02a_mb9XIn8CyX3A_D": "Off", "7vdkNJd4kRZP2sQMmyxpv": "<PERSON>", "MENG0IZDkTNnl3WoRl_p5": "Report", "IJW7kt6UjfglE5oKPiJ0z": "Streetview", "gbtfYMFIkzFPpyAatuQPP": "Geo-Fence Management", "4UElrpFn7scbLfe2oYK0B": "Sub accounts", "9JFj_wyOKWTwMbNqgGrRC": "Add group", "XjJkfhK6Qd8NBMfxckAle": "Message", "H7ZxbhzZKtJsZjdkN7YJL": "The equipment service has expired, please contact the dealer for renewal!", "EDcFx0tNb2Z2qVSxFsaJh": "There is a new alert", "kbqyQYySG3XGM2bVA-4-t": "Click To View", "LtNPazzpjZ8Ki6SHzTfs6": "Search Name", "SUBLSbsZXN6Yu-QsHnH5_": "Drawing Tutorial", "AmtYv4ISEbwIRe2OC3KP3": "Round Fence", "hu_Qd-LVVX--ytlHSCPNI": "Click on the map to determine the center of the circle", "AmLlcGde2A2OHyFdaqiJa": "Drag the mouse to stretch the circular area", "qAs2oKd_k5MsKqmoor8Py": "<PERSON><PERSON> again to finish drawing", "X7_1ZjxC6jNbqqwtSdZ77": "Polygonal Fence", "B8Vp2heRHQQFC19TLbWtu": "Click on the map to punctuate", "7ptVgNAs_gId9fabwwyXY": "Double click the mouse to finish drawing", "8uvQGTTGGK4YHP6RctT_K": "<PERSON>w", "RLJHlTX2QB4puw9-yaKkg": "Lock", "AHFQWLMEP4QcejtHVMHAP": "Device Name / IMEI", "NtWULYpXIIyqfvlHmyJuU": "The device has no location information yet.", "rVAoxxyYFdp_tC9Dnyb6M": "Move Success!", "Y9r-CsONpxxRmg00jzjLm": "Defaul Group", "OJAZ-TwNVqi3Bh7hztuLC": "Tips", "cvyH4iJuWCiLfB_Wsn4Nn": "Delete Success", "kUTV5VcObCrB6kdnyfHDS": "Not positioned", "NlOXMw3tnc5FS0jChJJeY": "Due Today", "bZb0QeKns1R-YrJrCIl_h": "Days due", "a2soi5XT71DCr2H3iW75d": "Associated Already", "NprpRhvbYE9KLFSrs1Ut3": "Hour", "T3YbKXAK7yNAbyXGGy3E-": "km", "zCKyujELRDbjx5vAbAD3r": "The account has no-operation privileges", "NHp9pI1Qr5rbpe8r-4Nd4": "Please input fence name", "zhjiZcfXH8sIv6yxSkkwq": "<PERSON><PERSON><PERSON>", "q_gUoHM-7bSFFC0IuHOmI": "<PERSON><PERSON>", "Csx-q_UsjB2o0oRRZf1qz": "m", "W7-XDEPApmz5QAAGPLqQQ": "Enter", "UyLRnzSqYp4MB2K4_aOaE": "Exit", "Oy3u-IJ6TzbP9vNvGYzw7": "Fleet fence", "Y1eS06Ae8KWuwsunOTGsV": "Please select at least one alert method", "6dWutEmqMZnfcebUwMWcH": "Added successfully", "JFQKJUrmNVUCRlOW-U6Qu": "Delete", "kAKDzRgzgRbGj7TWa8W8z": "Add Group", "I8lfTbBiYbCyvuPzjkf_Z": "Add a point", "CmWMoOrWpZU6sTGtkB4QL": " Please enter a name", "vjdwbqgsFl9Te_vH9kNY5": "Not Associated", "z5sQqFDnFSiQzix1HGtIB": "Cancel association", "wNtm7LWAqF0ewVQRdPb-J": "Please select device", "zeWy2Egw7v6c3sDhGAX9c": "Delete or not:{0}", "mnCksVnc2zGhj-VRBgGPw": "<PERSON><PERSON>", "vBpDexpv7D_KNy_aaxmZx": "Address", "IjtUG09nKBjgDwMEUXJEd": "Learn More", "kGPteGn1UwCDzRpDTYOOA": "Click to confirm the location, double click to end", "LoX_Y5XcmGFXPrKocd3Vy": "Signal: The last time the device communicated with the platform", "uHD_UGEfA88QbFYjH40Sk": "Location: The last satellite positioning time of the device", "CfXyIfQ7YrDKYGs6IcEjE": "When the online device is stationary, it will not be positioned, but still communicate with the platform", "I8b_zoiaDScRSfe8AcYJQ": "{0} Delete fence or not?", "53WepRb0dgsGBYKlPWo_b": "The fence is associated with {0} devices, delete or not?", "r07mdOUMExdAaIj36JZgK": "Please select fence", "fPpUDmYmZBfijNxuYo-dT": "Delete or not?", "LsRzknqAtdhScwNBoSsHK": "Fence Information", "Y9bbn5LDAbEo9O9LPdGcH": "At least three points are required to draw a polygonal fence", "CUalv8dHu0RQdAaF3Wj-v": "Failed！please delete POI (Point of interest ) first.", "n26EViLKtRWW73EXNwoIe": "POI", "GOXwqzj3woUoTJ5EcWIfb": "Edit Group", "OraWJ32a3r19pXlnEz7LI": "Edit Point", "hnmI2xuLjvPN3UZNk3qVe": "Uploaded file format error", "zSwW84PPwEdSY7KXU0_NP": "Upload File", "MWerfQOxskhfsWhfVL7aK": "Batch Association", "jGphq0Bb5xDW59eNTBOkR": "<PERSON><PERSON>", "O8-0tOC041IMU4vRnzpNL": "Failed to generate link", "wue_UE3tLAxmsNy30nm8x": "Share link success", "tKwyVQxLCB3j3YDFNoq3y": "{1} devices have been associated with the {0} fence，", "LseoYpEYEbqOsg-3HO9uM": "Unable to get address information", "Zbkw-Z-GkLdk92q2GqQb9": "Start Time", "tjh3v6c9w4iWKRhEsxCel": "End Time", "-L6s88wO8SJ2dBT3LRNqp": "From", "CnHQ3jqzoQUIJi5VNzDUS": "To", "n98s6qScQ72Pfc3ipN7sK": "Stop Sign", "kErEgq9bY9npX-B_gR5kg": "Location Point", "ppeXkkkZ-FQrqgexHkFd2": "Remove Drift Location", "qis-Gzwt4NrDH4wyIX8FM": "End Trace", "2vbMWIgM9jMU39l_y0cMD": "Driving Mileage", "Wyr6tYmzD3CWhgVab7Bzs": "Time Selection", "WPDTnaE8GGfKGXTg7ln4o": "Interval", "90u1wbF4gTwa5j8I-u7dj": "Longitude", "MlqbfDIYF1BFYrrm6l_F3": "Latitude", "kYg8FK3sHxS7WWLlmHxRs": "Direction", "lPdLzI8-RihRdC19Sj793": "Positioning Mode", "SKow9wO-zFrJ-bTY4iiNk": "Position", "Ii_xMJ-aK06I6WjE_7RvK": "Signal", "XbMQZa2UhMAnIgRdfYmBE": "Today", "NZn8Y-nZV3WRZLds93mmP": "<PERSON><PERSON>", "XuTgmO9YYX9ZgzdosA8t3": "<PERSON><PERSON><PERSON>", "3DYXD-6GdyKqIdA4QkIPq": "Address resolution...", "USsDC30U686JAi9DNifpb": "Please select device", "KKjrKI9Bincd6VUnZIqbE": "Start Time Msg", "N9aI7K4jam3xJAzQDHr7k": "End Time Msg", "Kwn53CWjiBzlFcnJJJGx0": "End time can not be earlier than start time.", "eFcy-BfOhf-h5PVaJ3_zt": "Data Loading...", "eZz4PP9ckrYa3h1cjvihA": "No Data", "sOTuSCBfF-JISyxB_Hstu": "History Playback", "D-3CnkxFwEGtcITqH7fbB": "Mileage", "HGjTtiFHYcRbFl8yOYVox": "LBS not included in Statistics", "d8OJkUti5rpb0ASvasdB-": "View Address", "c2VXLffkDAgW_W7kge10R": "Parking", "XbOlArDdJLP3YlKnbKEQ0": "Stop Time", "cr0p2Sw1rzg2KIpg4kmxH": "Quick Check", "FgXEzLIfSgW9hXdZU2BkT": "The upload content is formatted incorrectly", "n14RoDOGBsNntIwetoWMq": "Click to view video", "rMpzwjT1wGJzwTtW99xdT": "Print", "8Hy4f3sEGqYcZA0E2Tgwm": "Export", "Oy0sGBs_ICAiweVP0kuh8": "Time interval less than 31 days!", "4CputoWSHyKi0tf0k_mKf": "You can only query the data for the past six months, please select again！", "T9djoLP0IBjXNQVB1szpe": "Reduction", "fzpQzKWHHxUzOvguAlLA0": "Save as image", "8zbVKtrFrdYmnIr6smhz5": "On", "REZwRP_j2rmLc3pFRLqvS": "Off、close", "H4NX8-4EhG5zHLk1ytXkp": "Fuel", "hs24f25MR-Kkr7mTiNgrI": "Temperature", "n_gGRUBDnHrrFFZNMBWQF": "Voltage", "faw91fOu9AVHonQmNZS1R": "Share name", "8jMVMcD_Ztb85usaRh0ky": "Tomorrow", "coUL03ioWIVJ-B5LWsN6J": "3 Days", "7z_8O5fJQFxZ3qGxVSaD3": "Create Link", "_qETsEONk7wNBovgka8my": "Duration", "XsXoMT3CDfSMMxm_H8UGU": "Today Mileage", "K-2ZfNUf8dV65yB98WRvE": "<PERSON><PERSON>", "bkEROx2ZY0mMX1e5F9fbm": "Temporarily unable to get address information", "7mowPMr4CB0A0uIRO4d8V": "{0} Devices were modified successfully and {1} Devices were modified unsuccessfully for the following reasons.", "cUE4imnis7N3pb9QDkywH": "Delete failed", "Yo_d2neO6liIX0B6ZtnX5": "Is {0} set to {1}?", "MCvwqkGr2-dQqS1DK3XwR": "Set interval time range {0}-{1}, unit ({2})", "7GDN-cGPKDVJF3Q-qFCvd": "Batch Modify", "2JODYAuyHeVxEZXdVhZAt": "Activity Time", "KS-VF_Tm3pzzJDisCGNKE": "Newly added time", "Ipkl7-AfN7m1qHER0Tm7f": "Devices Quantity", "_QtO05JtDYmcbYWbiYs6y": "The device cannot be restored after being deleted. Is it deleted?", "Bv_g9A7SENymIKsLJ9ujV": "administrator", "itTlS_5B7wWxVWLZ2IZjG": "Distributor", "2biNeUu6eL5bY-ZYKcvi8": "Car User", "BLo9bZGWQ0hpd1oDuL3B2": "Renewal Link", "CguDuYAHEf6SmLcLCCDLD": "Accept Subordinate Alert", "1teP4Jl_EJz1v5UdgQZAF": "Notification", "J9_G307ZKQDjVoHtO7Qss": "Customize", "AYVWzUbEzW8nQXqBDTyC9": "Account/Name", "kGWxb26eIdnX_GrUdYd50": "Transfer Successfully", "1F7LiURKsTqo36ZrZkafp": "Please contact your service provider for renewal!", "YQKLFb8qkw8_3Cu2O2IZ_": "Transfer Customer", "9grU7SGwJ1bWmFgjQvuDS": "{0} is a end user and does not support transferring customers. If you have any questions, please contact your service provider", "IG5Mrae_PTMgmeWAW6kX-": "Command issued, waiting for device response", "commandSuccess": "Command delivery successful", "9L9roCS8UNQRrmlWr1zHU": "Congratulations! The <PERSON><PERSON> Executed The Command Successfully!", "lRyXdbGCaG68kEvHRl2wf": "Result：", "AMWltiKoAEZG_1FlswCOk": "Offline command have been saved, it will be automatically sent to device after the device is online", "hCxLHY28pr4VAUX59yaX3": "Transfer to", "BYPkXF0WNPoQnCGOTnh8A": "Working Mode", "6kV2sMrY3lJRwThT_NSLQ": "Interval\n ", "jmaCB00OAHprnw3-I3hUm": "Set Dismantle Alert", "fAHRmeV6DRHlr6Pd_0dKv": "Center number management", "UJhfS6Uu9OWK6sW6ASkHz": "SOS number management", "-DQYU_0OifhlsFR-Adxv2": "Reset the device", "yUWTv_VHIxw_swMm0qKyv": "Monitoring number management", "DPftk33RF1Cr-9EzPUpV-": "Listen Callback", "YJu4l4OgiSPD6yMd0UctL": "Alert number management", "iSHNRyf7vh0Vtuop0muUV": "Vibration Alert", "gXSOO777Zyjx828Cmep3Y": "Vehicle power supply", "KuJyn-XHuYQq5O7sOtHoa": "<PERSON><PERSON><PERSON>", "pnfK4Vyg6n2wiCxC2iZEz": "Vehicle Alert", "ucbv19YmxM9BgaLpFQAdi": "Car Searching Mode", "6zwffX5jH1JpFu917DGAz": "Engine Control", "OuaI6fAFAWCRsLdLAG_Jf": "Restore original", "BVLbawNBmWRSi4svvOQ_m": "Smart Tracking", "KRilYPhoZO1sT8aKsGzEB": "Parking <PERSON><PERSON>s", "IVhyLA_3ICW8aDDQr-v3v": "Time Zone Setting", "EwQMEv-4HfvN0bXg4Seja": "Remote Boot on", "t2xOtwpmtTfU6sc4426SW": "Fortification", "FbNlJKmw7P2Um_Vyd3sfR": "Disarming", "m5sOa6yTHvNgFp7m82xK6": "Telephone Listening", "xn499WPrR7JCXsWgImCmt": "Sensitivity setting of vibration alert", "KQxWomzE-Y7a5sl5e4ZK8": "Vibration Alert SMS Settings", "JFwhNoaUQY8ROls29jwhx": "Vibration Alert <PERSON>tings", "7BUMCa9c_IeOBWspNS9TQ": "Sleep command", "ND5cf9nzQ6aQT96OhgZIC": "Displacement radius", "Lt1mMvTHa_yuzv81mJ9Yz": "Temperature alert", "lBUwL931fZBbruo7oJk1w": "Platform is about to expire", "A9FNfVrnNX_IL3Sqwj9Su": "User is about to expire", "2fOpju8dVPzTsLD9Icbaq": "User expired", "COB-45zsnvgfvOuofxPf5": "Platform expiration time", "7_McUY6-mnqlIwzq_o7c6": "User expiration time", "x6j0xlfBVYCgf3-pXoh7Z": "Expired Query", "XbFegIO6XdtwVCLj3tHjn": "Start Date", "k3rb7GIYeArL-6QUB2jYR": "End Date", "JOHRDjZ4DXNl0KtpQTn5Y": "Time Query", "r4dYsKXhyAkW9-WKvNGd0": "A total of {0} matching records were found", "V7gpSV-XtOtpWE978XWMe": "SOS Alert", "KSTTK7q9bKSNc36SGzqL2": "Alert method", "GHkNMYw76nVM5W4mRICca": "<PERSON><PERSON>", "YWO2tIL3JS9UWLacM-ECj": "Fuel Setting", "xwcKgTp2DvPPU3BRpr4kw": "Owner", "error10003": "password error", "error90010": "\nThe device is offline,customized command failed!", "error70003": "The remote control value cannot be empty", "error70006": "Does not support or does not have the authority to issue this command", "error20001": "Vehicle ID cannot be empty", "error20012": "The vehicle is not activated", "error10012": "Old password Error", "error10017": "Delete failed, please delete sub user!", "error10023": "Delete failed, the user has device", "error20008": "Add failed, IMEI already exists", "error20006": "Please enter a IMEI of length 15", "error10019": "Wrong format of contact phone", "error10024": "Do not repeat sales", "error120003": "Share links disabled", "error10025": "The modified device information cannot be empty", "error2010": "Upload File", "error20002": "No IMEI number exists", "error10081": "Not enough renewal cards", "error10082": "No need to recharge for lifelong device", "error3000": "The role has been assigned to the system account and cannot be deleted", "error103": "The account has been suspended, please contact your provider", "error124": "Unable to operate at this account", "unArrowServiceTip": "Platform due is less than User due, Pleast select again, IMEI as Below:", "editDeviceTips": "Please confirm the device to be modified is the same model and is not active!", "uWFemTPKIbGOyQ17ugfkE": "Last Position", "tempSetting": "Temperature setting", "tempSensor": "Temperature Sensor", "tempAlert": "After the temperature sensor is turned off, it will not receive temperature data!", "m7PB-o0n69UQe3-RLFtm6": "Remote shutdown", "d2v88dFMBwc6IBasbVZUB": "Item Name", "laXMcUYMWZ5Z1GJYdq5_g": "Reminder time", "8E_5DReq4p9vShgeUzwDv": "No Access", "-9EQ9kbkuRXcOGCWQ6cMq": "Maintenance Date", "zdVunWKu5AhodKvqgP4rI": "Update Time", "0SbtD71xRYUoRVQf2nVLg": "Effective Deadline", "5WAnTiOO5Wp3w-x0SVjSN": "Invalid", "_soRxqN9L0mb6hSyXV6Vx": "Take Effect", "npm5kq2r28GlD7S4lIe14": "Creation Date", "lWW57n7Ua158IiiFvL0jU": "History playback start time", "jZ9YZbQgfeKLiDHeOQF4Y": "History playback end time", "wHLiHQweFr6Dg4PvyCpT4": "Link validity Period", "BnOTpdevlmr1HZw_IH-KH": "Share link is not enabled", "g0lDgo0eVJuPrPB5Z5Vgd": "Next Maintenance Mileage（KM）", "Rj0tzi6vp9nWZtswv3ipq": "Next Maintenance Date", "nwJzmS9Osv1MtnLuWwzIZ": "Current Mileage(KM)", "r9QdHdk_7rac9hv4aw9cl": "Copy", "9fBvgXknsLqW_Sr8WqrIj": "Add", "3QqqxkqFx7OzOMr7KoS0F": "Current Mileage", "7fSVAcN6FNA6tRDv-_R-D": "Last Maintenance Mileage", "kpJpvXpu9gAqYd8cN-Tcr": "Next Maintenance Mileages", "dsC8Y9WsQautq4sdheMJf": "Last maintenance date", "nqL_foYkFFB3XXif3unHh": "Notification On", "TogUVo2D5tJSTDaOr-n1p": "The next maintenance mileage cannot be empty", "fidPvoq7zsvnxAuSg5NGM": "The next maintenance time cannot be empty", "bxeltfqSrYbvgH34URuDu": "The next maintenance time must be greater than the current time", "bsQ5K2AQ9sCuBtOMxmqf_": "Renewal Card", "YodpqD_6tNSUO81daApII": "Please select the device to be modified first!", "pB2SeJU_kVoVlVfQFD-sq": "Is this order issued?", "5xdEeVm0qnz9SxT7MG8qC": "Platform expired", "Py6WRg65Hlhr4ZZeN4JTK": "7 Days", "9hrkM5Xiik9vRyY2TjZC7": "30 Days", "DwPR-SBjnWtgyoWDDpzr3": "60 Days", "wcGDlaCZO2gApNapnD1GE": "{0} Days", "zc7x8FJOhr0K527ImZQy8": "If there is a conflict between the platform temperature alert setting and the command temperature alert setting, the platform setting shall prevail", "mB7Q6tNij2-TEQuj_EQip": "Low Temperature Alert", "ngQ_u9YCVROkTgspYaeDF": "Alert Value: Temperature", "W5vK5LEvgAc_JHhPcxySK": "High Temperature Alert", "YJK4tJOghE3DJ5hCCblYl": "Example description: If the temperature is >=20°C, the alert will be triggered when the temperature reaches 20°C to 127°C;", "fpq5Sc6vhKfXv3uT3-_v0": "Mileage Maintenance", "f7IWU1fmCaxD0JjYJZoAR": "Reminder mechanism: {0} before the mileage reaches the set value / 3 days before the maintenance date, a reminder notification;", "PafeQj-F3Eo7YM2eQupv1": "Support mutiple emails,separated by semicolons", "Ce2R23rARaUED1OI3Xch5": "If ticked, email notification + station notification, email can be changed in Alert <PERSON>", "m7syXSguP29TzdRR-jtqL": "Please select a CSV file", "Nt1bQaoSwAA1lWy-Yki9-": "Select The File", "wjjTfBWo4sFD6dDqMUA8E": "Import instructions: 1. Save the excel file as csv format 2. Import the csv file into the system.", "JoV7M-08iOvdSofJL2dXv": "Modify successfully", "En636CNcxTxoDjeZPjArx": "Device Model (after modification)", "C2cZ15pSLdgYB6PdIEcHT": "Command History", "oQjvZaVtcYehcHekft4JC": "Terminal Not Support", "dHJWVOHGgZNJMVJctlIgr": "The terminal responds successfully", "3ouZgJBDf6-vt--s9KqFJ": "Terminal response failed", "cVVOCbNxQLFcv1HwysKUM": "Not sent", "qtX4hKNhrIq4HQWKq76qF": "Invalid", "VC9bpkEpdi5BGS4bxu-Ve": "<PERSON><PERSON>", "KV0PCwFh9KzMeYVPkXd9i": "Execute Success", "7055N1pThT9t2nVidaqYf": "Execution Failed", "qMnlim32WjTPSVJezsiZS": "No Answer", "DzuqoOisOoNLlaCC-Ji_y": "Sale Date", "VZEMI1HZaAQEAOccCxLgK": "Online Time", "qBNdY9Hof9Y2O9F204y0I": "Mileage Maintenance Record", "2_GOBlwzPbyr5HQDEsUOK": "Tracking", "CgoQEhz77jlNp0vrlVxSn": "User due cannot be greater than platform due", "4HbPhU8SVH27GO7vwb5Az": "Alert <PERSON>", "F4gONEExeouoNaqxfRgUl": "Whole Day", "YxtnLS-9penDcV00ukAce": "Day", "1PMQ9WuBMTKpUj5pxY9s5": "Night", "M3BBgNFBGii6WiaU5jQsY": "Alert Notification", "gVQwdq1hZqq52wB7i1u1n": "Receive lower-level alerts", "M-aGJuBti_0J8Qpbvmqzf": "Station information", "mpeSz021INSNFgOa8BCpf": "Alert Mail", "1ECHLtFguDsDq_3uOG0yp": "Maintenance record: After setting the vehicle maintenance reminder in My Customer-Equipment-Details-<PERSON><PERSON>s, check the maintenance setting record here, or add equipment maintenance settings through this page.", "RE62y37yvH6qImoM4ss42": "Sharing record: After sharing the device track on the vehicle monitoring page, you can view the sharing record here, or create a sharing link there.", "_WlC2Ljs_RF5cLhyYJnSi": "New Share Link", "1sW-OzlYc_q1b9n_NcFpi": "More Instructions", "vxvA4VAvFjv9MTiMd5EZB": "Query command", "enQR7BvU-FbOHOhV869wf": "Query software version", "ZjesfJleICyF8c_POTRz8": "Check Status", "GTC0khuQYbxhjkOcU-f6G": "Query latitude and longitude", "BbRsM3ZxO3aXYhzCH6iSU": "Query parameter configuration", "lLcqsvnFC-b1pvBbyIudG": "Check GPRS Param", "9B6fVyRW86BNLPYKEXqaz": "Roll call", "6vK3EWj5Tu8CeExT-hBsJ": "Query SMS reminder alert", "qbSvjPXqpmObBi1yVrlzi": "Query binding number", "vCi97K5uNUBnZV697Gpjo": "Command name", "j34MkEOAqjyT80854yoTQ": "Command content", "s6lAnu683jaqbQ7tCaJ2S": "Send time", "8Ch-s1H4v0OW2yx82azFE": "Reply Content", "5be1cXISzVRWtU12xrHrV": "Response Time", "hVknJbW_i4WBVBCdHbYZz": "Send Command", "F3JFF-1PIyXW-jnh4bsMd": "Center number", "ZvLlyWA7PnfWeaWbJ9UQS": "Anti-demolition Alert", "2izr0ZUzt8xrZhKYzDZh5": "Platform Alert", "c70mJqcGxaOeuqV5V9Z7c": "SMS+Platform Alert", "wVmqyjZhzO8ddRA4vDwhi": "Platform Alert+SMS+Telephone", "DyrTMwE_4K5Uk2TKYFLIh": "Alert value", "M2L1RdEwKiK8xe739Zd6i": "Buffer value", "K3pwVRJqNEWbwF0S786O8": "Time interval range 0-255, 0 means to close", "jQC4bDCPKXPp4Lff_4hG6": "Platform Alert+Tel", "eKj89wVWw918dADwmoY6a": "Phone No.", "qLAbckbjkA2Jh7bjocw1V": "Implement Brakes", "u4rNU4FweIl9vmKO8n7Bo": "Are you sure you want to check the binding number ？", "3Z1ZggbT0y84ej1_bi1hx": "Are you sure you want to check the GPRS parameters?", "nRNHg0_EYHP_Wc4PiR2ib": "Are you sure you want to check the latitude and longitude information?", "K9LhupOLQu06xBR1c8CY7": "Are you sure you want to check the parameters？", "z7H1qYX3aJE35pbQU1Mx3": "Are you sure you want to check SMS reminder alert?", "HTDQgJ7b2SoT9Rw7CdhQU": "Are you sure you want to check the status?", "Di-hmtqF6siUZJy2QH6DA": "Are you sure you want to check the version number?", "ktHad5imbbn2sPz06Utbf": "Are you sure you want to restart this device?", "zU8zLd7T1sJBQIv0bzTu5": "Disconnect", "dRqd3njVatWzZR2v-Dejt": "ON", "ZRPZ-M8Mk6NjGlbu6F7uk": "Parking time", "7-bUdwYB1cZvFilxmF6WL": "Turn on temperature alert", "AQ97fUpbsZxJQnd6NikCY": "Alert temperature value", "DX4-lrE6fLMHp5wOxwvMr": "Western time zone", "7bV8hfRhGldGWdo8Vb1il": "Eastern time zone", "y6VrMMGH4Mh4mO7HsHmBj": "Start time", "BOvye1w4YI1Hbo_9heqdM": "Resting time", "Uu6qeV2XLpiwboneINLak": "Interval format error", "qeJLq1tfQF-5G4t003P0c": "Setting", "fQWV5H2FZT3uLQ0sLLltN": "Please select sensitivity level", "nP1vM9sfXoEhoHMXN3-j7": "Scope Setting", "ePlqFNBNJSFjQI6j-eSUi": "Vibration Level", "mCtr-rCplu_IYmqN77Cw9": "Platform", "SLQRfUeAU4Dvy5O1WNHT0": "Platform+Message", "tWFKyzzfR_wlbUtuvEIGj": "Platform+Message+Phone", "tWFKyzasdfsdfsdfsdfdf": "Platform+Phone", "Qjag3m9P6bSba14_Zw745": "Vibration mode", "Bs2brZAj-iy3iAW34SB3D": "Way of reminding", "NXoB1WqRAzDcN0HJsa0aw": "Return interval", "tziOOsAmI3dacmVYXP1qI": "Alert wake-up time", "0iPIesWJahFY1C6WIW2Sr": "Awaken Time", "0tZ4moV7mgZFjX4qqmNw5": "Exercise Interval", "gbhcMXdMMOyETBXIHgDZ-": "Static Interval", "j5J2lzHmNEClnupw7vgF8": "In this mode, clocking in at work will turn on GPS positioning, and clocking in after get off work will turn off GPS positioning;", "Es2otsyREhsk7D6vv3TBu": "Working Day", "PdWLpHAYftr8mUT61cQVk": "Saturday", "om5IfTKXo7Qi6fz986E6c": "Sunday", "CHuzsvL1dgA0SVIzXsUON": "In this mode, only enable GPS positioning within the set date and time range;", "KeBSto-LPbBMKb7eta5NT": "Timed return mode", "mtCLx2EVthWETG3lLl1k8": "Alert wake-up mode", "Nfck51t_8ikXK6fKDt2g5": "Weekly positioning mode", "q7c1wxDQihrNW-5hKBXwK": "Real-time tracking mode (more power consumption)", "giACddDYdcUy9-wkgaotd": "Interval tracking mode (more power saving)", "nbB7jDHhjihV246qgHGii": "Real-time positioning mode", "9YwwBZ2KTfGNGEt_PjDj7": "Single positioning mode", "FbU62YUMNlB98e4figT9t": "Normal tracking mode (power saving)", "IyWI4ui9nSVwNx6hh5l3Q": "Single wake-up mode", "6wNw-7dzRHALobiqhwNHX": "Time tracking mode (more power saving)", "1ubUpBg3y15OGlxqErTwT": "Timing tracking mode (very power saving)", "kppRYbv1HsYqNSpzmnz2_": "Weekly tracking mode (power-saving)", "lYy-c9I-wddGRhq3__n-o": "Single tracking mode", "_pLfdO5dD6EIHGzn3EccU": "Intelligent sleep mode", "a1n_G5EuFGYkPvjxlUwuQ": "Time period positioning mode", "ZumqxPha0ETt4s6IWj_pM": "Check-in mode", "bFR6ganf7Gh2ntOnWJZKM": "Time period mode", "o6e4_Chy0zEchuvkyrCSC": "Monitor", "XoaIcv8RDjIKhJCCe-Bgg": "Modify Failed", "a-Eywl004h8c0Z8yaNvPq": "Normal tracking mode", "351stQZgFO1epbSXb6-g5": "Please input interval time!", "CVeOX4a2fE17TSJnYHWF_": "Please enter the correct time interval!", "PxTrEplTrqAsSWEaCaA5L": "Please choose time!", "OFZLob4ixsZGAbEMkCeGB": "Please select the next maintenance date", "kB5kAAr1kMy-xk7poKZjB": "Please enter the next maintenance mileage", "_YGwsF4tX9xp3j5lGqgDk": "The next maintenance mileage must be greater than the last maintenance mileage", "kOzwGA87dUbTPO2J8VAAE": "The next maintenance mileage must be greater than the total mileage maintenance", "5xdEeVm0qnz9SxT7MG8qw": "Expiring", "5xdEeVm0qnz9SxT7MGrnq": "Expired", "rPn2PAqmNooNekpD8vHZ_": "Sharing Link Expired", "cA4mkzbvSQKVnKohu-NBc": "Link error", "7ddIPjWcWGS0crmj1wera": "Overspeed Threshold", "7ddIPjWcWGS0crmj1tuff": "Tick at most {0} items", "7ddIPjWcWGS0crmj1dwcW": "Tip: After the command is executed successfully, the terminal will automatically dial the set number", "aHz9YjNwCHLIwsfvnU8-X": "Normal users cannot create subordinates", "SYFx7Jw39HypqfmpAoSMi": "Maintenance records already exist", "ubZxA7UWGmQd_Gn_FpuUS": "\nCommand setting", "9OMax5dpvzrcm_Krk5Qc1": "ACC On", "L1vd4W1ECVlMaIZ_IDKvr": "ACC Off", "rEY8A9V_1uxYDZCM5MwZq": "Device not activated", "kB5kAAr1kMy-xk-flusio": "Main Fuel Tank", "_YGwsF4tX9xp3j-flusio": "Max", "kOzwGA87dUbTPO-flusio": "Minimum", "5xdEeVm0qnz9Sx-flusio": "Full Tank", "5xdEeVm0qerterthlusio": "Fuel Quantity Alert Value", "rPn2PAqmNooNek-flusio": "Standard Setting", "cA4mkzbvSQKVnK-flusio": "Fuel Tank Shape", "qwersfasdfegergewvdfd": "Auxiliary Tank", "retrrtdxsgerhdfgshsi2": "Maximum Full Box", "dfgdfthtrrhfddfglusi3": "Maximum Empty Box", "aHz9YjNwCHLIws-flusio": "Standard", "SYFx7Jw39Hypqf-flusio": "Oval", "ubZxA7UWGmQd_G-flusio": "Circle", "9OMaeerfdgswertehhrtf": "Advanced Time Query", "9OMax5dpvzrcm_-flusio": "Irregular", "-mwXOBzafJJzS5ECa96HK": "A total of {0} records", "e83QYflrstV3mHtOKQUA8": "Modify", "2wrWc6_xwJGFFzHae1FBm": "Recording", "8_IICEycxVRCZ9oVUc4XO": "IMEI Number can change 1 time for the lifetim card.", "NORhEcC5v7cp-BdxgZl5u": "New IMEI", "wYeSVrHLh5AYblQHEkeLW": "Please enter a new IMEI", "yzdVhW5qve_BhwkYrJnC2": "Please select the modified model", "913JlvNitsiZ1R7_ypSye": "Start Date", "znQ4cCuYAjv_vrmmN8SV7": "End Date", "xSIfVXjSrVDgcrpNE2Y5q": "Prior To", "hwx9hX29-Q8iAi0Baajhn": "After That", "6g6wxHvMzcyEZqSUFYY97": "Old IMEI", "9Z5P7gAyBPa7F9zacoYIW": "Operator", "5oCJuHcl4bkZHJoS78gHM": "Please enter a valid IMEI that exists in the system, please re-enter!", "U-u7gdzttM7kMFNe73MuA": "Please enter the lifetime IMEI!", "0rcBbUV3Iju7g4de37PhZ": "The IMEI has been replaced, please re-enter!", "8OxUTZHtyqXfbhaGnWguO": "Lifetime equipment cannot be replaced with lifelong equipment!", "dN8YQHV0UmJ0biAdQ5eUs": "The new IMEI can only be replaced after selecting the model and importing it!", "aJqS-MRCOEzT3vAoobwyA": "Are you sure to replace {0} with {1}? Click ok", "safweerhgrsgsdgerwetd": "Please select a time range", "rethrtyjsdfsdfgerhrtj": "Please select query type", "5M1oL9E7Oa4VYJrSBCMI9": "temperature >= 20°C, then an alert is triggered when the temperature reaches 20°C", "21C7S-yVPUtM_umDE9-D3": "Supports input of 10 to 3600 seconds only", "8EZPAmUvEcgIQlu9Af7lO": "Supports input of 180 to 86400 seconds only", "0Yj_478WQcnoLCTAqhX28": "The device re-reports a new location information", "5VyshM83XNAdnIDpGFwQe": "Whether SMS reminder is turned on", "sL8DUa562p1PB4TCqLQN0": "Display the bound number", "bFypuArP-qFt5NBFfwsRw": "Cancel the defense state", "HJMoeiuNxOViTffIvIAGI": "Defence after being stationary for more than the set time, vibration alert can be triggered after fortification.", "eosV4vPkvxGINYFnMqTOc": "The parameters set of device include device IMEI, upload interval, SOS number, center number, time zone, GPRS switch, IP domain name and port number, etc.", "XoYqBt9yTe2Cumcn88i3-": "The current status of device, including GPRS connection status, whether external power is connected, voltage value, GSM signal strength, GPS status, etc.", "8pCLreWQJI7SM-dCnGDqa": "The specific longitude and latitude data of the current device location", "qDp8wbELHWHCtXR4kIKLr": "Current device program version", "dnRal8YUBaR4lwcct5KZh": "GPRS status, number of satellites used, satellite signal level, etc.", "Aiy8_YOvXwvR1y6NH8vzH": "Set the timing return mode, the week mode and alert mode are not effective.", "emPLIpp281tgE1Y9-SfqX": "Enter 0 for the time interval, which means to turn off the timing return mode.", "qPtdLQjjVdR-hBSU9vYhO": "Set the week mode, it will automatically send off the timing return mode first, and then send the week mode.", "0fmV32e9MeQyY5lqIXLf6": "Set the alert mode, it will be automatically turn off the timing return mode, turn off the week mode, and then send the alert mode.", "9kMue__kTvle19S0-jw98": "Incorrect email format", "d9YwxF2OSgivkz7M8H5Bc": "The device information is being returned and can be viewed in the command record later.", "asfwefsdfsfsdfsdfgrer": "Successful renewal", "ULolU9NS8lnR1QkO3rSsm": "Modified results", "3j-R_aY3-zIKPRKka1zBg": "This email address is tied to another account", "bzVAxsJEtQtawIDU5RBt1": " please enter", "6nl7HBlbHrOtUvi-N0OPM": "Message", "b0rVRhAL29g8HAgKi6cD6": "Upcoming Events", "4FFZ6E8mwSsIR99YCODZG": "Hello", "EG1qollS3y8DsffUEQ11i": "You have {0} devices that are about to expire. To avoid device deactivation, please renew in time!", "Y33r2M_1dN1ut_Bp2XdSU": "Warm reminder: Ordinary users need to contact the service provider to renew the device, and dealer users can use the renewal point to renew.", "H9Nda-k7XDEYdYb0Oxd01": "About to expire", "JgHgaEMi5JzduvJ65HHG8": "Back", "WKkPZV30ap53zWCZyuwuD": "You have {0} devices that are about to expire, please renew in time!", "Ux12pE1OeZhyzEB5Svd4O": "Current", "GWh39-lGjyVpcylYO7u3j": "A total of {0} records", "YoxsJOzz0McmdP3PDAcTb": "Expiry Time", "6L0b2U18GaqDt7DpA_yiy": "Content", "Hi_cEuIxsZ92-zskwssT7": "Target users", "zUiSwYI9z29DqQ37Yt4om": "Please enter user name/account", "6VlwgEKwHw_dv9rtupqSQ": "Reply {0}", "htnsA0a3Ra2_fYJ3r-5x6": "Mileage maintenance", "13b7lm7aQ9H9sHFfoIXPB": "Current Mileage", "c0nJYvCKZYWjJzvr-X9GM": "Last Maintenance Time", "TNGaQ-bXQxYyhgHYvOsPC": "Last Maintenance Mileages", "hjO1Vg319X18G9FnlJEPY": "Preset maintenance time", "KsoQwWQ7fu-RaTUnWvTl1": "Preset maintenance mileage", "s5SpmyxQe30oLsV-H-g88": "{0} Maintenance Reminder", "DKIGDk2OpzWx0lfNjGC9h": "Successfully renewed {0} devices", "iVazSwP-tbFP5TcNnyS7q": "Custom Reminders", "QDYMtaj3f4DzIQ0ZGMOPu": "Feedback reponse", "yJvXIZPZa3JFBQNZpzKNb": "Are you sure to delete?", "e3XxVhLcsTxKYpgRDLYn4": "Before renewal", "5IzURNFLb869S2Iil0zBK": "After renewal", "RaCuDe8vPz5OURmKQNTMm": "You successfully renewed {1} devices on {0}, details as below", "7Qr_N6o7eIW6DnbI-fmC0": "Address", "LxQFx33DbLbDCFMGVbm9r": "Alert device", "s5dkA82oFKqgptUbqsic4": "Publish", "6X_prYTdd-NdxYrTzYiTv": "Edit Announcement", "S3hMIOyR7bzPA6vDcwblT": "Update announcement", "ZgqbT6PS21BDxcwIKFkO2": "Language", "O0aHYTeJjn0FH79_1eXvf": "Creation Date", "CW7ycrIhRhBNT3JiCPuvR": "Customer Name", "DNheNhg94oWEH8ZCjFPxE": "Unbind successfully！", "mlTAaKUQGc5csKwzYGmsU": "Announcement Details", "vmaYyrRFsLx1fdLisRQg5": "Event has expired", "mIJPpkv893lCNJ6q9lO7P": "Customized", "GancrVwpSQ_JP9HG1XU8N": "About \"{0}\" is being processed", "yW2jL_VVgNCa1ZK_CVOme": "Regarding \"{0}\" has been processed, thanks for the feedback", "mPnTB8w6f32SrMN4DMi6G": "Regarding \"{0}\" has been processed, custom reply", "dashloadi3JFBQNZpzKNb": "Expire Soon", "dashloadiTxKYpgRDLYn4": "Status statistics", "dashloadi869S2Iil0zBK": "Online Statistics", "dashloadiz5OURmKQNTMm": "Total Activation", "dashloadiIW6DnbI-fmC0": "Week", "dashloadsdfghhdfgdfgd": "Month", "dashloadiLbDCFMGVbm9r": "Comparation", "dashloadiKqgptUbqsic4": "Version Announcement", "dashloadi-NdxYrTzYiTv": "All", "dashloadibzPA6vDcwblT": "Normal", "dashloadi1BDxcwIKFkO2": "Abnormal", "dashloadin0FH79_1eXvf": "Unread Message", "dashloadi9GePO6A6jBjr": "User Expiration (Optional)", "dashloadihBNT3JiCPuvR": "Remarks (Optional)", "dashloadioWEH8ZCjFPxE": "{0}Version Update！", "dashloadic5csKwzYGmsU": "Service statistics of this account (including subordinate) is less than or equal to 30 days", "dashloadiLx1fdLisRQg5": "Service statistics of this account (including subordinate) the platform service is expired", "dashloadi3lCNJ6q9lO7P": "IMEI Number/Login Account /Customer Name", "toIP_sB0-7C-n32qKpzMk": "Update successfully！", "t-pf5RwPxV_OcNP1WidEM": "Copy Share Link Success", "Jj2KZHdeUO7O0GAA9qykl": "Failed to copy link", "rB3aU56xAS9EVj5XVZ5Yu": "After the remote start command is executed successfully, please reset the device working mode", "pPGMtc0DrQn1jiEFpVe7Y": "Expiry date", "Nln5aIwzw0Bqh386-rPkg": "LOGIN", "PBm_ngQE-uvzxq3XBwYOs": "Global Tracking System", "4PUHYIKijDJgBGwb_X6oX": "Support jpg png format picture and the size doesn’t exceed 1MB", "OMFetLPAXXivTkvAOSIff": "Head Portrait", "Q30JLZF3GiZdfWq1MaCR4": "Basic Information", "6rP2gYwNfQwClJBEmxBEy": "Phone Number", "9sK_TGewQl5zfDn75RpIq": "Address", "KP0yfMnOJWx1FBWS-opEk": "Service Provider", "kA-s0kUkwL45l6iYv-TKh": "Contact", "IokO2UlgeBdvUDTLyX6ln": "Change the head portrait successfully", "YPoyixx0lWnv9IoARFDls": "Save Successfully", "vMngAC40PvIZ-niUfbb8U": "\n New Password", "mxMq8Gu5X1vUWmqndRe7g": "Finished", "MslCKqFFYRY2OyL-Zo_Dk": "Service Provider Reset Password", "0-v98dDMuY2bdr08YIehg": "Privacy Policy", "jD1yQ2o2qqCboJHfrm7uS": "Terms of service", "nmDH8KjrgNcL1-NXZoDqu": "Open API", "Cyllha7ZgGJqvuwHsEqaD": "Account/IMEI", "prX3JUTouWcIjV3_duH37": "\nPlease enter the verification code", "ZcMHgev4PYIoGliHjPmtn": "Verification code", "NC9hUZ2SjihaL3Jguqap0": "Change Another One", "SP0RYsgGSCmrPHOn1LXCF": "Next step", "BbZrXXe5mpSu5LWgE0htM": "Account can not be empty", "HV2CNVNoo9AxRsVyBNYPM": "The verification code cannot be empty", "bgEEvDhpqJetROOyhaAmD": "The account does not exist", "caceMSPMiJj6-8Gnn-Mj5": "Verification Code Error", "q6NfOlwZotvZUoO9xT_Js": "Verification Code has expired", "-mkJJAlBTMfaCMMD_cUVA": "Return To Login Page", "-1NoOoI1-XfLzqLSBP2cC": "Unable to contact service provider?", "UrmoTz-bEoYVUZudlGkmj": "Change retrieval method", "eDBwJLmqbZI_jLHPeNwLR": "Confirm New Password", "9o7fPzhzvlOW_AjZTYvQA": "Please confirm the password", "OV_u3cmw3_nqGdgXQhMq1": "Email can not be used?", "OWU3uV1ucUTxye3fWEOYn": "New password can not be empty", "CS1umvWqPvy5fyx5vpLev": "Password length can not be less than 6 digits", "VW5d-hqpDfpW1wxKz7GZB": "Confirm new password can not be empty", "7aDPEpfMB28Q6UpQ3KiPe": "Passwords are inconsistent, please re-enter", "AuAJ8pz6FdyRSEDF79UtG": "Get Verification Code", "rNhMvrlEMqTqvgVETayyC": "Re-acquire after {0} seconds", "2lSdlLHY13XWUtSy6zJ1R": "Verification code has expired", "3CyUohrSLLacv5So4dPGX": "Password can only be a combination of number, letters, symbols", "EYoz5FqDoi_qDRmAsXOAW": "Retrieve the password successfully", "Lv3v6w6vj3VIrTLiLi_uz": "Email Verification", "rVQn6WUbLJreV967ZLfdy": "You need to retrieve it by email verification code", "zb1y7CpuMLs3EACUW8OnM": "You need to contact the service provider to reset password", "Oo_W9j9D9ILdAR5k-vcW6": "The account is not bound to the email and cannot be authenticated", "CRyJ__yaI5l27i-XQkH-C": "All Rights Reserved", "Atr_RssdPBb1BwK-5pdcT": "Please Tick The Message First!", "CGfq1mj1IRuij49lT_V9r": "You have {0} devices that have expired, To avoid device deactivation, please renew in time!", "zfAaSueFmo1M44F10jQfR": "Export Users", "SrGP45dYF4TfIMabJ8GI0": "yes", "PSJif2BD7ijRnlbhUCzbL": "no", "Kclu7hitguzQnIENer23F": "Includes Inactive Devices", "nlTS_5a2cN5XVp4KtxjsC": "Commonly Used Fields", "KcBBwSU5GF3Vi-dyE_STX": "Select all", "d3qRaIFxfxVC8m9k5uUIK": " Device Model", "EVbsxypelcsn0TWWQ86fY": "SIM Card Status", "9HmB7gxHH1KowJ2BRUtQz": "Platform Expiration Time", "b-ZK14jqMcQs1RkyRsi6T": "User Expiration Time", "gwHls4t8DcXwChxc0iTMw": "Devices Remarks", "kPLQ9bi6YQ7CGlfsj1-Xr": "Group Name", "FxPry5C52XBPmrYIZYyL-": "Battery Information", "gBEw5BIt6PcPC9zFkw1pu": "Voltage Information", "0RE8zR_4ssG3-vDm1dDFS": "Membership", "G0h5Phv0L2ZYiNkW6qjOD": "Extension Field", "7hRo37AgY5tlz6R_c8A8E": "Keep State Time", "QGdPkhCM7YopsUXXGEC-s": "Last Signal Time", "cnyEHrN3GNiGP2f0vJWse": "Last Positioning Time", "G3TxUVRv5FTSlKpEn1Ql6": "Field Configuration", "0Aqxb_GDZq_HUfv9X1rx-": "Task List", "rDoa54vkvUnzRjC8KfzbD": "Task Number", "u5VGmAhjxmg1wptxMpiCG": "Includes", "rt7d3vYgqgqlKgp8G0ZFl": "Does Not Include", "EjLZvKQnhIOeBl97DUMEI": "Export Fields", "b2DWOt8gsb5UtOwfsq-0t": "Task Status", "_ktpLizL1zQbR4S-2a9yS": "File", "PSkqmsVXAnXwS4IEg_PCw": "Execute Successfully", "qemv8Jr6YLWEMlEjIqd7u": "To Be Executed", "ZTusx1IoqVDMV5n8ga3Kr": "Failed", "G6xSzxmYbLAVmP3-A9tND": "Executing", "dd9U0JHJTew4zoPUYl11x": "Re-Execute", "dbJgeT1ZDM7ccrHhrlq5m": "Execution", "f2qfydR_3cc6WkgIguaev": "Download", "NBv8StIMAV4Ss2MZz_cDl": "Successful Download", "tEmrFBYMHpu78vsHVCymD": "Smart sleep", "RhTgJ0vhpibaKqqZuOpiy": "Sure You Want To Name The Equipment?", "TxRDK1wUxDUn8xNJIkXRd": "\nThe device will  offline if it is static for more than 10 minutes, during which no command will take effect. When the device moves,it will be awakened and go online again.", "8GdL1zWMJptJ8AiAol3Yk": "Card Status", "OT5pi5ls02Yn-qXFicKOX": "SIM Card", "i_njN3t7VF2mJP1xxkkNN": "SIM Card Information", "J7GYiYHl6YxxMz6DV14ZI": "GPRS Function", "nKkMpCeUmGpAEZOHFubgT": "Close、OFF", "JH_oKJbhNVd2y3VDb6oS5": "Static Threshold", "UWZiT2UI0jtd6umy4pP83": "Off-Line Alert", "1BGMlf50Sr3tsvjmhkYJL": "After opening, when the current speed of the device uploading positioning data <= static threshold, the platform will determine the status as static", "lHr1G3dsLNsKPX2toJVeL": "When the device is offline, the platform will push the device offline alert", "NT9rhnVfP_H1_BU9AAWXS": "The static threshold can only be set from 0 to {0}/H", "ewfWEdsf3NsKPX2toJVeL": "<PERSON><PERSON><PERSON><PERSON>", "ETjPg2LN1EYYtVQsyU1Si": "SIM Card Expiry Time", "zLKnijHT1o-_DOpaFEyZS": "Base Fields cannot be empty", "4o8gSmZGcrwz70qV4Eg6q": "Oil Level Change Alert", "vovDYOhKoxvccZkmUQPKg": "ACC detection", "O_u_DNP0PxptNrO2O0dhC": "Forward And Reverse Rotation Detection", "Yhb1FXkSo-2p_oQYUpudn": "Notice", "CVCBH6Y-uYJ1j-m2sYz_A": "SMS Notification", "erhrhxfWEGsfdgeergdfs": "Map Data", "sdfghESDGdgeerREFgdfs": "Terms Of Use", "aEN_Am4XWjsh0ktkF23iA": "Switching", "dFkTJxdatpPvSNfTqBOLk": "Please Create A Role First", "fwgdgafafewgrhraqrger": "Check Monitoring Number", "erfdfhhgtrhrtsrefgbhg": "Display the monitor number", "ewrrertrhhfdhhfefgbhg": "Are you sure you want to check the monitor number ？", "fersdgsdfzsefrgfergrd": "Automatic Defense", "wqsdgsdgdhwrthtyfghb2": "Manual Defense", "5dibUgEIhQ_nxsvtyH-Gn": "Track Time Interval", "HT5-DuSlKusr_PXRq3BKi": "{0} To {1}", "YjNIXBe35TxkPNmhQXjsl": "Download task has been submitted, please go to task list to check progress", "Bad9L51R3bTOzbZK96v4M": "Idle speed", "sdgerhtnzbZKrtr4A445D": "Add", "sakFjkCsiFnKnbi32nwdG": "Speeding", "adasdwasdafadq321asda": "After the clock-in setting is completed, when the device is clocking in and out of get off work, it will push the platform notification, which can be viewed in the message center-station messages. Click the device name to view the working hours of the device within the time range", "asdwasafwsadasda32ssa": "After a long time modification, it will overwrite the previous Setup time", "Be2562h253grgsHHJDbRDAF": "Sharp turn", "BtyuwyfgrWERERRTHDAsdDF": "Rapid deceleration", "BtyjdfghtwsrgGHFEEGRDAF": "Rapid acceleration", "bausguOI4chuJCSUjcnwoo8": "Sharp Turn (Times)", "cveuiHXBbcjNCI65cvehJCI": "Rapid Acceleration (Times)", "YBGBXHcbeiHBUIbjhcwoi54": "Rapid Deceleration (Times)", "wugyrgHVBUJncw6cf4whcxc": "Idle time cannot be empty", "FDhxhjhe6gv5_cekhq64cxX": "Speeding(Times)", "xcjw54cXHDCG3cw_xkklklc": "Overspeed Setting", "ScefhxDXWc654CDCHcnxopc": "Within An Hour", "cxbuhy_cjnbxnX54VE6Vcjc": "Within 1 Day", "cxbujHJXIH5xcbujxbic45v": "Within 7 Days", "chchguiHGCEW46GFXhcij_X": "Within 30 Days", "xbcuCBUWCBIz56cv6we_xni": "Within 60 Days", "vniHCIHznjvoeg5fwhncicm": "More Than 60 Days", "bcxwuGXUG_xnwfd3vhjxwio": "Start Work", "bcwihn_xnwf5fdcdfoijqdc": "Get Off Work", "xujwguycx_xwf5v465cw6xa": "Leave Early", "bncwvc543_xwjbdncmjJXjo": "Be <PERSON>", "cbujBCXIHCXCVW5VBjbzhih": "Punch Time", "xbucBNXB_CXNVE4V5C5C55X": "Check-in Location", "cjwcnwf52vcwb_cwekfnbxc": "View Settings", "xbujqwfdbn_ckeneg2vewrh": "The start time is the same as the end time", "xbjwgbcxufgvdd542x4chxc": "The set time period overlaps, please reset", "cxbwf_cnev52cwhnicwxcqm": "Set successfully", "xufvuxinbqx9cn3wr2fvg2f": "Set Failed", "cbnwjedb_cxwkf6cfqhbcxq": "Working Hours", "xgbuwedfcx_xqwdbq5xdqwh": "Oil Analysis Chart", "cuwhbc_xbwf52xhxxqwdsxx": "Temperature Analysis Chart", "bnciwchi8cb2891dhxc129x": "5 minutes", "xbu2iby827tg29c89x9yd9y": "3 minutes", "xhi21b4xy89y1201090u1ed": "1M", "xqigd92x3t82brcvydgdiac": "All Status", "cbuwb2x86y129bx119bdh21": "Voltage Change Details", "zgu2918dfgcx809rc429_cx": "The user expiration time of some devices is greater than the platform expiration time, and the modification fails. The device number is", "cnbN8SYBnNXcj38u3vcv9cc": "Detailed Address", "dxb82y12ec2ycxhn23982x9": "Selected device", "sasdfwsdJfioOsadOI_1A": "Please select the user expiration time", "Ksdojos_sadiokvn15sSW": "The user expiration time of some devices is greater than the platform expiration time, and the modification fails. The IMEI is:", "Osasd_ojoiasdlkj_wass": "Please select model", "aOuinlkvi_5sdAvOsdWOm": "IMEI cannot be empty", "ansiuhWjhd_asd_QdwxDw": "IMEI does not exist", "oASdoioijds_sad12Adwd": "Device model cannot be empty", "WGSFGDSgdFnKnbi32nwdG": "The smaller the value, the higher the accuracy. It is recommended to set it to", "1dfgnfgndgadqGRTH234S": "Remark", "2dfgnfgndgadqGRTH234S": "Personal", "3dfgnfgndgadqGRTH234S": "Car", "4dfgnfgndgadqGRTH234S": "Motorcycle", "5dfgnfgndgadqGRTH234S": "<PERSON>", "6dfgnfgndgadqGRTH234S": "Truck", "7dfgnfgndgadqGRTH234S": "Bus", "8dfgnfgndgadqGRTH234S": "Mixer Truck", "9dfgnfgndgadqGRTH234S": "Taxi", "10dfgnfgndgadqGRTH234": "Police Car", "11dfgnfgndgadqGRTH234": "Agricultural Machinery", "12dfgnfgndgadqGRTH234": "Boat", "13dfgnfgndgadqGRTH234": "Train", "14dfgnfgndgadqGRTH234": "Loader", "15dfgnfgndgadqGRTH234": "Excavator", "werERGEDjty47thwhrw2r": "Access Server", "hrthwertehhgrjrryyuyu": "Please enter the 11-13 digit SIM card numbers", "tyjwerTEHAWEERerghrt5": "SIM card format error", "SAF2344asdfgre2425321": "Distributor Remark", "asduiunkj_sojdAojdjio": "It is recommended not to exceed {0} IMEI each time", "lkhoasdoi_ahdjkjnihds": "Import To", "asdioi_asdjhsjiohs4ij": "Enter one IMEI in a row (it is recommended to have no more than {0} IMEI each time)", "asiIojKODiidoOIjiwkiw": "Device Not Located", "rerdsbfgrygRG8retEqrg": "Last Week", "trhhfwertdgdfgqWERs45": "This week", "35sfdgwERGRGg534sbvfv": "Last Month", "hu8crhU0O5-9ObaQNtymN": "Turn on the idle speed status display of the device", "idlingDesc": "Note: If you do not want to receive idle alert, you can turn them off in \"Notification\".", "bzVDxO5OkfMskWoNiFsov": "Idle Speed Judgment Rules:", "s8x02hV2p9E13TkI1Xr0s": "(1) ACC status is on;", "561Z1QlQSV9ALHXwaOA2y": "(2) In a static state, the static time exceeds the set value (1-60min);", "cnWw9L3lCRWHXyv4IW7KN": "(3) The positioning mode is not 'unpositioned', and the recovery state is 'stationary' when the vehicle is unpositioned;", "86pBnkS4xC_Am4khna2WU": "Stationary definition: the speed is less than or equal to k km/h (K is the stationary threshold), generally the default is 5km/h;", "aasduii_15jdiusdiojAw": "Refueling rate", "asdwfhjkxohuUIjudoiuw": "Area Zoom", "oiuisauih_soIasdiKdiw": "Area Zoom Restore", "PjjiIkjh_1iojIjkdklss": "Reduction", "kjhOojolKJkjuh_alk2Wd": "Save as image", "asd_ihjk123_asdjsajhd": "No Matching Results", "xgwuedhcuiwhwdhwciqiu": "Idle time must be a positive integer", "uiwsh872y89cb781c289s": "Invalid Parameter", "knKLyHJUTw7crKa9duKC9": "The maximum time range cannot exceed one month", "asduiijoh_jkakjsdui15": "Latitude and longitude", "9VVTIU-Eu0k8W5Q_IiKoj": "Options", "Td5X-QbKaFt31TSs9FTV2": "Positioning method", "JBp30yKusF3Jvw8cQVZZj": "Satellite positioning + LBS", "y6P82AgvLwCA3gRisElPs": "Satellite positioning + LBS + WIFI", "ewr2334qRTEWrg34534t5": "Model", "ewrasf52334qRTEW425321": "Set/Removal", "asdiOIosdj_564Hjoijid": "Add subordinate customers", "hk-j6yAbxH519LUQTapg5": "Fill in more information (optional)", "2kiLagD1hB6MjdguFOHeh": "Historical Search", "znIORRJA2sCWxEuuJGAf9": "No search history", "nEXvu2BRLQvSN2q4zNh16": "Copy successfully", "PDD2x6KmulIA28JPPiWmf": "Unfold", "T2Sz03hEpYKf8z_mXsC9e": "Successful Sharing", "wUttprQVTKne9Pjj_eas4": "Link", "Todi_jvRP_qH1l9la6K-I": "Your friends can check your real-time location through this link", "i_FwdBnrmb7MzNMyx9Md2": "Detection method", "7Lg2fzoMMZD9T8t0xpX_P": "Capacitive", "nyTzyI4JkbFylgZL_8s6v": "Ultrasonic Type", "qPY1Oqb4GFE6p3lvhoJef": "China", "rvZdqRnVvEGgrInYrcao_": "Equipment Capital", "98zGSk76EGdJ2iIIoKbxe": "Total", "dfsf253663wreqQTRH223": "Please enter the correct displacement radius", "iCfNkRWDcS38BMFKnNEe6": "The export time range cannot exceed 7 days in advance", "sdfWET34N6n345knk2tt2": "Pedometer Mode", "wetdsfgT45626n345knk2": "Step Interval", "WE65fgT45626n345knsdf": "Please enter the correct interval range!", "dsSFGSDFh753A6n354Y45": "In this mode, the device will update the positioning data according to the set step interval.", "weafaE354SF234AFasdfw": "Fatigue Driving Alert", "sdf2345afaHHF234AFasf": "(1) After turning on, when the device ACC is continuously turned on, the platform will continue to exceed the alert value and alert", "safw36g346347DFGdfg34": "(2) Before opening, please confirm whether the device is connected to the ACC line and the wiring is correct", "rzPd23XcIb6rhIjDQxfQI": "Click to drag and drop groups to sort", "nle_9j8akFLmPCwbA6Tvt": "Drag and drop is prohibited in editing state", "JnbwpzQ1es7Knnztzs8BF": "Update Failed", "jCngDisNEtHDk2AbrnGT0": "Transfer failed", "_pPqRg2tUY4Ks1XYYQ22Y": "Please confirm that the batch of devices is under the same account", "gxywuyxyudeytgqdytgxq": "Expires in 7 days", "xbgwdygfcwygxqgbcdgyc": "Expires in 30 days", "xh27t6gd2ftx281xg1yd1": "Outdated device status data is updated every hour", "xg72stb27826td26d1276": "Expired", "asfwe87345a99T34g36kd": "Trace Color", "xg27td76gsdgb198ys1y8": "Your password is too simple, it is recommended to change the password!", "xh278dgtcfg7xchgx1h8x": "Added Successfully", "EG4bzTy443aMfL9yHir_p": "Collapse Conditions", "XYgitqAwgNUZ_WMaw417U": "More Conditions", "hjYHD87gdSZHBXCH8FJ9H": "Remember password", "cbuuwxgh7TX67XFGGCHCH": "Forgot Password?", "HX8UYguGBXCUYGWYGFHYU": "Demo", "EsLrJ7laPcMXBxHwm2UoS": "Synchronize user modification time? ", "xhq87s8s1ds1hdh1dhddh": "Administrative Regions", "GSAGuew823hdhdusuxjyy": "Fence Type", "hd823d5ddchf28H8xhd89": "Entry and Exit Fence Statistics", "dghd72f22ffdhdydyud2h": "Number Of Vehicles Entering The Fence", "cbchd72hfcbchjxzjzjjj": "Number Of Vehicles Out Of The Fence", "mvndjdbxjdjdhchd8dhjd": "Average Length Of Stay", "Bhxchjsd83947fhdhsjjm": "Entry And Exit Fence Details", "cn8c287y2gdhgdhxhxhhh": "Name/IMEI", "vnNDHhchc87yffggvh28f": "Stay >=", "MXDNihc837yhv8vhjcjjl": "Attributable Customer", "fn3uyaBGUGYBCX87bxhui": "Geofence- In Alert Time", "vmveioujf89c_xhje8hjm": "Time Out Of The Fence", "vmbmbiu4hbHCX8ycbchjv": "Static Time", "MXCghc87f2hgvhjdsjsus": "View Track", "cnc8c2hgJHXHihxuif389": "<PERSON><PERSON>", "sgeert4645FASRG23XFGg": "Static time must be a positive integer", "eq9r0DyDWJue8aasqqeSH": "User Guide", "asdf45DFfg325sdg5FAS1": "The command response times out, and the system will reissue the command later", "afeSR5SDG4g45gs23SGWS": "The command is executed successfully", "sdfwgrt457226EWT435fh": "You can modify the device information by uploading a form. The form file must be entered in the template file header format.", "WETNK235asgkn34Edkjgn": "The following fields are available for import modification:", "gfh3345sdg3425SDG2864": "1: Equipment number (IMEI) - required, 15-digit pure numbers", "sg36SER676sd2YR25745F": "2: SIM card number - optional, 4-20 digits, letters", "58FFRTJhrh565RTTRJ353": "3: License plate number - optional", "YFJ45345fhf5674RST345": "4: <PERSON><PERSON> name - optional", "dsfg64YRrtjty56645asR": "5: Dealer Notes - optional, only visible to dealers", "fhj25ERT765RT3453rgrt": "6: Contact - optional", "as357832WERU2458sgery": "(xls、xlsx、csv，no more than 5M)", "erg346RY455Yt33462rgg": "Part of the information is successfully modified, and the list of failures is as follows:", "dXYpGu45Rm1EWLEqXyc1N": "open platform", "g2345sdfg6t3300462rgg": "Fuel Electricity", "Yhr9Y0kuM8ANBfaRN3_NQ": "Parking Judgment", "kM8fJsLkgzgRDpq2T_SUx": "Route", "4PkVpEbTVm-b0kGGM-f4S": "Driving Time", "6TcxQMYvgXAQ_JSBxXWMD": "Supported Devices Items", "6TcxQMYvgXAQ_JSBxXWa1": "Offset Distance", "6TcxQMYvgXAQ_JSBxXWa2": "Applicable Time", "6TcxQMYvgXAQ_JSBxXWa3": "Single Alert", "6TcxQMYvgXAQ_JSBxXWa4": "Interval {0} Minutes", "6TcxQMYvgXAQ_JSBxXWa5": "Please select alert line should be delected", "6TcxQMYvgXAQ_JSBxXWa6": "Alert <PERSON>", "6TcxQMYvgXAQ_JSBxXWa7": "Line Speed Limit", "6TcxQMYvgXAQ_JSBxXWa8": "Please select a line", "6TcxQMYvgXAQ_JSBxXWa9": "Please select time", "6TcxQMYvgXAQ_JSBxXWb1": "Please select Offset Distance", "6TcxQMYvgXAQ_JSBxXWb3": "Please select alert way", "6TcxQMYvgXAQ_JSBxXWb4": "Please enter the overspeed alert threshold", "6TcxQMYvgXAQ_JSBxXWb5": "<PERSON><PERSON> only once", "oMUEw9fns5Q_1nPpGRnbu": "Route Statistics", "luet6c7PKCXPFY01x20CW": "Please select alert types", "luet6c7PKCXPFY01x2001": "Line Deviation Alert", "luet6c7PKCXPFY01x2002": "Route over-speed alert", "42iJdaFze5K4luDvSkt2k": "Belong to user", "42iJdaFze5K4luDvSkt21": "Start location", "42iJdaFze5K4luDvSkt22": "Ending location", "wegasqASDE32446sgwe01": "Line", "wegasqASDE32446sgwe02": "Tools", "wegasqASDE32446sgwe03": "Frame Check", "wegasqASDE32446sgwe04": "Regional Car Check", "wegasqASDE32446sgwe05": "Navigation", "wegasqASDE32446sgwe06": "Regional Car", "wegasqASDE32446sgwe07": "Line Management", "wegasqASDE32446sgwe08": "Please enter a line name", "wegasqASDE32446sgwe09": "Add Line", "wegasqASDE32446sgwe10": "The line has been set to alert, and the alert setting of this line will be deleted synchronously when it is deleted. Whether to continue to delete it?", "wegasqASDE32446sgwe11": "Line Settings", "wegasqASDE32446sgwe12": "Line Alert <PERSON>s", "wegasqASDE32446sgwe13": "Stippling", "wegasqASDE32446sgwe14": "Trajectory Drawing", "wegasqASDE32446sgwe15": "Navigation Drawing", "wegasqASDE32446sgwe16": "Click on the map to mark the route (click to confirm the location, double-click to end)", "wegasqASDE32446sgwe17": "Self number", "wegasqASDE32446sgwe18": "Line Width", "wegasqASDE32446sgwe19": "Route color", "wegasqASDE32446sgwe20": "Starting Point", "wegasqASDE32446sgwe21": "End", "wegasqASDE32446sgwe22": "Retrace", "wegasqASDE32446sgwe23": "Reselect", "wegasqASDE32446sgwe24": "Choose a device route > >", "wegasqASDE32446sgwe25": "Choose a device route", "wegasqASDE32446sgwe26": "Maximum time range cannot exceed {0} days", "wegasqASDE32446sgwe27": "Navigation Line", "wegasqASDE32446sgwe28": "Click to mark the starting point on the map", "wegasqASDE32446sgwe29": "Mark the end point after clicking to the map", "wegasqASDE32446sgwe30": "Add Waypoint", "wegasqASDE32446sgwe31": "Strategy", "wegasqASDE32446sgwe32": "Fastest Route", "wegasqASDE32446sgwe33": "Shortest Route", "wegasqASDE32446sgwe34": "Avoid High Speed", "wegasqASDE32446sgwe35": "Generally", "wegasqASDE32446sgwe36": "Renavigate", "wegasqASDE32446sgwe37": "Navigation results", "wegasqASDE32446sgwe38": "Waypoints", "wegasqASDE32446sgwe39": "Total mileage", "wegasqASDE32446sgwe40": "Please mark the starting or ending point", "wegasqASDE32446sgwe41": "Save Line", "wegasqASDE32446sgwe42": "Click and drag the selected area", "UVefl_f1h0ZTPQmWvaySJ": "Prompt whether to trigger the line alert only during this time", "pdMyZPIg2I7k3u3ByYLrU": "When the position of the vehicle deviates from the effective range of the set fixed line, a deviation line alert will be generated", "HhZrDdgis2Hm5-sP9EurB": "Route Name", "-YUdN9jL-AktN-FaK-AY6": "Support Devices", "t47OvSwHgN1jrekCjlFgG": "Protocol", "L0DK_-dPWIgIwVk29nx2U": "Location", "OETskCr0ggPlXNLTOD8cX": "Contact the seller for modifying", "cqY_Nktaga5MCpKzhZlzJ": "Server", "GCZMAUxDiNQjGiQlcWsgx": "Images", "HbdiV2RLW3mpJDkZ_KYFG": "Item No.", "P6hV_PyvDRj6HaDyYdaU0": "Model Management", "2S0dsK4yqAlGMmJh4k9PL": "Can not be empty", "WnpPKREGrpW2LdDnNXIzi": "File too large", "B7o8-BkLXrQP-C2VC1vRu": "Upload successfully", "PzBG2cs4d9h1G-yy4y-JY": "Upload failed", "6e8OYOZeRaCVXbbzz93ij": "logging...", "oo6OKdBR0WdWtwR9ww0BI": "Instantly locate the global location service platform", "J_O90-QzEYhF0wwpP_1OG": "APP Download", "0no1-wPECvW4oaBpA3PUH": "Auto finance", "gMBUxLUUq5HkqYp-IZMMw": "Username can not be empty", "SlzCfc3ZLA2qtLCdh9rQ0": "password can not be empty", "LoUsU3fXwcYRpNz6nk2Mb": "Incorrect username or password", "EvTOrBlsYbI6MLRmnjEby": "Simplified Chinese", "Q_JRX2AWfBfpvq0i7WSSf": "Basic Platform", "sfwA5_FWQER4kGGM23f01": "Total Quantity", "sfwA5_FWQER4kGGM23f02": "Supported Devices", "sfwA5_FWQER4kGGM23f03": "Week", "sfwA5_FWQER4kGGM23f04": "Accumulated activation in recent {0} days", "sfwA5_FWQER4kGGM23f05": "Accumulated activation in recent years", "sfwA5_FWQER4kGGM23f06": "A maximum of {0} can be selected", "sfwA5_FWQER4kGGM23f07": "Device Statistics", "sfwA5_FWQER4kGGM23f08": "Autumn", "sfwA5_FWQER4kGGM23f09": "Device Analysis", "sfwA5_FWQER4kGGM23f10": "Activate", "sfwA5_FWQER4kGGM23f11": "Model Statistics", "sfwA5_FWQER4kGGM23f12": "<PERSON>ert <PERSON>", "sfwA5_FWQER4kGGM23f13": "Field set", "sfwA5_FWQER4kGGM23f14": "Customer Statistics", "sfwA5_FWQER4kGGM23f15": "Growth amount", "sfwA5_FWQER4kGGM23f16": "Liveness", "sfwA5_FWQER4kGGM23f17": "Total amount of Subordinate Customers (excluding users logged in with IMEI)", "sfwA5_FWQER4kGGM23f18": "Monthly activity, customers amount who have logged in in this month (de-duplication, not included users log in with IMEI )", "sfwA5_FWQER4kGGM23f19": "Month", "sfwA5_FWQER4kGGM23f20": "Year", "sfwA5_FWQER4kGGM23f21": "Unit：ten thousand", "6CM5WFkktUM9AyRxu4XAm": "Service expiration includes platform expiration and user expiration", "HqKkLvYhwnihOKgZ5D5gq": "Service expired", "Ja6QviJTdBDcOxl2ngWXG": "<PERSON><PERSON> Query", "_HKPHYTEishptFq7vFBk1": "Field", "iLDn-CSjPBQCrfCiJcKgp": "Enter one line of data", "VLNvCttQNLcWxHSN-fPOl": "A single query does not exceed 1000 pieces of data", "k7qAMHi43Xjw2PYIQ38hq": "Maximum time range cannot exceed one week", "jaGWdGPfszdjZvW57NUiJ": "The time interval you entered cannot exceed 7 days", "pvqy81ufHUkt_cJ8QuDoR": "Confirm Password", "453T_t2i6SJoDBBAy_R6B": "Back to login", "dJBhX5e3-fFcp5QbouP5w": "The account is not bound to the email and cannot be authenticated！", "jDSJ8u2HnhtninUCX6vMB": "Password reset successfully", "qsvd2Se3tlv5Dsb9Hya01": "Collapse", "qsvd2Se3tlv5Dsb9Hya02": "Unfold", "QBfoK7OejhnKA31m5cUb_": "Target device", "dBc3sWmATHCVdJB5RtzRA": "IMEI/Vehicle name", "asdfwemATHCVdJB5Rtz01": "Only support transfer the point card of the currently logged-in account to the target customer", "asdfwemATHCVdJB5Rtz02": "Newly generate point cards for target customers", "asdfwemATHCVdJB5Rtz03": "Recycling point cards of target customer", "asdfwemATHCVdJB5Rtz04": "The card balance of the currently logged-in account", "swefqrerggARxHSNAfP01": "Positioning upload interval", "swefqrerggARxHSNAfP02": "Data Upload Interval", "swefqrerggARxHSNAfP03": "Heartbeat interval", "swefqrerggARxHSNAfP04": "Fatigue driving time limit", "asdWdGPfszdjZvWs765sg": "Satellite Positioning+WIFI", "uAtFq7CLH25vyR_9qCWOx": "Asset Analysis", "owditLvP75QFCxudedrBl": "Please enter offline duration", "OZTi4YTKLZz8u6-YnTnG8": "Offline time query cannot exceed 1000 days", "PopB3vD--FyqFAitPbG-9": "When closed, the current speed of device in the uploaded positioning data <= {0}/h, the platform will determine the state as stationary", "iwdChV3IahUyambbg9AFn": "When enabled, the current speed of device in the positioning data uploaded <= the static threshold, the platform will determine that the state is static", "u7_yN4XM3aiplwBPPyKQJ": "Turn on vibration alert", "HH3gLagydJHVlK2reit8q": "Turn off vibration alert", "PxYMk1w1YywfN127GyF_L": "Car condition", "avn4Z2zY-8AmiFArM_Kzi": "Recharge mileage", "1FnxxFoT2tdJpyAUbkQ8d": "Gear", "vZ_NmsWGw1TljsTvgtYSc": "Charging status", "gmiIB_fgQPFeHSPgUZ1OX": "Ineffective", "sjqasBHO4-7v_oE_oQhzf": "Not charging", "rr1RNSaQZHRkg3YmwbXN3": "AC charging in progress", "3NqztzkemfaYubkgd94tg": "DC charging in progress", "gW9Sr4E4DO3vcWEXwSANp": "Charging completed", "C3mHbV6ctjTFoPxWohWcP": "Release the foot brake", "eW5vpowqQzG4FDKqffwhd": "Depress the foot brake", "Y8SpiwQuS5O9oxoBoy2kL": "Release the foot handbrake", "E3uUXB0kIhb1zEAcQGLUI": "Hand up the handbrake", "RDwtFG4WvXrnr1gJn4FEf": "Time of obtain", "jAwo7Dt5bMPQddJLedM5N": "VIN code", "xWvddkQeFsyOfJIBomA-W": "Vehicle power", "NmYX0e6R4ef5q9at8zVBm": "Total battery voltage", "bgQGnhJJGfoSv7R2bHoep": "Charge and discharge power", "IFI8p5520lb5gVh9NKX4U": "Motor speed", "QVF1XS6JNRbW6dAeOGSpE": "Motor temperature", "_HAw3HjMpxf_5Li2ijxzG": "Footbrake status", "8BSkuJhUJVmpC-FyYv_tP": "Handbrake stu=atus", "dPrshwoE0R9HYtR_pqiyB": "Gear", "GFHU5hpHTATv03VLlUraj": "Vehicle condition information", "asdf2353WEEEW34463E01": "None operation for a long time, it has been automatically closed!", "asdf2353WEEEW34463E03": "Capture Center", "asdf2353WEEEW34463E04": "Live video", "asdf2353WEEEW34463E05": "There is no video to play, please select device first.", "asdf2353WEEEW34463E06": "The device is not responding, please check whether the device is online.", "asdf2353WEEEW34463E07": "<PERSON><PERSON> disconnected, please reconnect.", "asdf2353WEEEW34463E08": "Waiting for the device connection to time out, remove the waiting queue", "asdf2353WEEEW34463E09": "TF Card", "asdf2353WEEEW34463E10": "Can't record normally, suggest formatting the TF card and try again.", "asdf2353WEEEW34463E11": "Not connected", "asdf2353WEEEW34463E12": "Have played {0} minutes, automatically quit playing.", "KqFWT6QtVOAQ7hc3cr84X": "Please select a device or search for one", "QK_2GJFOQAXQpT2t--zKK": "Image name", "yC-UA-x1iWn-_8MWhMRUJ": "Preview", "UTh6HJ0cZkBaGYhmxiqzi": "Please select a date", "hDsjEhjGMV2q1OhZLj51s": "With video", "BVvJFDI4tb81rTca8ggnz": "No video", "nuxEh7ICQm1xi9uibTsEn": "Your browser does not support canvas, please upgrade your browser", "n4CXnjUAtGwof4KxtLIs7": "Capture", "rbxQKc5qyK61DZUKq4R2J": "Acquired capture images can be viewed in the 'Capture Statistics'", "G4YqkP4TlCeqrV4kaShwj": "Remote capture", "DF5rkRH7xPSGfwVqhIZpx": "Go To", "CKBlMeR7SBptjZx9In-Di": "The device is offline, please try again later", "9Gz9xO-UIA7OhYqouSYlk": " \nDevice  Inactivated", "bREKx_fI1e13-UqxZSRal": "Device offline", "xb_MxCHFkbfiDQK87uTTx": "The device is not responding, please try again later.", "K0DCR9FMFQUUR12RWd7nJ": "The query is successful, please select the playback time.", "Ai9BqK4KDDGgpUlIM0L3Z": "No playback video on this date.", "15zeVzw38Aza0pV2JpH8a": "Successful execution, photos can be viewed at 'Capture Center'.", "2UV-WqfP4CJZmmLPAGuny": "Playback ends, the device is disconnected.", "R4xVH6Gojtza6L_bDXWmZ": "Video retrieval timeout, please try again later", "RYTm5aEvo7HeIs2bpdEIN": "Fullscreen", "o9T1vRXeXagCVmJ2L7HXh": "Exit Fullscreen", "ixY_KDMtIVUvWecVMQTWg": "Volume", "P0UwTpyNrAmeEFn-ZtpL8": "Slience", "0_iZ3QPJnrJ7wWOLfcFt4": "Play", "xN4IbwPtNSUXm1NMMrCJr": "Pause", "gDT1meCgWso2DTOwunb0f": "The device is offline", "8QHejHRn_K6ApxX1HI_x2": "Please select or enter a customer name", "sEc-d5mjOt11-Bl13FHEi": "Please select or enter a device name", "hoaQubz4oNzQmOYis2iw1": "Set the playback date based on the time zone where the device is located to prevent the playback date from being inconsistent with the query date.( Can send commands to determine the time zone of the device.)", "-LMYZ7kqXRKWwUyYD7GOF": "Auto capture", "l4A6SV00MzaTtBzrPfqK9": "Photo interval only supports {0}-{1} minutes", "7x1-CyvyQU47Ks-9Mscic": "Please enter the photo interval", "FlBzcCTYSxWGdVm3pcxQP": "Relatively low", "wyDuKJCKh-KmMMTnY_bBB": "No. {0}", "94nNNXBShqMfNNc3dNGHx": "Date Limit", "xAyYnNoSfX2pbPVT9h1pC": "Photo interval", "8-ai5B94OPd5_wwtjQyZu": "Execute automatic photo-taking only when ACC is on", "IXsWeOfIA5805yY47UJGz": "Image quality", "wf2tZAJzkmSHhwNlwULgH": "Camera", "_Vr87IrKGdP_Z0Ir0boqS": "Please enter the date of the photo", "d_0qVpLU434hyMGjCIT_p": "Time limit", "IYUaUWFilxVZo9ESIhEy7": "Cancel device automatic capture mode", "r2u1VZH0-LuLyUjjtQp20": "Cancel auto capture", "alzrqMaGjzgsumqGYvejz": "Capture statistics", "F7bv-5zI8V1rO9ndXVOoV": "Payment time", "uOMU5cwmKPl8Qugv8cZpi": "Device service provider", "LvOf_dbnglLtOVFdtEwov": "Order status", "lYoEvNPHjrc6x3Ct4fh1n": "Payment method", "qzwEjrvjWERzbbwcvI5rK": "Package type", "SY_o_6OPrbX3-RA03j02e": "Recharge", "AnLVb2XOdBa6NjOeYULb7": "Recording package", "WZK3OsRZ4KO9I6X8u1mLu": "Wechat", "Xl9OJ9sjrHo_ukN8urbT6": "Alipay", "HSHNrVZVhYcCKzEXB2O_A": "Done", "O3QtNyUozWoUEwHSnRrzJ": "To be paid", "ufxwjQ0al34EVQsfU2cQz": "Cancelled", "9IGNKCfQfiGSnZ0ojOGrl": "Order number", "a9z-zLegU7yCzTuKkNtOC": "Payment order number", "wyF5AfveJkdrdwRnGU330": "Order number", "uRcRfIURR21OtZb2XaJIz": "Order creation time", "uigvo2391weT6pPqyfxYg": "Package name", "L8rvTj6RbNqMjBCUHrP4w": "Amount", "Vho8Z0XiLBfL9q77Vbq7X": "Serial number", "LTht5hSzFVGNGPsD-aWdO": "Package details", "XuMA6V1Wxn2UZv7ETqtnL": "Value-added service", "66mGM-OMMfoNzSaI85F5O": "Service", "UOIe7iu7who6AF5Ea9nuE": "Package type", "QIJcJzpIK9rdXE4EIl86s": "Custom time", "FEbdGHU9DzOupQt0oPGxr": "Common device", "vCx8UWyPG3dwdwKY7yN7I": "Basic package", "yFydQode-qpYNF6prCBa8": "Paid package", "PxiKJqRk2NZgR5rrcfbxV": "Order information", "tnVXZ-wvzIO2qu-BR6Raa": "Total amount", "xhBOcDdGN68_xdqLGD_AK": "Order number", "w6f4z-ArVpDgonjSek29T": "Payment time", "ZolzOIth0AcW8J0-AYx8m": "Payment method", "i9LCQ9tWnpPpxkzaSPAkE": "Order amount", "amountReceivable": "Amount receivable", "jq-noYxqRQlR1lpP_fKaN": "Payment amount", "BKMFu69paMH0rP0zUnz5x": "Renewal package", "FBmAMXGiheGz_xtB50egU": "Package validity", "fifkznhs7I8Fg-SaLDuGt": "Valid until", "VFMYa1yNoRQsy2FXnykHa": "Package information", "Fop3dk-nX2QIqUnQMVv0G": "Package type", "UzzQQv1w_yEq7V-Ar-RNi": "No", "ocMzF-bOfCsBqkM2MeBHt": "Anti-theft recording service", "9Z_0oEj77LZJQDoY7KtCa": "route service", "jSK9Ol1Oa90s46x2ts1w_": "Positioning time（upload interval）", "ryqVCbe_EynBQLBPkT6ug": "Electric Fence Service", "W7OC5YBgi9SJwShHGe-eO": "Statistics service", "ZlYns25c8Tujl-vP1nuEn": "Platform alert service", "rijikLyLIhPpZYsAQZHpb": "Month", "883ApjJAhE8Rjotd5jXrT": "Package ID", "jyVu4QMd46VDBzTPBAuAT": "Package category", "84HZrvlRdBMYzNbvpub__": "Package level", "dUxii2qTpMb8i9Lfp0kEo": "Package Content Parameters", "8NNHQ_xsjOjWx2o4u9cKW": "Package price", "hGZVKjFNvq1TAvLwxkAgq": "Package month", "_CXEjvnybYTxiZPXdJknx": "Package status", "cCQXQbp5AVce12lU6a937": "Package content", "nsqSz47S_fmKY2einyuul": "Service type", "uRDsAdnGtIGFDejyo0j0w": "Hierarchical", "QGJEx6AV2WGlBkvg2VPIG": "Overlay type", "z7PZM_aLZkhX-rRa1FZ0l": "Standard edition", "KoVO0P4i-qT6lUsN12fFK": "Member Edition", "ZSaRleSUdarb4RX1awLBT": "Platinum Edition", "sQ6JBdba8e6qBU57-Ik_h": "Permanent", "UGsdURGj2-A8w_d-tIoPp": "To pay", "k2Xz_evCamP4Y6Am33-Xj": "Activated unpaid", "Z4hoIjz3ox3rSFufsljh3": "Experience period", "2We31Z8Jl4hCBRM_pd5BT": "Experience expires", "yrpPHud6Y6vUqEW1LzSUh": "Nonactivated", "mgwsOBjaQLIs7QogPPWZP": "Membership package service", "mp6yVv2_LUo1Lr6d2St01": "Bundled", "_ik2yJTjlR7Rdqa0Bwweq": "Anti-theft recording ({0} minutes)", "M1I3R0AnDHCWu1mZ1OsVP": "Track (track within {0} days)", "Nw2KKVqus1cw69F_aMpji": "Positioning time ({0} seconds - positioning)", "l7rj88qG_-sQlNjsAGFZ_": "Normal Edition", "uiY_pCNl4iy2jsY8bSHQv": "Gold Edition", "WkEFol678sA7CJ17KAlFb": "Basic Services", "3nuuynf0P_SrOx0JqRKPW": "Batch Search", "70ERCDxsXgDD3RuW2XD6q": "Heartbeat time", "D4fkMsn4E25Wnmp2AAm_l": "Not detected", "OD6TR38aI88Bu7uB8uTwq": "Detection failed", "BHbhSwWp8seqYBF5anqUk": "Detection passed", "tT2sRSRlwvHp-8CBIm7bt": "GPS Time", "N0OhAZYIH0g9tcRa8qeD4": "Please enter 15-digit IMEI", "W9AT_wWgNGQiHIiUa0xzP": "Only support input {0}-{1}.", "sa8NRVeTVfugiuRw61wuE": "Wake up time", "M_TMom29kkT90sW_GtNrB": "Tracking time", "AKhshdLe4CcX5kgQ6wQbA": "Time tracking mode", "BLlJDrVJyhhSzQrSOxyh5": "History", "3xEp9e-_z1918x8ohGTow": "Select", "sAgQ82QEkN8XhiaQYF-FX": "to mark the start point on the map,", "x5p9J9L802EOSHMvXgfyi": "to mark the end point on the map.", "NC0GvIUNGMIckA487qi_W": "Account balance", "xsV0NNfyQJJhMNnQpfXJr": "Details of ledger account", "OQQDG7tWbK4xJ_SxAn7mM": "Service provider management", "-qO1k-kyMJ78r-k7hvTFh": "NO.", "RugUrOHMBSp8lZCLkCZOT": "Please kindly enter the ledger number", "tf9xHu3qD81AQzCR4i2Bh": "Amount of income and expenditure", "f8gnN4SjblDXQUgvZtnei": "Please kindly enter the device IMEI number", "lcNVANxOy5jOngZ8n6BSU": "Ledger number", "6zhGiJhw5vrsMXx3jSew5": "Recharge number", "y8ZOkzHwx2mVGslRNjFSo": "Actual receipts", "K5b1Or2vbenyxUOLm6DzC": "New service providers", "xgzOVj5OyHFE7FtwzEISW": "Service provider splitting first", "vyx_f6IfvRThEn7vSa7uh": "Service provider level", "UBGf6jO1VlyC5cZG6G_Vv": "Parent service provider", "KgSLgX2QXc5P4ebGJhoCr": "Billing split ratio", "5TuLCfhzC3c4Fhk9bFXFe": "Cumulative ledger", "jG2Um06j_7e9GgAO6u6x6": "Account balance", "S3i2lNWXrpS54EiZ_zVr5": "Ledger amount", "fkWmt4XVNoCEOqcqM53Vu": "Total recharge", "llSrRUSNQCGG3Tzw4CSoM": "Please kindly choose the service provider", "JNNF3uGWW4u7fYgJtmHcL": "Add service provider", "-bWCf0xmzSKqssy9O6C-8": "Description: Current assignable ratio {0}%, service provider: {1}%.", "cQCrS5O1FtY3f7CeXf9Ul": "The amount of the order after deducting the handling fee.", "XXlgxn8xHhDCvDo0nhRaI": "Please select a dealer user", "SnUXBvZwZ_rci24Ivvgrf": "HD recording", "rYEVE8FCA2SXW955yf98B": "Anti-theft recording", "QNLyVdYTL-D09KKJszyHr": "Member Package", "k7tmRHnc8k29NMS7xFv1O": "Leaving the fuel and power cut off", "GYlfTrb-v_i175rZ_TsnY": "Disclaimer", "nlPyvAgAv_1BNyr-98ope": "Cutting off fuel and power may lead to accidents, please use the function reasonably under the scenes that meet the safety norms! All consequences are borne by the user!", "YELMd4qb9ZvpmW11-5PX1": "Configuration item", "RsEwptNDs5Yk1gsTLvob2": "Whether to include subordinates", "Z_54Oo1YxTWx_x4DiIaBC": "Add time", "dDAKOU8dHan4D9HlS0Qwu": "Out of the fence power and fuel off", "mL45YNy1DNVrfOdSVUscD": "Configuration", "9m4QL0-j7BiH2hLI--u94": "Agree", "vSvvdtZaIL_oUd7hfGj-b": "Package modification", "UCE4NbQTRf2L6QFdVr56w": "Import Device", "ULguhh46biHAPnWL1-7As": "Modify record", "ZeQ1BBuCptskLmr-Jj8QS": "Recording overlay pack", "hHj3x3eBVUf2fJQt7aHYK": "Manual input", "PSBfdFXVla4Vk5sigF3Ct": "Form upload", "urtoz-lPdO_PHQIlaz5Zs": "Click upload", "1926HGNuX84IOWFR5BjL5": "Only XLS、XLSX、CSV file can be uploaded，and no more than 3MB.", "C1iN37-gSdpTsqWkrHJ9e": "Import account", "A_9Rad7zVPj2GBmUGpEVZ": "Please select package content.", "y6fr8Bq1SUa2Y4t3QsKh4": "Please enter the batch number.", "RVpUIj1EBBJ1awwP0KoTR": "Please enter the operating account.", "Q01UqFAPK5CgQBT6Va9Ur": "Performance mode", "x76HSqPas266y9PL13L2c": "Normal Mode", "IudF8SqtFP2jmoBEgx5RC": "Power saving mode", "_VJb7sn4Wp1sg_MpUMqqx": "Turn off location mode", "E19JqAWWpVfDPfQNztbi6": "Channel number", "76VJ_0CYltD6DDiTNyZxf": "image", "8j-cIxRn0mxuGcQu-J6U-": "Video", "J-XTISs_rbWSjK4D-RUnI": "Event", "P3xgcENq9CpwWHOJDCodc": "IMEI/ device name", "jyUQCyhg9_HqCbJnmTNj4": "event name", "4IqR47LAiISMhbMm7IhgP": "The execution is successful, please preview the image.", "RHSs8Xwa_q8HNTZN6MyTw": "selected:{0}", "0D8w0x3Z3-4C-ei6GzRO2": "Uploading, please try again later", "jpXZlwrJaiDcEaQiwy10A": "Select at least one channel number", "NowXQsGP0xEGBs1WjE9_Q": "Do not choose duplicate service providers", "cQosMBRHsqt_530cSmbwx": "The time interval you entered cannot exceed {0} months!", "xOwwQNdPzsClyM2sZEHsx": "Please select the device and check the date", "5x5ttmnhGQm_xfx8-fUMW": "The query is successful, please select the playback channel and time.", "IKa5v1S4C9m3JqKSNCGDm": "The vedio and audio channel is occupied,can not be opreated.", "PeLZGwiFPA7OD53c77fpj": "Make sure to listen for {0} ?", "xEKN7mlzVzK9GQt_b-Min": "End monitoring", "TE2fBaGlpdBY0lKWjOTKq": "Start Monitoring", "5D4MnWs_OiIq-I_QsGAyX": "channel", "VgbN0QpFxOoOVebTMifEb": "Connecting......", "-rwIcMW4enfFNVkLmJOOF": "Monitoring:", "Z2KhLcz1RQv-0BG_4cUod": "Connection failed, please try again", "-uah4lHNi9qpmlWruM9Tm": "End monitoring ", "SW8Lee_dtDjszssgsNc4W": "Retry", "ZrCxLSKRGicXpczHTBMUR": "Please select the monitoring channel.", "Mwu8g570bWTiEqGKSZBKM": "{0}video is closed.", "6zg6_aIDxHLfIYIsnh7wJ": "No video at the moment", "_SP67dHXDs5attlodtzd4": "After selecting the channel, click the play button to play", "-iSrrSkshPlUsGVHwnJlz": "Are you sure you want to locate the device immediately?", "BCInOUOWDZqvRtmRgoaIa": "Real-time mode", "rksCtLkboMzqxSXtG_Njp": "Ultra-long standby mode", "_d7RyxRhjvrOS12pZe1Rv": "Voice broadcasting", "Y-ouNNO1zuT0O25-wmQC2": "Recommended no more than {0} words.", "BgThYyBBQxKdbvTTxaqEu": "Sure to send down the voice announcement?", "-oHWPszUGv_bgmY41k0OM": "Engine speed", "rcVq-K-FhY0x_XGWgWQyH": "Intake manifold absolute pressure.", "WB2u4Gtzp_x_qB5aUdVA-": "Remaining oil volume", "D5xhxtuMiaCdB6ZtyfA-I": "Cylinder 1 ignition advance angle", "wiVXwMdYifHIIDTaYy7Uk": "Ambient temperature", "sxKO1EpE7584f885OstmP": "Atmospheric pressure", "hVS2wfr7_gPvVf2ZXdUov": "Intake air temperature", "nIvuguTEb64P4DeUO6Kyx": "Engine coolant temperature", "wL1nAP2XF8cqzzAea1l-M": "Long-term fuel correction", "ir8dRcv6tPQQT3b4fDUhJ": "Calculated engine load value", "pmSiApNt_l-CLgU_V9-EN": "Absolute gas pedal position D", "Jvzgr6i4iCYe861FOePqW": "Number of fault codes", "vOB_ErOfnohph6BFBeJa8": "Fault code status", "OGYkaAzV3zLdQVPWtr3aM": "Not illuminated", "6l5PUoCfEha4agcOU7c3c": "Light up", "VXAStgxYyFeoijgMgkPGh": "\nVoice Seeking pet ", "XqVd3m4oejVwBud-SvnxK": "Light Seeking Pet", "2247BJLuV1ixtjGA1O7gV": "Applicable Type", "vF3kO35MlJqcY7R7BojED": "General Package", "Bpdy-V0IcCvHQ_Agega0T": "number", "1W2i1KY0Bfz_8olXlC2d6": "Agent price (yuan)", "zUwkOXwA9wIf2S0kzBT7x": "Sales price (yuan)", "pWM8WfOGzZzcyBJMF4w4Q": "Sales discount", "-sHvr4dqzhBg8zhkGisc4": "Basic service", "SlQAbSeLcvKG64T_WCQ4v": "Free gift class", "WYZoPhp_aCouZhtTNRx_v": "Platform service", "ulJ0IYNw9yhO3xtOejMkq": "Equipment activation class", "BxnsRGI8yOGF3AIgmEDPA": "Anti-theft recording", "S5BLPa5cR1lfkPtX8C9ra": "HD Recording", "cu1pVC4m4_ExdeETrqlaH": "Up", "_7CU8hvXPxWIHZ2Hw09lG": "Down", "wgxerTVV3ubJSwGQbIRXK": "Guide price", "eGPJZha0zDcNjnOUxMl5f": "Sales price", "hqZrtrvlMrwFhVBJDoK9d": "Guide price (yuan)", "biaeNmEm5PyRHVooq6ijB": "New Agent", "woHbo-mjkIJGvCCXfPGdW": "Edit Agent", "krRoZPXODjW95IxZY3Jxr": "Agent price", "EoOkU9Au2oolyDpxoABEO": "Package Description", "0gTRzqUCLGERAfFhL-8y3": "Standard Package", "iqmW63hnN6IZmBcr4Bx_N": "All devices", "rY_cnjuM0KhLnfuxRdY7Y": "wireless device", "VLVXHDOnSopPJux0MHDsU": "wired device", "Cc1uwqoUoGjbyyWsaRu8q": "Copy Package", "vl7XPni0Dn5MHXeBkgUvR": "Cost Price", "Oe4zmx4CPyMN2Y_uT0kXj": "<PERSON><PERSON>", "hjX3McS9BgQRZc-zoimJw": "Operation Tips: After reset, the test data such as activation time and track of the device will be cleared, and the device status will be reset to not enabled online or offline", "0rxp9_tAb1nkVwt7ZfEG9": "Package Properties", "aoalRCCBuSPYz5q4mvZI7": "Cell phone number", "jDJyz2pFt82shF84u9X7N": "Service Status", "V0p40frI_DrTg5_KB9uQB": "Service validity", "F3tP2-szQjwBbJ2_Wgdky": "Track length", "AbcDEN7NWWYIeiIQTs0Ps": "unbind", "j-09UjLIz8t7ucYKWSTdZ": "Do you confirm to unbind the device number?", "ovK_RRKbTaBSEHhgL_kRy": "Value Added Features", "mMGDudnh3-MYiKN3ZmVZQ": "Duration of validity", "iIRxRd4XquTPvNwkbbPAt": "Effective Rules", "YEsjuadygPpzDlYbOQdfC": "Cost", "ygl1CCzlGB6pYQi5oJgMg": "Effective Type", "eIhE-MpijfPx1_YivOWfn": "Monthly", "N5usFqbTBj_x-AGRsPR8s": "Monthly", "a9_Kks1xUEIPs5523_ebG": "Package Additions", "CTqygomw_FwACb306Vgef": "Package Edit", "j_nyP6IyvoRo9kejtwukP": "Yuan", "ZVF3com1sFG-XrZC-livm": "Track service category", "F6SGMDU-K-qsjW7cV9B-3": "Platform service activation", "POuxG3He6OPD_8Jxozz0m": "Stage service hours", "KeHIZrek2q3vBPe4Cp8Da": "Anti-theft recording hours", "QKAK9kLAa86Xy4nOgZf-G": "HD recording hours", "iPiBIJga70QxIIRCBldDv": "Effective in the same month", "FXJLzQWH0isx1K681LHvq": "Effective next month", "tf0jk5t-3-we9pamkVaB8": "Cost price (yuan)", "o7LB26olpIL6MYrh3AEkB": "Package", "Lf5yEIWEaqmGp-MdVGn6K": "Please enter the sales price", "_0RCRKXXXIzu2xxfKtnTl": "Please enter sales discount", "ikXI1IxXKIQp8u1h9AA07": "Sales price X discount must not be lower than the agent price", "RB650X0GgnbB2n4QFIvyn": "Sales price X discount must not be lower than the agent price", "O2O9o9Gdf7kujPQNuGDx0": "Please select the value-added function", "hMbxn5NOJ97gDiey6VkNK": "Please enter the guide price", "AsjA-juIl1c-vrm5bkDZe": "The cost price must not be higher than the guide price", "hqIAi-yNhcVJJes_Wii8D": "The standard package is not allowed to be removed from the shelves", "1tG8AEnd8XObHHyr_LJWO": "The package has expired and is not allowed to be put on the shelves", "Zt6qNQ4s8YR-9ONkDbjz4": "Valid duration (quantity) must be an integer", "HcbviLThyjLIgx7yQhJIo": "Cost must be a value greater than or equal to 0", "PnNbXwUM5AIUvXD55zozb": "Sales price supports up to two decimal places", "8SZSVQ78DZJPp5CIkPmPA": "Sales discount cannot be greater than 100%", "M_FhAqKNUJRMUlAI_5Vg7": "The guide price supports up to two decimal places", "p0b0S03PgYUBMU8N6cMsY": "Other", "eWUjPc_p2SoSuyirGqpJC": "{0} minutes remaining", "KDjF1mSArjb_8e-G98TE1": "Total {0} minutes", "3bRqkRhUHfGo7-5bj1NSp": "within {0} days", "qPN0DtMhqG7A1BZBT1cLO": "Unbind successfully", "eabaCuk_HwzDEiN7IPbvs": "unlimited recording", "7o9hFnRaSRSx3gBUxUS7c": "Real time tracking mode", "yvNR-W0Zy8b780JbYTcDz": "If 000 is set, the device will stay on until the battery runs out.", "2akZqOLanUYhOK--8J8k9": "Recording duration unit", "UQEVNmYnChMFTjlzBF8HL": "flow（M）", "68DfXrUSOzBjsZopo9k6K": "duration（min）", "j8lyKEC7rR2Sp2J66aMSE": "pieces（n）", "thL7pcDqxtuMTgDoCxlBT": "pieces", "WZSRsRw77pZ671WKh_pPu": "Anti-theft recording: 1 minute = 0.125M = 6 pieces; HD recording: 1 minute = 0.385M = 6 pieces;", "wl6K0SUQd6ZY6YvzFM9u-": "Power amount before charging", "BumVkHW3JAn_PRo1yQcpB": "Power amount after charging", "eimmiPhZYS-8rwVnb6O_F": "Cumulative charging capacity", "HUdLf5Qxok84Kaszyp_fp": "Cumulative time", "FNSsRfB5Bxlf2Olm2Bw5M": "The vehicle is off. Remotely waking the device will take a moment and may cause battery drain. Confirm to proceed?", "0re2Dz1xGLdRIiOtLWBC7": "intercom", "jYDwCZu9Zq3SmUDyx02I6": "Connection is interrupted, please try again.", "kT3Em9RLb40yUyQU2evbW": "end of intercom", "J25nBN1alltybJIfS_Oog": "Connection failed,please turn on the microphone first.", "3mmuWWCL0vQVg_OKx7s83": "Intercom has been canceled.", "Q3niut5rzhCbc19r8z6IE": "The intercom is over.", "Pg7k0mGxvOxvRWARJpzfg": "Determine the voice intercom with {0}", "XNuroHgKla6Y8FamER4aN": "Monitoring has been canceled.", "jiTHwcnpRb8phd5923CyG": "The Monitor is over.", "3QLWf5J9RxGxyAS6uvvn3": "\nTurn on the SOS alert messages", "4RrCaePSVwyb6dIgHJAqX": "Car door", "7zhRC0Dt31JaorstFzV6V": "Please confirm that the device supports these features", "LTRnQg4MJhykYTWC-gy9o": "Car door status", "ABmfMli4oBWSNTfpwhgCE": "Air conditioner", "BqNwXl-ubCc-XQqV9O_ai": "Punch in details", "F6x4nIih9qd4aJ38EvJwq": "Punch in Summary", "FiAchNAkfO6rQ5HjVgU9H": "Driver management", "6IiHBsPsmt2DsTs-jU5T1": "Driver name", "l4zTYCxKwWS524zpXPvDO": "Vehicle name", "UeGir6yBHf-qmk0S1MGlw": "Vehicle ownership", "OXT0B09os-9cWGS3qpQlU": "Driver number", "8MPbkjc_ZQug88ZNTfODw": "Punch Method", "lDQIuVzHPDvREIDtlgET2": "Punch Status", "yfUqcxxldbfEsjLVNkxej": "<PERSON>", "ZyeVJa2gqbhhQO9I71t64": "<PERSON>drawal Status", "ji8RZWQkmXTT7ATPAAy_2": "Duration of time", "Pg8DZ2SFsTy8TojEeHX1B": "Punch out location", "0ikzdX00_UnFach5SBJXF": "Punch in location", "HIwKcqVBFF874LvZ_WGam": "<PERSON><PERSON>wal Time", "MToDC9TiRnmBcdPXl4FwO": "Attribution", "LnK9XV16hgzXMb2YOEzT1": "To bind", "rdH_UWyNxXZODhFqxxUmP": "New driver", "0Dt9UrCF2o2oZXyw4vW13": "Please ensure that the driver number is correct and has been set to the device through commands", "1leGeFJqaq67Fn4tiH1zx": "Bind driver card", "Srl9p3JczfeWAbfvhn6Ly": "Unbind driver card", "UGF865b9yIhFrTCVyLWxX": "A total of {0} pieces of data are selected, of which {1} is unbound, and {2} is bound. (re-binding needs to be unbound first)", "hHaTIsbL4r6vms19pugun": "Delete driver information", "dJ3KpaxrLmH_3rhK9unJv": "RFID number setting", "thiOWyWe1XmakaJ3GiOa8": "Clear RFID number", "AxP3mfFKmnFaXGqh5fIhI": "One line represents an RFID number, supports up to {0} numbers, and the maximum length of a single number is {1}.", "rfidTip": "One line represents an RFID number, and the maximum length of a single number is {0}", "d3bW-I2JF9X4k29xNZGGb": "Query RFID number", "_k-zAyJ8HPGbJdM17dzPH": "Unknown driver", "QO0NEmAJYU5fQSmklCOJy": "System matching", "PZufwLgOnX4fpRaD3iCSU": "Normal punch in", "9QOX0uIsKTjJBbo44I-Bb": "The RFID number supports up to {0} numbers!", "-leT9C--bOJXbur1tGMLg": "The RFID number is invalid.", "FVu9irj9xPTNFGBGvlobc": "Report type", "COZQ6iZB1zS4aElBMs78o": "Way", "qXO0YkCGN6h8FYEqKl3p_": "Historical report", "cGBoFIfJ2d5YNsf38ePbg": "Build time", "jZyH20rj2wEEEQcsmGcYk": "Time interval", "BpQY6g4_DbX8RhgZMVPQ4": "Add task", "qH0wuLIULJgKD37_nfLrZ": "List", "8hALcKt0MpF_SQGoBpb4K": "Format", "Mx9jIE1yFOacxR3pPnIFY": "Email push", "bjPw4KOPuYjc6NUfC-SyH": "Single task", "9AYF2wxsGFHQUgIvy034D": "Timed task", "AdhpSaYYkylLxIlRrLNHr": "Everyday", "n2QHPhR_EzYSzaNPQ6NmF": "Weekly", "9KLn6bfLtRc3MQgbsjyEo": "Monthly", "ieZ8uvcsbtzbKohADR8W1": "<PERSON><PERSON> selected （{0}）", "RyPBlexis_W5GIRM2muRj": "Quick selection", "G74wdj_2KXBhhyV7zDJdr": "Last hour", "TRBbE8yn5-eXAAnReeJJb": "Last three days", "HS2zeNZLmT6EroeI89wkw": "Not yet started", "iit63cuu_F-O9XEnoFvcn": "ACC ignition", "XUnTcb8HIcoLV6jX_-bHw": "{0} minutes", "zqbPU0UwwDMJdobNznkdn": "Please select a report type", "er_Ap5667cZtQW4X0jKfo": "Please enter the duration", "MJMdS_gSDGYTeKt7KPuKb": "please enter your email", "VX7OSMnRmFVq9SD6yTT8c": "No Executed", "gksm18T7sI0BjTgYSs3xr": "Name cannot exceed {0} characters", "usermanagement123asdd": "Device details", "s0ozWWyNHgC_hIgeFR28o": "Order details", "8nrk8LIy6KzpJQv8D1o0w": "User name", "nJDVObsHH5ogCl6MxIO4C": "Affiliated service provider", "daGXDH-K0OYEPY_EUdT-c": "Hardware device", "qdU58CUu3U1NuSOt3DBVU": "Associate friends", "t22DN7yhHaAkxCW69UYsh": "Last online time", "D-b7dWflNTTmQ5ISj_Fcy": "Disable", "fuKtObxB_3LepAmhP3yf_": "Service expiration time", "629CyKaKNeUB9bKRG0Pay": "Binding time", "4hM8uJZMOB7wXdwjRHa9p": "Are you sure to disable this account? After disabling, this account cannot log in to the app.", "yk5sO0QMXGD7l_MxoTXYA": "Mobile Positioning Service", "3aj3cEG2NB2VfffUFAdC_": "Please enter IMEI", "M3jEXD8nRHgxmpsbQKAuz": "Please enter phone number", "xzJrxRX1dNU-Rb_0eqU-7": "The mobile phone number only supports entering numbers", "gi1Rx6yaRf1mw_Ma10Og7": "Data recovery class", "0DzF5hCURmG4E1XvrInUx": "Available balance", "zTlleNA-rGV7NcSvnohCO": "Freeze amount", "j3-S-EHwypDM9f764RfWK": "<PERSON><PERSON><PERSON><PERSON>", "UpktOagmkL4g9LVa3s4DI": "If the user does not complete the withdrawal payment after generating the withdrawal order, the deduction amount will be frozen and cannot be used", "HdBEUEdRZtTN4ZfrwWDWj": "Withdrawal details", "cBvWd-2OWDLK-phHepxX-": "Please enter your withdrawal number", "3tBnaH8FayAwn1TmLrMRE": "Withdrawal number", "-2qRzHpb3yJe-NXYwm9Q7": "Receiving account", "4ox8EnAHNJ1ge4l1vollX": "payee", "CqTkd8dVrd22g3nDNVCoy": "Withdrawal amount", "0RwYjMyWnVMT6CIWbTvc2": "Application time", "YgZRMzhnb44UD16opnREb": "Payout review", "IhERyASJM8HWY4YSRVoeO": "Audit", "0GEWe45NcNov7FwWFSMVW": "Total cash withdrawals", "L6xr2tfBSDAIDoOf-18gW": "Real-name authentication", "LSUmKyLB2Gn7aIkH7L-hV": "Submit", "Q0SSVtTXVVVneQs56sfNV": "In order to ensure the security of the fund account, please complete the real-name authentication of the service provider, if you do not have a real name, you cannot operate the platform! The information is automatically filled in by the system recognition, if you find that the information is wrong, you can modify it manually!", "ExBfBD38BcFKpPdnaMMHJ": "Business license upload", "Wsx4i0OdVEnXu1i63lLdv": "Business name", "ICkaBpHUtevl_AT1N__hY": "Unified social credit code", "mzMzHjVtXGlHXVdehyj32": "ID upload", "Im9siJmOMFBKtFadxaSF8": "Name of the legal representative", "wRlEeQixlH9M9_HIP4vYK": "The ID number of the legal representative", "SgzsmJZu_MjkxFDKgtv_h": "Only {0} files can be uploaded and do not exceed {1}MB", "qC-26Qu65eWrPudHuLnuV": "Supported file types: {0}", "zEv9TzyaPU0xOkzgdVrzd": "Maximum upload file size: {0}MB", "mDQb0v7xumz3q5VuF3Vmc": "Face of identity card", "2ok-cfZmy8rdxwZL6-4nd": "National emblem face of ID card", "IkmaKekEYC3fKh3fRt8NS": "Please enter the business name", "_RtgmCH5vQ8wdY1tlQHW7": "Please enter a unified Social credit code", "nisCJPEsBiP2BkHolBjWx": "Please enter the legal representative name", "omKEokmgAc1zk-PZxqP-8": "Please enter the ID number of the legal representative", "y1yB9wGJ8CWF63TCI9WpX": "Please enter the legal representative's mobile phone number", "hQBDtnCXVOY5IURttX9Wp": "Obtain", "Pzs_J67ueBLvXIl0jEXSE": "Send successfully", "ZPq_ipnNUy_FW5VWSeimD": "{0} Resend in seconds", "_blypwAlGDx8o0-3JRFdO": "Please upload business license", "LTy7Te4YBG56m2zQdlHiP": "The ID card format is incorrect", "0ZqoYIX5loeLfUCWxwKB0": "The format of the business license is incorrect", "IYmN5ElWd2GKDXm_6Ox7l": "The unified social credit code is incorrect", "jjTIqk33Rdkkx6Py1HxoY": "The phone number is incorrect", "FDRZnbeysAjO9tp4YIYqv": "To change the real name information, you need to contact the company's business and change the information offline", "rbQMeechuyoZqAFzEyLM6": "Enterprise real name authentication", "TI6ctBVhkuX19fhWGPn49": "authenticated", "8jyucpdW1YuSmtRGAIb6K": "Business license", "IsF9Jc79BZ211alKXLRR0": "Identification card", "QFiEj7y94A9Ja8AkWUNyf": "Real-name mobile phone number", "qd8IBrhi8u-w8Y7DF8b-q": "Id card of legal representative", "HAN6a6h7V1iZt0lnwGGcV": "Change mobile phone number", "T83DURPRdSVIBzAvsaeez": "Please make sure that the withdrawal and collection account can be collected normally, otherwise the payment fails, the processing fee generated in the withdrawal application process will not be refunded!", "d5h6lLvBW7oXMESBAf-xM": "Enterprise withdrawal", "9dNZZYaCdlmDKhSaYelkB": "Personal withdrawal", "GZy7CzuyKTwsBKOmuCs1T": "Information authentication is not currently performed. Please add it first", "JN6k5nduoDoNFRKZS3DEn": "Deduction amount", "pHaDrlW6Ws3jwOkuAbQoM": "Special invoice upload", "t66P_5Nqai7Za4Eu4ITGB": "Template file", "uvVmCHuLW4xeFcLm45zbc": "Upload again", "qR0w92Dg9fchUpj2DzSC_": "Supported file format: pdf, maximum upload file size: 5MB", "uCBzEQrQku-26KkCRss4A": "Amount received", "fU-37Env5DcFrKxRb3xsW": "The withdrawal rate is {0}%", "vcjXle_BohdJUl4d1oLue": "Please read and agree to the following terms, Service Agreement, Privacy Policy", "7SBVFKuCpv5odq1HN33Ln": "Change", "Wx9bkDQ3RJ4pkj9EV2TAy": "I have read and agree to locate immediately", "w3RNmBUSW9RqkqTEoK59O": "Service Agreement", "OqJohIkvVLQABgxc5DRdr": "Privacy Policy", "bjGxifi3699uiCO6gsCRV": "Account management", "xiLtJRgF_g8uX1RTxKvfX": "The above information is automatically filled in by the system identification. If the information is wrong, it can be manually modified", "NaD6zzw94Z1os45DPZwjr": "Account opening permit upload", "CUmdfQcRlZ4i_0jCpy3a0": "Withdrawal account name", "nMIYlhG5tsGuYCDxDF3tt": "Bank of deposit", "8Q3xmvZ9DUvHOt7XY-i9i": "Invoice name", "BjInLzWyZCAq-mP4O2kg0": "Name of buyer", "xCBXDogrKXOiq4chx5EwV": "Duty number of buyer", "YyzFoNwurUCVV9H6hgtsA": "<PERSON><PERSON>'s name", "CCjOqQunpaqrCRLHmX3N4": "Seller's duty number", "aBPmzVAoqSZiJWXyn9bN-": "Tax rate", "REFy169sYWJ7Md2hb8sGE": "Please enter the withdrawal account name", "LWAmlhiJWmHzYrie1S9N1": "Please enter your account bank", "pJWb9Yer6FxP6ovkzGrK_": "Please enter the card number of the bank account to which the payment will be made", "WDW0-yYxFWMfodLosOWb9": "Please enter the invoice type", "sgfGs3OTZyvteg31GHtiX": "Please enter the buyer's account name", "a2ULMzDVkEk-nPXKBJUXI": "Please enter the buyer's taxpayer identification number", "vgTEJTdZurmuqJhzrZ29P": "Please enter the seller's account name", "vKsW1EMNxUJGGfV4ZYBXg": "Please enter the seller's account name Please enter the seller's taxpayer identification number", "TvCzo89fxA8tR-8Lpyb0q": "Please enter tax rate", "Fkbqaa_km9aC0nyOkzceB": "Please enter your mobile number", "hnIRT9k39y16Q99rQQzsA": "Identity card number", "oKb5Wq5V5XoAfzU-M0Otf": "No bank card information is added", "WryJR0-_zuJIjTJ4EKK1z": "No Alipay information is added", "zbt_yS1gKEnz57prLvp2K": "Bank card", "jvnedWVXi4gkwKd5I2hvo": "The above information is automatically filled in by the system identification. If the information is wrong, it can be manually modified. Class I bank cards are recommended", "DE215MPVNCkdUHvjIgGZk": "Bank card upload", "Fllf7DIpwifaI2rjf1wQO": "Receiving bank", "L_lzmLBmj3YzI8mZG5J0S": "Alipay account", "LgIQiPo4j90qzVhoe0Vy8": "Comply and agree", "sdFitZwo_yLFH4QGGN3Yt": "Flexible Employment Partnership Agreement", "5tav7xkbaZ8_ibteH8f-S": "Old mobile phone number", "_0koNeMLJ29pQU0iaEDgu": "New mobile number", "DxCo_EDksU3itpOI06hg-": "Please enter the verification code of your old phone number", "KKTmtCyrgM1yvbXV5zHcv": "Please enter your new phone number", "3-OViSd7qngs2IVge-AK5": "Please enter the verification code for your new phone number", "oQ34kNi30enS9BhzsG0H-": "To change the real name information, you need to contact the company's business and change the information offline", "0sCR-_9qqz7K1qILK3DVb": "Please enter the withdrawal amount", "EbBJyirZ75fpO-gVDM83Z": "Please enter the recipient of the account", "OsDOX2z2g1Q-gK32LRxFM": "Please enter the ID number of the payee", "_l_YQpYxCoNyeAL5aqwGZ": "The value contains a maximum of 50 characters", "pWME5Atx2F7StkRlHkIm3": "Enter a maximum of {0} characters. One Chinese character is equal to two characters", "qkjCxOqPs_QAibUK-0Y1Q": "Enter a maximum of {0} digits", "WJLplQQ9j88JLH1YqCRmA": "Enter a maximum of {0} characters", "TF-2MZlCuvwuW28jBv6I0": "Please enter the bank name of the receiving account", "w_r8IwPRfn4mFsJI9VODn": "Please enter your Alipay account", "qlaqPAd2iRvHO0ZwbK-za": "Service provider account", "VDbpijEe2xTelunTQS-4e": "The amount deducted exceeds the available balance", "eY_5eabsCmQBbE3r0rV5a": "The input amount is less than the minimum amount, and the minimum withdrawal of a single sum is {0} yuan", "TRJhAFDq4ep7qm7BVCQXJ": "The input amount exceeds the maximum amount, daily and monthly limit {0} million yuan", "nST-25toc3noZP0cfW1qk": "The amount entered exceeds the available balance", "DK_Faq17n14OmHECO018w": "Withdraw {0} yuan, remit {1} yuan, deduct the balance {2} yuan. Are you sure to submit the withdrawal application?", "GzYwkFzGANgH5TJD5X8X2": "It is expected to arrive within seven working days. Are you sure to submit the withdrawal application?", "xkj_ATjt1xs4_R-rg1Ji0": "Change the profile picture", "5207eon94HDHbK4sDdL74": "Formatting the memory card will not be recoverable, please operate with caution!", "EHD3KoOJqi2GCWNVeEmwd": "Format the memory card{0}", "kO825bS74aibjof-TZHgI": "The number of exported devices cannot exceed {0}", "rwsPrmsetvYwcvgy_tgQH": "Please enter a positive value between {0} and {1}", "xN-U2ljzfROs3R2UFCYsM": "Amazon tracking number management", "_N5YSHeYO9S0dkx3zF49M": "Please enter tracking number", "nBb0XGJJR6Ejlzv5qSHpV": "Amazon tracking number", "9tWLlIJg_5sgvhCXM44UQ": "Please enter Amazon tracking number", "IyD6K3KtVOOO_VcBTjgZ2": "It cannot be restored after deletion. Do you want to delete?", "UFmFUDIdFnppVa1V3bBST": "Upload again", "_oBDgDEVJpXqs_vs3_HI9": "Template file", "YGoedXAVvM1MW-3niNbjm": "Imported successfully", "Wi6h3aRHdkRiVA_ltXWj0": "Edited successfully", "QE818HiR698_BEiYMx3u2": "Only supports execl files", "uTwenDZPA7tqG0989SQXu": "Package Discount", "aaBbwSAmqzqUCcJRh515g": "Agency Price ($)", "QLgJjy0jWjsRUEDzFwoX8": "Sales Price ($)", "OFM2oOsQ2x9IwEF2ttv_b": "Guide price ($)", "xOo6bW69d4UFSs5A8Lc6t": "Cost Price ($)", "gQPrng0oObyjc6BgSvrjD": "Dollar", "g68FVq2_VXAjp7hYpDiRN": "Handling fee", "mj3M4r0KwKkzvAFrIO1Wr": "Amount received", "5-ZgZDBneAm7B6wX8GojM": "<PERSON><PERSON>", "A6LwrujHDGaAuqduxVNP_": "I/O settings cannot be repeated", "DVbzGOXzFw3chkPtHUj3s": "Recording operation", "bindingRecords": "Binding records", "LpQxX7pBohd343PV2C6l-": "Build time", "_b20VKdHpaKqao11ii03n": "Operator", "w7fGz3NHk93u9dv-zX0QD": "Affiliated device", "YOrAWZ7vpBk3TJg-dkb4P": "Recording", "F0uq5S360ilbM_B2J6AKU": "Static Duration", "Fcj9rd0TdhVtlRNldRgLO": "Static start time", "sTK0zTcZFNQTdsKoeupiA": "Please enter a positive integer", "AAQN_3lzSsjg1ihjW8Ox6": "Please enter a positive integer from {0}-{1}", "sjsJKFIklgkg1ihjW8Ox6": "Day", "SQpS4TgBG3fMcqbMSt5r6": "Family number settings", "jdlS4kVTg_CeomNPAM_bK": "Format memory card {0}", "tcqzqrxZ_gYuVhkt8OehJ": "stop point", "T6iMJCitY1kEG5bcW9HaV": "Share link parameters are invalid", "-6NUqOp6i8GMNZf-wX0W2": "Slowly", "MTpCOGV40pnn900xY1J0D": "Fast", "W1n3IpE3yaJTXBtZ2UgoA": "Fastest、Very fast、high speed", "lmCYFn9l-af231uvKKXNg": "click to show", "5l9_lUSa6C_lFR_eESOlJ": "Free trial category", "PjztEjunq2DdH8CUhaTom": "Friend increase class", "5j3kZHTwBeczKhgIlGP_-": "Seconds mode", "OH_8D7HPFdgNgklMyg1AE": "\nID Card Wrong", "OfBdh8qjrGLU-ZdWl5vOq": "The account opening license format is incorrect", "OWZM_rW5ca_xlQz_J8Xvy": "Incorrect special invoice format", "9OPyc0VO0-5p0d5Qed7Qy": "Please upload special invoice", "6HtrWK5wh8axd06VVCS92": "Set as default", "uOPB9tT30SyYk_0INSAYq": "Please enter your Alipay account", "n_gZB7je9SGq7ENwoME-1": "Only when the first withdrawal account information is perfected can the withdrawal operation be carried out", "hIpV6Yp1O1wSHehp-inf-": "Collection account cannot be empty, please add", "RMrKKjo43e39krDHu3aj8": "Please abide by and agree to the Flexible Employment Partnership Agreement", "PA1XThpT0ZeCsgxFK0j0J": "The card format is incorrect", "tKjv5WnytxK4wm-WJXePG": "Select payment method", "XFJzOJWIXzS-m-Ny2eXc9": "Please enter a positive number, up to two decimal places", "ESkCYm4yQfnXTqmN8lyEM": "The old and new numbers cannot be the same. Please re-enter them", "2cdbOHjhkxnwglIpBtMkT": "Real name verification", "IiLSldp0KbladWLu3iyAc": "Please upload", "Wo_ctGJBTe7KxuJltu9Gt": "Avatar preview", "JmBSnAvO4DN3XSi1cxZnb": "The withdrawal amount cannot be empty", "Z0Tm_VlEKyRO3z_N1zVo5": "The deduction amount cannot be empty", "85Bv3yRHBX1Q-bx2fsC7i": "The phone number cannot be empty", "zovpNYJACi9yNGKOVuO_j": "The amount received cannot be empty", "6_bQ6z9UwX8_tB64es-tv": "Have not uploaded", "G_w7OaWVA0Fc_ZaYRdEet": "Business license uploaded incorrectly", "MNysP677Xd0NQ6tBqvUxG": "\n License uploaded wrong", "oJ1yZ_MvfuN0QEeBrJW39": "The special invoice was uploaded incorrectly", "n-cfAU-iHIIcYW0DRnb1M": "Please enter a contact\n", "1kfaRbMY51Sk_FlCts7OX": "Please enter the contact address", "IEdjqcPX5tg0KEDbRFuFf": "result", "u7luzUuxiNGav9UQrcdN-": "Payment slip", "Z3rtP8jS9MkkLD-7eNsm9": "Payment audit", "0gKC2yGndZbATta9DrMLH": "Please select a result", "Jht787aJMAS7FrZc5PFwo": "Please enter remarks", "zsci9ZO7C5fd2L3lpNgPb": "Please select reason", "-OG-7bm5-uMXgqOUZJs5E": "Please upload the payment slip", "vDoHr7o7eBmMjapFFY8OX": "The user name is incorrect", "FEiCv02i2fFwURArNjISu": "Account number is incorrect", "YTPo1eg1Yz7EQvr72KecO": "It is recommended to consult the receiving bank or adjust the remittance method", "I1GGIAAo0PS8mfokKAxQO": "Please enter the reason for the failure", "21imeJSSKJ9gbnFeLZBAO": "Exchange return audit", "DZzQ1lUL4GRdGJWqYGSYd": "reexchange", "xdADy1Z3oZAlvbj18SpWe": "non-refundable", "2tHLcFI4qtrbhNizgLM_e": "The bank card is class II/III, which exceeds the collection limit", "GVKUFE9vSgm2vThS0OcLb": "Enterprise account", "2xItceXA56C70CviN7hdw": "Personal account", "LZhiHlMRIm8k0EoZom6Tx": "Withdrawing", "WYfBrkaFrjnSb4zcrXvXP": "Successful withdrawal", "uC2ieR0grcypLFaU4Cin-": "<PERSON><PERSON><PERSON> failure", "fiYBECKlqfQ37zsh0oe3e": "Withdrawal rate", "_ex7EzDE-2SAKg28OfvjC": "Account type", "rcNxw-zV_WkSiLzT52c7F": "Audit account", "YffYxg-01J2YhI9tvT13w": "Special invoice", "uytDBRHTcdWnmzmK6fKY0": "Processing progress", "PiElRejZ-v3cSTBDm5qv5": "Withdrawal application", "uj4JVD9nGaVo7z7c3-uH9": "submitted", "DLKB29kasijsA6oH3Hi1h": "Withdraw cash and transfer funds", "5U7GHtibp0j-Y7dysyT4-": "Check receipt", "VYdJLhD50Dg1KJJMCmDFZ": "Account change record", "yqvaBjHTsKmA2LqSHAMJ3": "Please enter the ticket number", "2dcfGeIDG9wGif_SW6SCm": "Account information", "xM9JFuMrJJaalkJwLOYoj": "Electronic invoice", "RC9ijDhKKlVpzWTJ_XeCd": "Payment method", "k6EP4_ZJK39lzc9AwkWvT": "Separate account", "7lftycmlnIBPgSygUr96m": "<PERSON><PERSON><PERSON> arrived account", "S2TQ3uhLwMNpAtrlBHHLZ": "Offline distribution", "LTDA3b7Un6GgIPY4leo_H": "Offline withdrawal", "HrJ-Uh34Wr3BSUdYA4iZ4": "Withdrawal commission", "YdSMTbOvyY2TixaUAcudm": "record number", "by_j84zg2KXDekxc0ORKK": "Mobile phone positioning category", "WNFXoZ2k0AQRuxAeHiePH": "Package gift category", "1Lm2fks-D28PGJDB2-QEm": "Gift record", "NSVI24la1iHcxE77XkD3j": "Please enter order number", "LbFrBk6riSeMQhZfbSFBU": "Associated order number", "24jZw1sAUlrUcHXVqC8nl": "Old device number", "ljiVq_vz1IcyxSBvLz5n9": "New device number", "BLHhC-hCNlmfdLUyrpBGE": "Transfer content", "dCzQ4KFY-2pAvEdvMcXbd": "Function items", "UTq55YDmzFRzm3R7o5pS3": "Effective time", "fcR_eRPV_vuaCod-X3pMK": "remaining balance", "eg65yL7TTeZtMUi-SI7Rf": "Package transfer", "9NTH28CxVugtC2EK38qjg": "Package gift", "YeAgt-s65pnisd3GVw5cZ": "This month’s gift quota", "8HGMdlzSUPltolCP43dQ3": "Remaining balance of this month", "e-k-39po_d2imfFB9rqcg": "WeChat Pay", "-6DPwMdNy_QQpwuo7kuOh": "total", "OVTUT6W1T8c44aoqR3P6N": "Please select mode of payment", "XhBA-h70LpYFm5FvDEQZD": "Scan the QR code to pay", "MKDFByNhuA4Q6lc7kKGnD": "please use", "h0svoUIo43SfkW63bLk3W": "{0} times", "VgvWw-6git1MC9SgonQOH": "The number of devices is greater than the remaining times! please enter again", "BcqbA7moiyqrLmNgNYVy5": "Please enter the old device number", "BkVM3eOcVuzpNIfjm-pkB": "Bound user", "ifvJXu_6FIFkEmAwGXVGL": "User {0}", "1ULnqJ-kbF80fAeQeJcku": "Please enter your registered email address", "MkwKY8UFV5BYmGR2Mxodq": "Mailbox format error", "jhesBFPUhQElo899N45RY": "Duplicate user email address", "Ef1mk9PHB8ODRFJoczZKV": "pet", "oEDqfKAIpGPQkQG8CGkgy": "bicycle", "aLxS4NInZyaUxSNvXTJB9": "electric vehicle", "3q5qw05Wj8yWZbxMIrGZp": "GeoKey management", "taCCGgf3d8bb84p3_id3E": "Key value", "qxIADlgwah-virZ8nR6iu": "Quota period", "Y66QsIM1QtLdK5WM2rd3D": "Credit surplus", "LGHa7JNWZdeF1_LXIkraR": "limit", "uIEW8VtyZTL8vSRQuCNDv": "everyday", "H9MpmjLPEK7MSh_UDrxrI": "I have read and agree", "kTLDGY0XzS0m8E0FCTjgU": "New binding", "tHuKOisIETCyXaBDTuGJj": "custom", "fnkuxm9_NrkH8_Tv3YleO": "Key name", "j7YuqwDqRlF0IjWiuKlba": "Google Map", "FTIf59K9kivPSetxIUrNf": "Tencent", "aIBhMRPmLVzRG-pQb1Mo9": "Baidu Map", "CHuYEEkDGK4YxGo8MurjZ": "non-custom", "FXXEmOPHqITyLJvOCEuUC": "Key name", "pk3H6woWKDwAEch-Xwb41": "Configuring users", "PZwpizZ-BjCmgRnSp048L": "Whether to delete key: {0}{1}?", "Jm91_vcsOlbdLFr9M28Qu": "Using the Geocoding API allows you to use the Google Address Resolution Service. If you want to continue using the Google Address Resolution Service on the Platform, you need to enter your Geocoding API Key on the platform. You can register and get your own key on the Google API console. You can set a personal free access limit based on your quota. You will be charged for exceeding Google's free limit.", "FRbY0el5plNTH4CH8dDPT": "Google Maps API Charging Standard", "9mmurV7wHvpIeR7nUXj2O": "key value", "ZidcfkEaoaegWCkr25Ddp": "Residual degree", "F2g2pJ6QAesDcCFK_8Ppv": "Customize or not", "SEpOsvkTJIs7rq1SuxBWL": "Key type", "JVS1v5gcUSn5hMyjRZiMe": "Please enter the key name", "cD5nN5W2nGnv7ehPyw5fr": "Please enter the key value", "26CTHIClRcVCK1bBRwhnj": "Please enter the key amount", "zoGrf2ereLlxNVDFzTq3t": "Please enter the remaining count", "SXBcD_L6s79BG2r1rbApf": "No limit", "wOFW2Ax2skLKfvbu1gLQt": "You can register and get your own key on the Google API console. You can set a personal free access limit based on your quota. You will be charged for exceeding Google's free limit", "tjCzMH4WONHn5m1VGfzTQ": "Please check the Google Map API Key Terms of Service", "SxnXfOEYbbEoCICpC21pX": "Please select a target customer", "unLEXxQ5Se8zqvgBrB8Qc": "Recording query class", "DFz6LZXDUzGC9h1Hnul2u": "Please select package type", "KFCuQkp2b0zldaAxmGwgB": "Email is not registered", "gy8pmlehGRoLkJdV_edY8": "Query vibration alert", "wlukedrwDRyOK0XeIkhZO": "Current device vibration alert status", "HihkzR3INnfDv_bS0ml8x": "Query positioning mode", "Ppa_zXemQO2ey5qX_mzXm": "Query SOS", "-S5Zj2mRhidmTd8IcTPAF": "Query the current device positioning method", "dvr_kBxVSCo9O4skA53qY": "Query the currently set SOS number", "rbAN2_LHXoCO3Rj9ifHHq": "Timing positioning mode", "CEQE89HjKKDIM5xBDWBA8": "Smart positioning mode", "OPHvVo3S-kkq8EJfg7gSZ": "Super power saving mode", "j-SWrpc1Vrf1S2wKVXsrg": "Alert mode", "ILxJ2c9W9RlSgcyXJU3AI": "payment successful", "sZW0PuFvcmAVNmno6QOc7": "Account", "mLKZ7cU5tvQwf6tn5ddby": "Please enter new device number", "xZblAEf-Ca5QidcBCgFWI": "Not for gifting", "l9KpGab_FPNpBWZDNAWrF": "Are you sure to transfer the package?", "9ti28ZU5Zt40xby1G_ahv": "This month’s gift quota = total number of activated devices in the account * proportion, if less than 10 times, the default is 10 times", "aizY__cr8e9LMRXQUTIgf": "Number of activated devices: {0}", "ux9Kw9v1rmHt6LahAEhBK": "Ratio: {0}% Refreshed on the 1st of every month", "bSBDQVvDZ5-2m1m1b0X38": "Cumulative income", "vx8cJaHHxhErDo_u98V9g": "Rate", "wvZQ-g667pqaS5k7ElKBJ": "Associated order number", "SCx72TSY1VcnknS0ks5lJ": "Total balance", "DeCU3YJEQZ4m6WTKYOimH": "System Settings", "JgTflL8nrYglkCeN2xVKk": "Unit of length", "uaDXv6VTf_i6KFdDpiA5b": "Kilometer (km)", "oz1Fpf2Cy2REH5VddyL87": "Mile (mi)", "_Mb6dEr1YRXYFPEh11ycQ": "ft", "TYzDOtOg8b-lSYzNRPTjQ": "Mile", "machineTypeSupport": "Model support", "aUEMcGA3UzcsMsybrAd-j": "Active State", "tCdgp3PrZMzQB5QsVmyih": "Recently bound mobile phone number", "KeCWjWFz4-VIYEIeukzc5": "Last binding time", "S741RuNHsAsXlx43GUApW": "Order canceled", "kIM7VbZMekH-KO6wIHSqh": "Cancellation time", "WRHOzHvIYZ7yQT7MYxaBw": "Whether to buy a package", "LNt5xB9FS0cjz8sZxzrB2": "Canceled order details", "8ki2CCZoENshLvjE7QLGh": "have", "5Sv5ytuUMlSPalt5usauH": "Equipment import time", "7HL8IXRIIp5cKUnInarno": "Ledger status", "J0UYZYck8uhBvlodD6N4V": "freeze", "W7PEgcWDx2hes2lBqOX4u": "Already arrived", "91_1vPtPXMzqa7sdwnbWw": "Amount received", "8cpgA7arN32LnJYYoHs6b": "The frozen amount consists of the frozen account amount and the frozen withdrawal amount. Click the amount number to view the details.", "iZ6Q-Cqe20id8Jqqi-n28": "Personal real-name authentication has not been passed yet, please contact your superior service provider", "IpRD_Ggq8UhJWAyYyRTo4": "Go to real name", "1xXJa_y9Dj73JgvFgLmpY": "{0} real-name authentication has not yet been completed. {1} can be withdrawn only after the real-name is authenticated. Do you want to authenticate?", "7R8ninLk0Y8vd9nEBvIPz": "enterprise", "oYXW0p5J3DkyunUzLbpq3": "Personal", "3bYcoGZhmlMZGYFqPsQMI": "Freeze the split amount", "DPJPrWEMHEaNY32Puj9ld": "In order to ensure the safety of funds, the obtained account amount will be frozen for 7 days. After 7 days, it will automatically enter the available balance before you can withdraw cash.", "1LeLeSLQG4tH3Mb7oPHNA": "Freeze withdrawal amount", "24RGgbcmuZQWvxaEek6P9": "Make up the difference (tax)", "cbV0W15tenwiobhwsxGKS": "Due to the requirements of the tax bureau, our company must pay 6% value-added tax. If the service provider provides an invoice with a tax rate of 1% or 3%, it must make up to the 6% value-added tax amount, which will be deducted from the account balance.", "MxKJZnXtEbLQy_CJM4Wo0": "Please upload special tickets that comply with the rules", "LgRB21jCFOEI2iFWMhk_u": "Please upload a special ticket with the project name as promotion fee", "fugpgJ6HPD0IiniTJJsu3": "Name", "wxLv0t9cVv88YKsE56eV0": "Enterprise real name authentication", "QLw5b0HuFkQor0jnyYnDQ": "Personal real-name authentication", "S_3XDTwVR39xyBxoDNayv": "Please upload your ID card", "YoC4H8soU_i0YdFgPxxDM": "Please type in your name", "Zxsxh9PZBmwPf4rykaJF8": "In order to ensure the security of the fund account, the service provider is required to complete the real-name authentication. If the real-name is not used, the platform operation will not be possible! Personal real-name only supports personal withdrawals. After submission, it needs to be reviewed by the superior service provider. The real-name will be considered successful only after the review is passed.", "tCAlTVE55bteYRYT6gDbc": "Identification card", "N9O3C4bv7mci7eE1eYJTp": "Please enter your ID number", "IjrAT01SxjPX4qgbJDgmQ": "Information authentication is not currently performed. Please add it first", "lDKrZEHddhAqje9P-dx2D": "not certified", "ACgMBoX8dES2ll2wNJlXu": "Certification in progress", "_6ZBaztE9i7WPTNBpU-JV": "Authentication failed", "NWB1XTgWnDZdVXApDMGP_": "Under review by superior service provider", "eeLhrjq3zdRq8RmncZ7Re": "Review failed", "f2J3ZgEdW0dBUMf-usvcc": "Certification", "Mbh9kHVbIMxc_aykJBsDr": "recertification", "wPblFbX-4CmURcOYNxNPM": "View example", "-UdcRA2f8GflwWkRatINL": "Review order number", "KT8cy99eeIOwTPqi3IjgK": "ID photo", "2B9eM3k51NkPbpIJauFfj": "applicant", "V5V_Z02EeDLZJVkjL5yB9": "reviewer", "H_WXP1q8rO_zGjM7z2l0R": "Audit opinion", "DgQ6Po_e9_AEGCu1oDKXl": "Approval Status", "KlZQCdiEceUY54vxcapV-": "Real name again", "r4G785OKstjScU_Nw3EzO": "Real-name authentication is under review, please do not submit it again.", "GWiBNGprbSzdB2Z986MsC": "Real-name authentication has been passed, please do not submit again.", "oJu4xk2bHPcEt3j1zcnqU": "application status", "i-SpeLRCOCbLglqTGD0u-": "under review", "OUv3PKk3G_Cc_gdEyENKW": "examination passed", "m9T-6rAOChrVMG0YDV8CF": "<PERSON><PERSON> not passed", "GrOaJg9APCHrf5k--_U_D": "pass", "WuZQiTqcBj7cGod0nj7LO": "Fail", "-NuVs-wS7J4GYTOwjFuMh": "Review comments are required", "et_LYLHFrM04XhGjP46tP": "Please enter the account sharing ratio", "K-fRsoopgmoiVxQIwvZFl": "The account sharing ratio is not within the distributable range", "lEttzd9lDWHeMmVtBmrNU": "Personal real-name review", "QHCtboh3seN6ui33CZmWz": "deal with", "OLvc_EzZFYBlBhNIXnjZD": "Enterprise real name", "Whfkt63jRh6VQ4s6QphEa": "Personal real name", "sIND263kTy5EADi1pj1pP": "Withdraw account", "specilUser-enduser": "User", "lhZ_M3uoptVK76DILnZ2l": "According to the APP operating rules, the selling price cannot be changed", "regaliErrorTip": "Gift failed, failed device downloaded as form", "bOBwT_f2edDC27PM3NQgy": "{0} items selected", "Gad1mLkSTY0eVBR4fnhNI": "fence list", "_uAM6l-rr2rggms5zdveJ": "<PERSON><PERSON> selected", "AqxN0F8RcW0DD7-uAWTI5": "Select customer", "evYC9JPWCRXiYcDMbZvWx": "Personal information", "NoFYSU79Nfj9dt-xcgrQ8": "The form format is wrong, please refer to \"Template Form\" to upload again.", "xbF35iFGLVAJnswRVH0-u": "Data association successful", "Bwg2kt_P1yhAb5-hjPOgO": "iccid association abnormality", "ek_N_GSAZAG9NyQXKbnJE": "Some data associations are abnormal, please see the download form for details.", "GbKT46C8x66GgpjgBjOqn": "File size cannot exceed {0}{1}", "SwOWzfqnAh0jCThFr4A6v": "{0} files selected", "4dGkAxc8S8l2COYbcDF-g": "Download template", "s02oAL3-DrwNUsBNxn6Bw": "To avoid association failure, please fill in the IMEI and ICCID according to the template form. No more than 500 IMEI and ICCID associations are allowed at a time.", "BMMOrNpblCgFpy0mjOb1Z": "Please upload files. Only files in xls and xlxs formats are supported. The file size is ≤100KB.", "c7A7o4pVd1rpivRvna5Gh": "IMEI batch association ICCID", "4weboexar_U03Ho0q5Shn": "Data association is in progress, please do not operate the page", "3JZI34eFq9qolsBRyHs7W": "Userdata_{0}.xls", "zdWLkJqxc7V4XODM1RAbn": "Loading", "remoteControlTip": "If the remote control disconnection time is set to empty or 0s, the remote control disconnects continuously", "remoteControlOne": "Remote Control 1", "remoteControlTwo": "Remote Control 2", "controlTime": "Control time", "restore": "Rest<PERSON>", "registrationTime": "Registration Time", "equipmentService": "Device Service Class", "softwareServices": "Software Services", "deviceActivation": "Device activation", "setProfit": "Set The Share Ratio", "packageIncluded": "Package Included", "serviceAddTips": "The subsequent service provider share will be carried out in accordance with the proportion of submission, confirm submission?", "ZEGgtLzv8abXPKrlPdzUC": "Automatic renewal", "ADC7SxVEyPAWy5cdn2lKb": "After modifying the price of the automatic renewal package, subsequent deductions will be based on the modified price. Confirm the modification?", "EFUKd-0PFs2kje_Z0c1h9": "warn", "9G-UExFllfV8X9i978qOe": "After the automatic renewal package is removed from the shelves, no deduction will be made when the user's current package expires. Are you sure to remove it?", "gou": "dog", "mao": "cat", "ucYI8ufupwygMJHhm1DQU": "Alert information notification", "desc": "Instructions", "driverRequired": "Driver Name - Required field", "driverRFIDRequired": "Driver number -RFID number", "connectRequired": "Contact Information", "emailRequired": "Email", "remarkRequired": "Remarks", "GrQZ6_ZOP74ym8FTQlT6z": "<PERSON><PERSON>, waiting to be credited", "S6Pgt6-_Vw551atSMNdO9": "transaction failed", "enoughTip": "Lifetime card deficiency", "package": "Version", "standardEdition": "Standard edition", "freeVersion": "Basic edition", "ImportVersion": "Import version", "serviceCycle": "Service cycle", "standardEditionTip": "Standard Edition (all platform functions open)", "freeVersionTip": "Basic Edition (basic functions open for life)", "UpgradeBatch": "Upgrade batch", "DeviceVersion": "Device version", "remove": "Remove", "ServiceVersion": "Service version", "baseTip": "The following basic functions are open for life", "nvx3Eu71rcjx9HzshOkfZ": "Annual entry point", "3umrb4aXbfmzceFs9R4Lp": "Upgrade", "baseVipTip": "Real-time positioning, circular fence, track playback (7 days), equipment instructions, operation overview, alert overview", "upResult": "Upgrade result", "upToastError": "Please add the upgrade device", "runTimeAll": "Total driving time", "distanceAll": "Total trips", "popularModels": "Popular model", "otherModels": "Other models", "6sWTyQZG3FPA7c6FT3VyC": "Humidity statistics", "b7e_Uxgecy2CNFWjkOaJG": "Humidity analysis chart", "DWo-vUEFZ0QNf-S0qokc1": "Positioning status", "rnIm0BaHQUeveQZGjQ5-y": "Whether it is effectively located, wired device, shows whether it is connected to external power", "x4U0kj1KZo88WNcFhVS-2": "Base station/GPS/WiFi/Bluetooth", "LgYLxSVSYDF0RSJiIbO6O": "Signal strength", "4mc0BBksNeCOzN13HG2Kq": "ACC Status", "vEYkN4a5nw7Mvi0ycE09T": "Start and end time", "accTotalTime": "ACC total ignition time", "VjZM92-uovWbEuTGQX-Qt": "Reset", "ONfwsGz2suFI3faXq7qOq": "Partial success", "MGzpzjvhwPnlJ2_B_WuwB": "Reset status", "D18M1wrudyshM_m7yv-nZ": "Reset record", "upgradeTip": "Are you sure you want to upgrade the device? After the device is upgraded, the rollback version is not supported.", "lycRATEE2FhUXs2E-l_NW": "Sound recording", "q-mRStgBB2oIPAQj7EXBt": "Duration", "-SbPXykJz9GwiZN2raM0L": "Already removed", "modelConfiguration": "Type configuration", "modelManagement": "Model management", "modelName": "Model name", "category": "category", "myCompanyTip": "If the model category is our model, the user can preferentially select the model when importing the device, and the model list is preferentially displayed for selection, which is equivalent to the popular model, and can only be open to the target user", "companyModel": "Our model", "baseTypeTip": "If the model type is a basic model, users can preferentially select the model when importing devices, which is open to all users", "baseType": "Base model", "modelId": "Model ID", "port": "port", "videoEquipment": "Video equipment", "modelAlias": "Model alias", "originalModel": "Original type", "domainName": "Domain name", "generalAlarm": "General alert", "超速报警": "Overspeed Alert", "静止阈值": "Rest threshold", "离线报警": "Offline Alert", "ACC报警": "ACC Alert", "疲劳驾驶报警": "Fatigue Driving Alert", "怠速报警": "Idle Speed alert", "温度报警": "Temperature alert (PC only)", "低压报警": "Low voltage alert", "离线判断": "Offline judgment", "录音": "Recorded AT", "追踪(秒定)": "Tracking (seconds fixed)", "温度感应器": "Temperature Sensor", "监听(视频)": "monitor", "对讲(视频)": "Intercom (Video)", "油量设置": "Fuel Setting", "车况": "Vehicle condition", "I/O设置": "<PERSON>/<PERSON>s", "实时视频": "Live Video", "视频回放": "Video playback", "companyModelMessage": "Our model, the user can preferentially select the model when importing the device, the model list is preferentially displayed for selection, equivalent to the popular model, only open to the target user", "baseTypeMessage": "If the model type is a basic model, users can preferentially select the model when importing devices, which is open to all users", "customModel": "Custom model", "timedTasksTip": "A scheduled task has been set for {0} device. The following results are displayed", "timedTasksToolTip": "Oil power off time range, execute the oil power off command to complete, automatic oil power off, wait until the cut-off time to end the oil power off, execute the oil power on command, automatic oil power on", "设置结果": "Set result", "批量设置定时任务": "Set scheduled tasks in batches", "定时任务设置": "Scheduled task setting", "执行类型": "Execution type", "日期范围": "Date range", "选择日期": "Select date", "时间范围": "Time frame", "是否开启": "Turn on or off?", "选择时间范围": "Select time range", "请选择执行类型": "Please select an execution type", "kHt0e3FCxBfFaaS8fs2o9": "Cut off oil into the fence", "QmAH9PaQGsFNjcHUM3n5d": "Enter the oil power cut", "eL2A3XxsMOLN0LfDsxexr": "Leave the oil power supply, enter the oil power supply, can only select one", "cNZ24bjJRWQo6Y-kbZs7V": "Password is incorrect, please re-enter", "rzbvqLXmVrJcwetdcqj1N": "The user's permission configuration already exists", "vjQ5tSfzfzJZkLEG4pRuV": "Pet information", "-QiINVEAEQs1lOyxoY2-U": "owner", "V7ZEeKcoq4ZacqGumFBso": "Pet name", "EF_z9YeHVGT1BuC0Xxt9R": "Home address", "BpPwm6MYFDChvmC_ZNEFm": "The maximum time range cannot exceed 7 days", "0j5o0HN0PNLvrneCrbb43": "Base field", "e-YOvR7f7jEgWnfpsPHIN": "Last battery information", "1DjgiuZQ8BexU2yv9t_wp": "Last voltage information", "5JuYDTW-zrpUmNxJw_nSd": "The base field cannot be empty", "受限驾驶报警": "Restricted driving alert", "受限驾驶报警提示": "Activate to trigger an alarm when the vehicle moves during set times", "操作结果": "Operation result", "sNuyqv3TTfegG8OtSl7nU": "Please select/enter vendor account number", "7kCT4o4MOEYlu09rJb2Q6": "Account number of the distributor", "xUIxHiKX3S634h2ZT-Hei": "Please select/enter the distributor account number", "dC7w9wyuS8ypXXErkJ-QS": "Distributor ID", "-gm8iiFKWgzDomRd-mtiS": "Email push", "rmrECn8K92VTFKS9hwdkb": "After this function is enabled, users will push feedback content to a specified mailbox after using the feedback function", "qzvBp0BIfI3fGjokogkL0": "Multiple mailboxes can be entered with symbols; Separate.", "tQHLaMWzMo2JN4I2BZ1jm": "Feedback configuration", "员工号Tip": "STAFF ID - Required field", "身份证": "IC number - Required field", "群组名称Tip": "Group Name - Required field", "身份证号": "IC number", "员工号": "STAFF ID", "群组名称": "Group name", "3000Tip": "If the number of selected devices exceeds 3000, select them again or export them in batches", "团队": "Team", "地区": "Region", "库存仓": "HUB", "团队Tip": "Team - Required field", "地区Tip": "Region - Required field", "库存仓Tip": "HUB - Required field", "确定删除吗": "Are you sure to delete it?", "防盗拾音提示": "You are advised to select a time range within 30 days", "录音勾选提示": "Please check Recording first before performing this operation", "录音提示": "Only recording files with normal status can be downloaded", "防干扰报警": "Jammer signal alert", "77_wwzNTTHafPEOC0QLk-": "Your IMEI account used a default password. ", "b3aU4VIlinEYLMqxOzRUw": "Change it now to secure your device. No liability for unupdated passwords.", "zflDZ6jTRVCKTu5u3tpKp": "Change Passwords", "8pEMx5x-VOk4MOLoRZRuv": "plantain", "aBF16On3BfsZNCUJ7A4yI": "pilot", "BDiR1RKwGe_Gkkjd42Suc": "in-vehicle", "aaRIAHINadX98uPLHcfWD": "carriage", "OkTXgR5JsQCfmgiR-pPei": "ADSA", "0NL__z7IrwP4XRWDx9a8W": "DMS", "YpT7EDmLyZivmHVHmVfBV": "BSD", "2EYnIDOIt8Y1ZBMp4phrT": "Please select a province", "06KhnrOrA4Bs_1vwHy2w2": "Please select a city", "KYOUEl9w_JqXiNDpynhBS": "Please select District/town", "timeType": "Time format", "timeTypeTip": "Note that switching the time format will display all times applied to the platform", "24Hour": "24-hours", "12Hour": "12-hours", "1zn8t2V58hA4zxG_Y7jeu": "founder", "s4Towo3SLRRMYC574wkLg": "Fleet user", "RDM65k10RlfRQk4fHEbjd": "After turning it on, enter the alert threshold. When the external voltage is lower than the threshold, the platform triggers a low voltage alert.", "fCvirGBsZoJLXRJHHHZbd": "Enter a positive integer value\n", "RqtauiLCXvZ1erD4k81hD": "Input can not be empty\n", "取消成功": "Cancellation successful", "确定删除设备视频": "Are you sure to delete the device video?", "视频时间段": "Video time period", "创建人": "Creator", "下载视频": "Download video", "10分钟提示": "The maximum duration of a video download is 10 minutes", "设备上传视频提示": "Uploading videos on the device will consume SIM data, please operate in detail!", "上传中": "Uploading", "请先查询设备回放列表": "Please check the device playback list first", "取消失败": "Cancellation failed", "查看进度": "View details", "视频存证提示": "The upload task is created successfully. Please go to the \"Video Storage\" of the device to download the video file later!", "时间错误提示": "Wrong Time Span", "仅支持查询仅一个月的数据": "Supports querying data for up to one month", "视频存证tip": "Video storage certificate (internal beta version): The cloud-recorded video will expire 1 month after it is generated. Please download and save it in time!", "选填项": "Optional", "必填项": "Required field", "更多信息": "More information (optional)", "填写": "Fill in", "yGQ6XcdO8Sy64ocojoYZl": "Original audio time", "fsnMITM1wU8xV2PKzLWjZ": "Temp and Humidity Stats", "O0qntBrpdv8l2SC3pG09l": "Temp and Humidity Dynamics", "nz2_Ad5OYtByfOB1QF3Vl": "<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>", "TXWIRMSEWbj4vtHr7DUJv": "Subtag", "ObM5FY6uCuadK3X9CPiCl": "Device Name", "NyEHBemDgp87PEzlnyLVc": "Subtag Name/ID", "WT2DYWziS2YIrcleR2tGT": "Average Temperature", "ETgTh7hRkghCY7YNBf6I8": "Maximum Temperature", "2dy9zO3qN7emQTgGASZpk": "Minimum Temperature", "Uys4ymT9Nt1lR31ln20t_": "Average Humidity", "aM_5tWEss1bMoV08rT5Dn": "Maximum Humidity", "dVDmsVU8O1mAWZAWej8uF": "Minimum Humidity", "KUJ4j-738X9s_wvGrcaRX": "Total Alert Count", "_-k0Jh2WNEm1YsmgIBnmd": "Total Alert Duration", "fbT5E73inb0AGt14hJy4I": "Ultra-High-Temperature Count", "Fgh4tYRD97bN5_Q2AzTWT": "Ultra-High-Temperature Total Duration（min）", "bSqSifScjo2TKle1XmMKS": "Ultra-Low-Temperature Count", "3chnb5MVuzr2-XHeDkCxA": "Ultra-Low-Temperature Total Duration（min）", "uGZs9lFwECt-z6ucB3Gj-": "Ultra-High Temperature", "l6q5r2x6VZq5zNJ91t7hi": "Ultra-Low Temperature", "ufw1tWMtXV3ffNamDz5xU": "<PERSON><PERSON>", "Fo0DGwbxnBAlVn0y2ck32": "<PERSON><PERSON>", "-F6HE4fAdtZPFvdc63MRN": "Overtemperature Type", "QO0rjr3U04jQP_3aNJSJg": "Overtemperature duration (min)", "XghrPNgWiK990yE5fPxJ0": "Overtemperature Start Time", "uFtXUo1GQdbTBo5ngcwx0": "Overtemperature End Time", "lIYpSzkBAnha_7zYd70o4": "Overtemperature Location", "EULZSW-P16RFs_3B-vk2x": "Temperature (℃)", "PBPHCzoG8bgpY_K0XQs2v": "Humidity (%)", "子标签": "Subtag", "关联时间": "Association time", "关联中": "Associated", "子标签ID": "Subtag ID", "子标签名称": "Subtag Name", "取消关联时间": "Disassociation time", "标签关联记录": "Tag associated records", "电量": "Power", "导入修改": "Import Update", "更新标签": "Update label", "批量设置温度报警": "Set temperature alerts in batches", "关联记录": "Associated records", "温度报警提示": "When {0} exceeds the temperature requirement, the alert will be triggered after {1}min.", "取消关联提示": "Please select the data to be unlinked", "清空设备提示": "Do you confirm to clear all associations of the device", "是否确认取消关联": "Confirm to cancel the association", "更新标签成功": "Tag updated successfully", "子标签输入提示": "Enter one subtag ID in one line (no more than 10)", "子标签空提示": "Cannot be empty, please enter the subtag ID", "请勿输入重复值": "No duplicate values", "请输入正确值": "Please enter correct value", "温度报警设置": "Temperature alert setting", "低温报警提示": "Example description: Set low-temperature alert ≤20°C; triggers after 30 minutes if between -123°C to 20°C, exceeding requirement.", "高温报警提示": "Example description: Set high-temperature alert≥20°C triggers after 30 minutes if between 20°C to 127°C, exceeding requirement.", "持续多久后产生报警": "Duration Until <PERSON><PERSON>", "输入数值提示": "Please enter the value between {0}~{1}", "不能大于180分钟": "Cannot be longer than 180 minutes", "94iJTFe_HpSBynofR58Ti": "Trace storage service class", "fsYFvBFucZiu_oWbNvZ22": "Platform feedback class", "油量总览": "Fuel Overview", "偷油次数": "Fuel theft times", "偷油量": "Fuel theft amount", "偷油次数提示": "Turn on the Fuel theft alert at \"Fuel Volume Settings\". After setting the Fuel volume change alert value, the platform will count the number of Fuel theft times.", "偷油量提示": "Turn on the Fuel theft alert at \"Fuel Volume Settings\". After setting the Fuel volume change alert value, the platform will count the number of Fuel theft amount.", "总油耗": "Total Fuel Consumption", "总加油量": "Total Fuel amount", "总偷油量": "Total Fuel theft amount", "偷油总量": "Total Fuel theft amount", "偷油报警": "Fuel theft alert", "油量报警值提示": "After turning it on, set the alert value for when the fuel drops suddenly. When the fuel reduction rate > the alert value, the platform will trigger the Fuel theft alert every 1 minute.", "总量": "Total", "加油": "Fuel up", "偷油": "Fuel theft", "油量骤减": "Fuel theft amount", "总行驶里程": "Total mileage", "温湿度报警详情": "Temperature and humidity alarm details", "高温报警值需大于低温报警值": "The high temperature alert value needs to be greater than the low temperature alert value", "更新标签超时": "Update label timed out, please try again", "最大的范围不能超过三个月": "The maximum range cannot exceed three months", "指令下发失败": "Failed to issue the command", "标签导入修改成功": "Tag import and modification successful", "标签导入修改": "Tag import modification failed, please try again", "子标签取消关联成功": "Subtag unlinked successfully", "子标签取消关联失败": "Subtag unassociation failed, please try again", "标签更新成功": "Label updated successfully", "油量明细": "Oil quantity details", "加油明细": "Fuel level event", "油量事件": "Fuel level event", "标签更新失败": "Please refresh the page or click \"Update Label\" to get the data", "取消并删除": "Do you confirm to cancel and delete this task", "确认删除": "Do you want to confirm deletion of this task", "任务添加完成": "Task added completed", "批量下载任务": "Batch download tasks", "任务名称": "Task name", "请前往任务列表查看": "Please go to the task list to view", "取消并删除tip": "Cancel and delete successfully", "取消删除": "Cancel and delete", "执行时间": "Execution time", "音频包数量": "Number of audio packages", "任务ID": "Task ID", "子标签温度报警": "Subtag temperature alert", "XlMy7U3zd-o82bjczCtb3": "High-Temperature", "2yW2Lw-Pm4B3CHnUv6_RI": "Low-Temperature", "数量已超过15条": "The number has exceeded 15", "标签更新超时": "Please refresh the page or click \"Update Label\" to get the data", "请输入任务名称": "Please enter a task name", "名称不能超过20个字符": "The name cannot exceed 20 characters", "音频": "Audio", "取消选中": "Uncheck", "是否确认取消选中": "Confirm to uncheck？", "已选中": "Selected", "ACC开启": "ACC on", "ACC关闭": "ACC off", "导出任务进行中": "Export task in progress", "请切换至任务列表下载文件": "Please switch to the task list to download the file", "网页过期": "The web link will expire after {0}。", "统计时间": "Statistics time", "ExtensionTip": "According to different application scenarios, the fields of each report can be combined for export.", "统计": "statistics", "总加油次数": "Total refueling times", "总加油量(L)": "Total refueling volume", "总偷油次数": "Total oil theft times", "总偷油量(L)": "Total oil theft", "油量分析": "Oil analysis", "子音频名称": "Sub audio name", "子音频时间": "Sub audio time", "合包记录": "Audio Merge Log", "KOTAIifGVE4RneCwmKmqS": "When turned on, if the reported oil level of the device is lower than the alarm oil level within 5 minutes, the platform will push a low oil level alarm", "低油报警": "Low oil alarm", "X6lIcAfgOp1sXkdgE_9OW": "Please enter low oil alarm value", "62qJdcSuv6iGqUcKFNdY3": "The alert value must be an integer within 1-10000", "低油报警值不可大于满箱油值": "The low oil alarm value cannot be greater than the full tank oil value", "套餐版本": "Package Version", "nhLyAGMQLMPMAnlog-vFL": "Autonavi", "以下设备不存在": "The following devices do not exist and cannot be added", "新增设备": "Add <PERSON>", "批量解绑": "<PERSON><PERSON>", "是否解除绑定": "Whether to unbind", "默认": "<PERSON><PERSON><PERSON>", "3qycxyVXHpDIRbCj9IK8c": "effective time", "uQX-Oo4jyWWmFhVUIFDZa": "Save and publish", "9HvYA8H1k0s5oYPTo_TRz": "Announcement content cannot be empty", "6mWY-LWd-3OLeGOmBOlvR": "please select language", "EcwH1eTZ4h47qTr03bT2-": "Please enter a title", "M27dQ-Dpq2WkJc3ntw1gg": "Are you sure to delete the announcement", "Xv7QA7Z7u7lCYx4f7NmWs": "Please select the notification platform", "瞬时速度": "Instantaneous speed", "机油压力计算": "Oil pressure measurement", "节气门位置": "Throttle position", "瞬时油耗": "Instantaneous fuel consumption", "空气流量": "Air flow", "发动机实时负载": "Real time engine load", "油量tab提示": "The platform performs routine daily calibration of data, providing oil volume data that includes both \"Calibrated\" and \"Uncalibrated\" results.\n\n\n\n", "油量tab提示Remark": "Please note: There may be minor discrepancies in data before and after calibration. It is recommended to analyze the data in conjunction with actual operating conditions for the most accurate insights.", "油箱": "Tank", "算法校准": "Calibrate Algorithm", "关": "Close", "gQI5MVNOUbZlMcC0kHzKK": "Binding status", "iVMYDWp97LRmLGrbbnnsD": "Bound", "Oeez9vYceazcpBOlH3Xq-": "Unbound", "设备名称不能为空": "Device name cannot be empty", "油耗": "Oil consumption", "监控已结束": "Monitoring has ended, click refresh to continue playing monitoring", "唤醒失败": "Wake-up failed, please try again later", "限速": "Speed limit", "距离": "Distance", "speedAlarmTip": "When the vehicle speed exceeds the set threshold, the system will trigger the speeding alarm and repeat the reminder every 1 minute. The alarm will continue until the vehicle speed falls below the threshold and then automatically clears.", "speedAlarmToast": "Only supports querying data from yesterday and before", "durationTip": "The duration is positive", "Vikgp6W99LW5DLi9_vpHo": "More Actions", "fgRSP3L6Ua8B8bVBYLsMS": "Enter Fullscreen", "riskLevel": "Risk Level", "deviceTotal": "Total Devices", "deviceOnline": "On-Video Devices", "riskEquipment": "At-Risk", "highRiskEvents": "High Risk Today", "mediumRiskEvents": "Medium Risk Today", "lowRiskEvents": "Low Risk Today", "bigFullScreen": "Dashboard", "notProcessed": "Pending", "processed": "In Progress", "resolved": "Resolved", "lowRisk": "Low Risk", "mediumRisk": "Medium Risk", "highRisk": "High Risk", "batchProcessing": "Bulk Actions", "handleThing": "Please select the data you want to process", "handleContent": "Bulk content", "handleType": "Action", "handleResult": "Result", "handleOk": "Batch processing successful", "phoneNotify": "Phone Notification", "wxNotify": "WeChat Notifications", "positives": "False Report", "interaction": "Devices", "processingContent": "Content", "exportData": "Export", "exportError": "Export failed", "exportSuccess": "Evidence export is in progress. Please download it in the event task", "EventTaskTip": "Event Task: After exporting evidence in Event Center - Event Details, you can export the task here. The task will expire after 7 days. Please download it in time or export it again.", "terminalContentTip": "Enter device reading content (max 200 characters)", "terminalContent": "Play Content", "terminalPlay": "Playback", "processImmediately": "Process immediately", "unresolved": "Unresolved", "unresolvedTip": "The event has been resolved and no further action can be taken", "highRiskTip": "May directly threaten life safety, need immediate attention", "mediumRiskTip": "There is a foreseeable danger, need to respond quickly", "lowRiskTip": "Potential risk warning, can be prevented in advance", "realMonitoring": "Real-time monitoring", "playContentSuccess": "The broadcast content was sent successfully", "playContentError": "The broadcast content failed to be sent, please send it again", "_uJmjZE1r8GDjxEZyeh-U": "Exit Fullscreen", "yokKefCUCFgzYsRo9BnzS": "Clear All Monitors", "a64oSYjNagLD0Gg88CxK3": "Stop All Monitoring", "AG-MTfxbowF8XTdMK8yYK": "Start All Monitoring", "x0jvg0WV_R32p1KMHyujw": "Active Selection", "hJ_bvQO9HYG8ge-S_IDO-": "No Operational Channels in Split-Screen", "8_hhY_1Tx0o0w7Y1wdCRb": "9-channel limit reached ,no more can be added", "q9r2Om1U7SO896v3LSVI0": "Stop Monitoring", "3JCIEcBT9HpfXFK0Fu_TN": "Clear Monitoring", "bathHandleTip": "The maximum supported input is 200 characters. If the input exceeds 200 characters, you cannot continue to enter.", "事件弹窗配置": "Event pop-up configuration", "开启后Tip": "After it is turned on, a pop-up window will appear when a check event occurs.", "事件声音配置": "Event sound configuration", "开启声音后Tip": "After it is turned on, there will be a sound reminder when a check event occurs", "事件任务Tip": "Exporting evidence. Download in [Video Tasks - Event Tasks]", "9FY6SGcGYrCwFEcACJ5BV": "Daily price", "m8MBU7gaNdNDQorXi7_92": "Supplementary description", "FIgAdMCZbTjUXRp-BVY-9": "Please set all the entries marked with * first", "A-DnuXZN2arqL1ZwkHLQ5": "Conflict prompt", "bjf4Pxo6-3enYjZX-8tyQ": "The maximum number of pop-up advertisements that can be simultaneously published for the target advertising space is 1", "ShF6ZmpbA1EijFMRApFIw": "There is a time conflict between the current advertisement and the one that has been published. Please modify it before Posting", "TzYCTuYEh0mlda6hmKngc": "Got it.", "_5ENuHSGjo9AOnh9VZMSm": "New advertisement", "D2vixOeMA0xifby0Pqek1": "Advertising user number:", "ck-6pjY9Q2p9eZYMfj3gz": "<PERSON><PERSON> the advertisement", "sxNR6X6-YPM-rM3UYnGC_": "Advertisement Name:", "fSSnMr-IMjus9Nm2qaloa": "Target users:", "7GGCLgtrmSuZ3yX7QKlUB": "Advertising location:", "Zw7bEnWCItRO3OZR4f41u": "Associated device:", "s8ggwKHE3jM4A355doFBv": "Advertising cycle:", "mziSJbA1dzcUsPsiiLLBE": "Advertisement type:", "Q_XGjoL48fZZVJzBlyXW7": "Advertisement picture", "levGypCyH-rXOx1mmg65m": "Limit ratio:", "ND62G7W-ITK8yn7gYxUyU": "Size limit: 2Mb Format limit: jpg/png", "eVbyEIX4agI-jM3BK9Ief": "Please enter the URL", "vT48eh78S-pz3WROKQ9tn": "Please enter the name of the advertisement", "XclqZtcCm0lDPV1UqYj3i": "The length cannot exceed 20 characters", "wZpmnbEoDIWkbpBtxW5gU": "Please select the advertising position", "d64gIF26q7WcJy1oOOKkL": "Please select the advertising period", "lvS33ZKf9iiNf7JKvRJHO": "Please select the type of advertisement", "Ae9AKKIZR7Z7EcvHe76aW": "Please upload the advertisement pictures", "zLoxHFKf86oWk8m9HKhm_": "Failed to obtain the device list", "e7akjZcWeEbSamhM97F5v": "Please upload png or jpg images", "wxzOyEnLiHt1bTxTUpCHM": "Please upload pictures of no more than 2Mb", "BWhWUz0AlHOT-S0V0wK4i": "The number of advertising users is 0 and cannot be saved and published", "8cuHAuf6V9y1S0ktrujuE": "Successful release", "IobVQNnwBhgHS5pOxH4Vw": "Saved and published successfully", "S2DqHrLgq8j-ObZQL40qH": "Save and publish failed", "MJzt0chtyypPJrJM2etk4": "Save failed", "GJ8mxuRz8wIpmKeftNX8V": "Failed to obtain the advertisement details", "dGvQSz_VHN0aEluf-2SYm": "Please enter the correct URL format", "Bkq0Yczk7b865d7nN8M8W": "Advertisement Name:", "k9HNUSJDi33dfFV3JFI0F": "Loading...", "vt1MZfWXyMZzyFU7ZD6kc": "There are no pictures for the moment.", "8Y9n6AMUzmJgLppRkiPqC": "Back off", "ybYXmIEwb2gFT3jw0NhiZ": "Offline", "dRz9guMYbTUs-t4KKVMoI": "Whether to delete this advertisement", "FPiSGK9vmdXLttuGBLCNy": "Failed to obtain the advertisement list", "-BryKeYUPdPBoc7NnBDjn": "The advertisement ID is invalid", "QMXVAEUa1l0LSMMq1P-GE": "Whether to publish this advertisement", "9qwy42KyBhnFOtUfCP4L5": "Publication failed", "p8YaGbXkOnRHfH73h6uwL": "Confirm the rollback of the current advertisement?", "WUHni97FVPSbV261NuNyM": "The rollback was successful.", "52qKTZxNp_787WjWjlGIw": "Rollback failed", "Xflr8gmyOVSwPDsoDtigU": "Confirm the offline of the current advertisement?", "46L3Sa2n3_HRwD4n9EMdB": "Offline successfully", "ta6lpPTi4NhVLI6x0xtqy": "Failed to log off", "4XmNWhQoR-eEcZdOzqJR9": "The sorted value must be a number between 1 and 99", "jM1EyGoVHeSsOrb84SzPr": "The sorting has been saved.", "QR-j-6cQyMmEYZUipjhag": "Sorting and saving failed.", "1jUjxmqgvba50alYQ9LBI": "Advertising ID", "qKT-47crN5dDtrd4hPLo_": "Advertisement name", "OD9HmFY35w2-KGjqj9mSV": "Advertising position", "zlQECz3Ehrie3BAaW7pLa": "Advertising type", "6IeAplUy8IVQGWPIsK9pY": "Advertising cycle", "fVGJhvEZ-ZDSDhA0XchW_": "Advertisement picture", "rM1Ai797icksN5452hvqh": "Sorting", "ZIlhD_KbnyMZiGVadFupW": "Update person", "D7J2uzbGR9NdUHMzsYJET": "Draft", "uURJR8YrvL3Ha-nUf-EOH": "To Be Published", "u4qG7qA0CreU6kKaHGVjc": "Online", "sd5fGztwwaG_oY68aoZgd": "Offline", "lc73xHWPc169AIGWpQWED": "All users", "PV07lSEjTue8rcXVfQvZW": "All mobile phone users", "HYB2ZWBIbSoGGHFRq_rlo": "All equipment users", "p0LuUDlOJamobXWFBmvwq": "Home Page", "_0ruF2IV_hA9Zn4miCbgz": "Equipment Details page", "B3sJW7aZoEUiyVh30YerS": "In the recent week", "ZoUjvBsJRkXXrvCva1pLY": "In the recent month", "-tjmITfzU3XeMoxOA-2N7": "The recent three months", "JrWkSlLps5yku5MOyYKnU": "English", "_YIgMRgV6UMh4EHG2MJaE": "Traditional Chinese", "LNy00b-btNH2e2vSaDazn": "Spanish", "l5_XZj_k7myJXWJsiNRJh": "Portuguese", "kHWK3fJ2TvdGRZXQrIcgC": "Russian", "616Gu11GhAUHLm6tzMB06": "German", "TJ-4o27rDqFCGOPlOzqqa": "French", "BNtah_dKQVXGzDPA2no0n": "Pop-up window", "e0alpg_MbQSLh3XDGIzmi": "Banner", "hkY4dtYbrsma9O2lxwpp0": "The recent 7 days", "aXr65zaaIK8D8KzkjjUMx": "Please select the correct type of advertisement", "ipe0_s69Mr1o3M0-Vh_YQ": "The picture reading failed.", "v_1dniF62eoQTZy9tRtjP": "File reading failed.", "CBX9YAaJlHGAGvxJjAX20": "No equipment", "cxagxiJXpUhAZ_HjQvqTX": "Operation failure", "1eoXePBAdeMp4mp4IXK97": "Publish advertisements", "d0nknHU7xudZOANlToBfT": "Offline advertisement", "F997Le_b3iMzyjviH1w_b": "Refund advertisement", "4SdxzgE6mEOHAhf-kAa1i": "Advertisement Details", "S8KzvULqkjLUEUDggUP7O": "Basic information", "sWZVMsKpDLm7Zo_TUC1zI": "Data analysis", "1ozcMqmPXf7ML4ahZPcz-": "Are you sure to delete this advertisement?", "ehydDkOY8K6pbQMdrYdvw": "Have you confirmed the release of this advertisement?", "TFWMEXmNyu3JWAhFrUI5c": "Have you confirmed the removal of this advertisement?", "9-KD5m4BVoNYl4cRLd00f": "Are you sure to roll the advertisement back to the draft state?", "tJI29weqgBdl7X8Q22xXm": "Sorry, the current advertisement has not been launched yet", "eRqgRC5edGZp2h9L4B0Wx": "Statistical cut-off date: {0}", "X0_QBixRCOYRGZyOIlRuw": "Cumulative page views of the advertisement", "5n4RaESXtvz5GiDax_JZt": "Cumulative number of visitors to the advertisement", "dwAD15TZTKz8D9Aeii6hE": "Failed to obtain data", "EmaS6irOhwdMjUdKb7kpt": "Advertisement status:", "oFuc9t_2oQw5HruLy8wPn": "Advertising ID", "Faq4yaX0d6aAiKJuLoqDy": "Equipment model:", "z1M9X6X4Jc_lcHUZPY2yo": "Sorting:", "Mxt4sLL2QJGL5YrRL4TOQ": "Updater:", "EiJhbLu2h-Rx5MX_YXwHW": "Update time:", "Qq5z7_IdzCpASYspXX9xc": "Visitor number trend chart", "fQKeYjglc9g0-0oWVktBZ": "Unit: Person", "alO9uZXdsebWQhFqe3vS2": "Number of advertising visitors", "n7TbeWPx6ERiuCLdi1xMo": "Page view trend chart", "UiI3Qz1DnMzuUOsVXVsFz": "Unit: Times", "1ykDVrd_FE2ioXA_6at7N": "Advertising page views", "atBMWwI4wBA1fjp9rDMUL": "Advertising ID", "bmp6Q586E8NHP4TOjI9wy": "Target users:", "0PRcq7VkoU6VCeXMG6lv_": "Advertisement date range:", "pTu4TJ_3imdKY9kGkrkN4": "Advertising location:", "OgP1t0z_sac6t8wWYfsip": "Advertisement status:", "uO5Hxlkc71BV8-Ipub1Lv": "Please select the advertisement status", "8KO149E4QUo096F392XNf": "The maximum number of Banner ads that can be simultaneously published in the target advertising space is 3", "Lwkp0P2tS_8L9TCqgGY9H": "The selected time range can only be less than one month. Please reselect", "LCpYvqukl9DTtyOJRGv5v": "You haven't signed the contract yet and thus cannot withdraw the money. Please contact your superior service provider to complete the contract signing.", "xhfKJiat7T-bprrBbtlKc": "The target number of users for release is 0 and it cannot be published", "lmpTTGK79olDncBLWwHkE": "Contract Number", "8QcKpYI9MVF-Y0PFV5nfI": "Please fill in the contract number", "xCxLzzOADKKHqh3RXgLz_": "Only letters and numbers are supported", "NFsODE3FCSNwfl1kJhx4L": "Service provider type", "Kl8KHimuoV06FBms4UjzY": "Please select the service provider type", "djmIMlcXzVVOAmDmvw_vQ": "Ecology", "hndIUBCh21ExxiafeHaPI": "Self-operated", "LW0_DuWras1S6OvP-zQvd": "Name", "CScSIqDVa1M_YVWDKHimX": "Service provider type:", "PZgd7E_28uEnsrKeQCiPQ": "Number of devices", "JHsxhKvpTpi1f14chgJ1w": "First purchase", "5xMgAQOeDE6KWxd0K3bYl": "Device subscription", "PM9VYxX3Pi36qPhErNb9m": "Protection by relatives and friends", "NquTuvpdB1KBOo7X28kZM": "Please upload the picture with a ratio of 9:16", "bkT9w9uSvQJwn7GOyfxek": "Please upload the picture with a ratio of 343 to 60", "K-bOltnmVObygLynJo1Zl": "In the past half year", "package_type_manage": "Package type management", "scene_manage": "Scene management", "baohantaocan_89eb25": "Included package types: vehicle safety, pet supervision, item loss prevention, and all package types under home monitoring", "baohantaocan_7675b9": "Included packages: All package types in the scenario of being protected by relatives and friends", "suoyoubaohan_155ccc": "All packages that include platform service activation classes", "qingquerenfu_fc4bcc": "Please confirm whether the service provider's profit-sharing ratio, amount display and export table functions are enabled or disabled according to your needs.", "fenzhangfuwu_06c3b5": "Account of the revenue-sharing service provider '+ ':", "zhichu-tuikuan": "Expenditure - Refund", "duanxinxiaohao": "SMS consumption", "jinexianshi": "Amount display", "caiwuguanli_d21d0c": "The total account amount, available total amount, frozen amount and withdrawal on all pages of financial management; Income and expenditure details - Balance Detailed account splitting list - Income and expenditure amount, frozen amount, received amount; Withdrawal details - Total withdrawal amount Service provider Management - Cumulative account allocation and account balance", "daochubiaoge": "Export the table", "caiwuguanli_833465": "The export function for all pages of financial management", "hetongluru": "Contract Entry", "hetongluru_0d3219": "Contract entry refers to entering the contract number. After clicking \"Confirm and submit\", the service provider can normally initiate withdrawals.", "luru": "Input", "tuikuanguanli": "Refund management"}