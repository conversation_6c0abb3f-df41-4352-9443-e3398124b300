import { i18n } from '@/i18n'
/**
 * 广告相关常量配置
 */
// 允许的图片类型
export const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/jpg']

// 图片最大尺寸（MB）
export const MAX_IMAGE_SIZE_MB = 2

// 广告图片比例常量
export const RATIO_9_16 = 9 / 16 // 弹窗广告比例 9:16
export const RATIO_343_60 = 343 / 60 // Banner广告比例 343:60

// 接口前缀
export const API_PREFIX = process.env.NODE_ENV === 'development' ? '/api' : ''

// 时间选择器的快捷选项配置
export const AD_PICKER_OPTIONS = {
  shortcuts: [
    {
      text: i18n.t('XbMQZa2UhMAnIgRdfYmBE'),
      onClick(picker) {
        const now = new Date()
        const start = new Date()
        start.setHours(0, 0, 0, 0)
        // 如果当前时间已经过了今天的开始时间，则使用当前时间作为开始时间
        if (start.getTime() < now.getTime()) {
          start.setTime(now.getTime())
        }
        const end = new Date()
        end.setHours(23, 59, 59, 999)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: i18n.t('trhhfwertdgdfgqWERs45'),
      onClick(picker) {
        const now = new Date()
        const day = now.getDay() || 7
        const start = new Date(now)
        start.setDate(now.getDate() - day + 1)
        start.setHours(0, 0, 0, 0)
        // 如果本周开始时间早于当前时间，则使用当前时间作为开始时间
        if (start.getTime() < now.getTime()) {
          start.setTime(now.getTime())
        }
        const end = new Date(now)
        end.setDate(now.getDate() + (7 - day))
        end.setHours(23, 59, 59, 999)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: i18n.t('YI-CDEbvHMaK4Qo-P0C54'),
      onClick(picker) {
        const now = new Date()
        const start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0)
        // 如果本月开始时间早于当前时间，则使用当前时间作为开始时间
        if (start.getTime() < now.getTime()) {
          start.setTime(now.getTime())
        }
        const end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: i18n.t('hkY4dtYbrsma9O2lxwpp0'),
      onClick(picker) {
        const now = new Date()
        const start = new Date(now.getTime()) // 使用当前时间作为开始时间
        const end = new Date()
        end.setDate(now.getDate() + 6) // 从当前时间开始往后推7天
        end.setHours(23, 59, 59, 999)
        picker.$emit('pick', [start, end])
      }
    }
  ],
  disabledDate(time) {
    // 禁用当前时间之前的所有日期
    return time.getTime() < Date.now()
  }
}

// 广告类型
export const AD_TYPE_OPTIONS = [
  { label: i18n.t('BNtah_dKQVXGzDPA2no0n'), value: 'POPUP' },
  { label: i18n.t('e0alpg_MbQSLh3XDGIzmi'), value: 'BANNER' }
]

// 广告操作类型
export const AD_OPERATION_TYPES = {
  SAVE: 'SAVE', // 保存（草稿）
  PUBLISH: 'SAVE_AND_PUBLISH', // 发布,
  ROLLBACK: 'ROLLBACK', // 回滚
  OFFLINE: 'OFFLINE', // 下线,
  SORT: 'SORT' // 排序
}
