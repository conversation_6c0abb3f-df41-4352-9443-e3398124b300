import BaseMenu from './menu'
import BaseMenuItem from './menu-item'
import BaseSubmenu from './submenu'
import BaseButton from './button'
import BaseContainer from './container'
import BaseDropdownMenu from './dropdown-menu'
import BaseDropdown from './dropdown'
import BaseDropdownItem from './dropdown-item'
import BaseDatePicker from './date-picker'
import BaseSelect from './select'
import BaseDialog from './dialog'
import BaseAlert from './alert'
import BasePagination from './pagination'
import BasePopover from './popover'

// 提示
import { baseMessage } from './message'
import { baseNotification } from './notification'

const elements = [
  BaseMenu,
  BaseMenuItem,
  BaseSubmenu,
  BaseButton,
  BaseContainer,
  BaseDropdownMenu,
  BaseDropdown,
  BaseDropdownItem,
  BaseDatePicker,
  BaseSelect,
  BaseDialog,
  BaseAlert,
  BasePagination,
  BasePopover,
  baseMessage,
  baseNotification
]

const BaseUi = {}

BaseUi.install = function(Vue) {
  for (const element of elements) {
    Vue.use(element)
  }
}

export default BaseUi
