<template>
  <div class="service-provider-settins">
    <el-form ref="form" :model="form" label-width="auto">
      <el-form-item>
        <template #label>
          <span class="ljdw-mr-4">{{ $t('jinexianshi') }}</span>
          <el-tooltip placement="top" effect="dark">
            <div slot="content" style="width: 400px">{{ $t('caiwuguanli_d21d0c') }}</div>
            <PcFinanceTip size="12" />
          </el-tooltip>
        </template>
        <el-switch :active-value="1" :inactive-value="0" v-model="form.amountShowSwitch" @change="amountShowSwitchChange"></el-switch>
      </el-form-item>
      <el-form-item>
        <span slot="label">
          <span class="ljdw-mr-4">{{ $t('daochubiaoge') }}</span>
          <el-tooltip placement="top">
            <div slot="content">{{ $t('caiwuguanli_833465') }}</div>
            <PcFinanceTip size="12" /> </el-tooltip
        ></span>
        <el-switch :active-value="1" :inactive-value="0" :disabled="exportSwitchDisabled" v-model="form.exportSwitch"></el-switch>
      </el-form-item>
      <el-form-item v-if="isHasPermissions('contract:entry')">
        <span slot="label">
          <span class="ljdw-mr-4">{{ $t('hetongluru') }}</span>
          <el-tooltip placement="top">
            <div slot="content">{{ $t('hetongluru_0d3219') }}</div>
            <PcFinanceTip size="12" /> </el-tooltip
        ></span>
        <el-button v-if="!contractNumber" type="text" @click="handleEntryContract">{{ $t('luru') }}</el-button>
        <span v-else>{{ contractNumber }} <el-button type="text" icon="el-icon-edit" @click="handleEntryContract()"/></span>
      </el-form-item>
    </el-form>
    <AddContractDialog ref="addContractDialogRef" :visible.sync="addContractDialogVisible" :value="contractNumber" @save="handleSaveContract" />
  </div>
</template>

<script>
import { PcFinanceTip } from '@/assets/icon'
import AddContractDialog from './AddContractDialog.vue'
import permissionMixin from '@/mixins/permission'
export default {
  mixins: [permissionMixin],
  components: { PcFinanceTip, AddContractDialog },
  props: {
    editRecord: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      form: {
        amountShowSwitch: 0,
        exportSwitch: 0
      },
      contractNumber: '',
      addContractDialogVisible: false,
      isEdit: false
    }
  },
  computed: {
    exportSwitchDisabled() {
      //开启导出表格功能必须先开启金额显示
      return this.form.amountShowSwitch === 0
    }
  },
  watch: {
    editRecord: {
      handler(val) {
        if (val) {
          this.form = { amountShowSwitch: val.amountShowSwitch, exportSwitch: val.exportSwitch }
          this.contractNumber = val.contractNumber
        }
      },
      immediate: true
    }
  },
  methods: {
    getFormData() {
      return this.form
    },
    amountShowSwitchChange(e) {
      if (!e) {
        this.form.exportSwitch = 0
      }
    },
    handleEntryContract() {
      this.addContractDialogVisible = true
    },
    handleSaveContract(contractNumber) {
      this.contractNumber = contractNumber
    },
    getContractNumber() {
      if (this.contractNumber) {
        return this.contractNumber
      }
      return ''
    }
  }
}
</script>

<style lang="scss" scoped>
.service-provider-settins {
  // margin-top: 15px;
  ::v-deep .el-form {
    .el-form-item {
      margin-bottom: 5px;
    }
  }
}
</style>
