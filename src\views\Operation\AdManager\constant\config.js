import { i18n } from '@/i18n'
// 表格配置
export const dataConfig = [
  {
    label: '1jUjxmqgvba50alYQ9LBI', //广告Id
    key: 'adNo',
    prop: 'adNo'
  },
  {
    label: 'qKT-47crN5dDtrd4hPLo_', //广告名称
    key: 'name',
    prop: 'name'
  },
  {
    label: 'Hi_cEuIxsZ92-zskwssT7', //目标客户
    key: 'targetUserType',
    prop: 'targetUserType'
  },
  {
    label: 'OD9HmFY35w2-KGjqj9mSV', //广告位置
    key: 'adPosition',
    prop: 'adPosition'
  },
  {
    label: 'zlQECz3Ehrie3BAaW7pLa', //广告类型
    key: 'adType',
    prop: 'adType'
  },
  {
    label: '6IeAplUy8IVQGWPIsK9pY', //广告周期
    key: 'adPeriod',
    prop: 'adPeriod',
    configs: {
      width: '170px'
    }
  },
  {
    label: 'fVGJhvEZ-ZDSDhA0XchW_', //广告图片
    key: 'adImg',
    prop: 'adImg'
  },
  {
    label: 'rM1Ai797icksN5452hvqh', //排序
    key: 'sort',
    prop: 'sort',
    sortable: 'custom'
  },
  {
    label: 'lg.status', //状态
    key: 'status',
    prop: 'status'
  },
  {
    label: 'ZIlhD_KbnyMZiGVadFupW', //更新人
    key: 'operator',
    prop: 'operator'
  },
  {
    label: 'zdVunWKu5AhodKvqgP4rI', //更新时间
    key: 'updateTime',
    prop: 'updateTime'
  },
  {
    label: 'lg.operator', //操作
    key: 'operatorBtn',
    prop: 'operatorBtn',
    configs: {
      width: '180px'
    }
  }
]

// 状态映射
export const statusMap = {
  DRAFT: i18n.t('D7J2uzbGR9NdUHMzsYJET'),
  NOT_ONLINE: i18n.t('uURJR8YrvL3Ha-nUf-EOH'),
  ONLINE: i18n.t('u4qG7qA0CreU6kKaHGVjc'),
  OFFLINE: i18n.t('sd5fGztwwaG_oY68aoZgd'),
  EXPIRED: i18n.t('Jet1u7xx2NupyskORg_e-')
}

// 目标用户选项
export const targetUserOptions = [
  { label: i18n.t('lc73xHWPc169AIGWpQWED'), value: 'ALL_USERS' },
  { label: i18n.t('PV07lSEjTue8rcXVfQvZW'), value: 'ALL_PHONE_USERS' },
  { label: i18n.t('HYB2ZWBIbSoGGHFRq_rlo'), value: 'ALL_DEVICE_USERS' }
]

// 广告位置选项
export const adPositionOptions = [
  { label: i18n.t('p0LuUDlOJamobXWFBmvwq'), value: 'HOME_PAGE' },
  { label: i18n.t('_0ruF2IV_hA9Zn4miCbgz'), value: 'DEVICE_DETAIL_PAGE' }
]

// 广告状态选项
export const statusOptions = [
  { label: i18n.t('dashloadi-NdxYrTzYiTv'), value: '' },
  { label: i18n.t('D7J2uzbGR9NdUHMzsYJET'), value: 'DRAFT' },
  { label: i18n.t('uURJR8YrvL3Ha-nUf-EOH'), value: 'NOT_ONLINE' },
  { label: i18n.t('u4qG7qA0CreU6kKaHGVjc'), value: 'ONLINE' },
  { label: i18n.t('sd5fGztwwaG_oY68aoZgd'), value: 'OFFLINE' }
]

// 日期选择器配置
export const pickerOptions = {
  shortcuts: [
    {
      text: i18n.t('B3sJW7aZoEUiyVh30YerS'),
      onClick(picker) {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: i18n.t('ZoUjvBsJRkXXrvCva1pLY'),
      onClick(picker) {
        const end = new Date()
        const start = new Date()
        start.setMonth(start.getMonth() - 1)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: i18n.t('-tjmITfzU3XeMoxOA-2N7'),
      onClick(picker) {
        const end = new Date()
        const start = new Date()
        start.setMonth(start.getMonth() - 3)
        picker.$emit('pick', [start, end])
      }
    }
  ]
}

// 初始化搜索参数
export const getInitialSearchParams = () => {
  // 计算一个月前的日期,只要日期，不要时间
  // const endDate = new Date().toISOString().split('T')[0]
  // const startDate = new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().split('T')[0]
  return {
    adNo: '',
    adPosition: '',
    commonSearch: '',
    endTime: '',
    id: '',
    lang: '',
    name: '',
    offset: 0,
    pageIndex: 1,
    pageSize: 30,
    startTime: '',
    status: '',
    targetUserType: '',
    userId: '',
    userType: '',
    dateRange: '',
    sortAsc: true // 排序方式,默认升序
  }
}

// 分页配置
export const defaultPaginationConfig = {
  pageIndex: 1,
  pageSize: 10,
  total: 0
}
export const LANGUAGE_OPTIONS = [
  { label: i18n.t('JrWkSlLps5yku5MOyYKnU'), value: 'en' },
  { label: i18n.t('EvTOrBlsYbI6MLRmnjEby'), value: 'cn' },
  { label: i18n.t('_YIgMRgV6UMh4EHG2MJaE'), value: 'tw' },
  { label: i18n.t('LNy00b-btNH2e2vSaDazn'), value: 'es' },
  { label: i18n.t('l5_XZj_k7myJXWJsiNRJh'), value: 'pt' },
  { label: i18n.t('kHWK3fJ2TvdGRZXQrIcgC'), value: 'ru' },
  { label: i18n.t('616Gu11GhAUHLm6tzMB06'), value: 'de' },
  { label: i18n.t('TJ-4o27rDqFCGOPlOzqqa'), value: 'fr' }
]
export const adTypeOptions = [
  { label: i18n.t('BNtah_dKQVXGzDPA2no0n'), value: 'POPUP' },
  { label: i18n.t('e0alpg_MbQSLh3XDGIzmi'), value: 'BANNER' }
]
