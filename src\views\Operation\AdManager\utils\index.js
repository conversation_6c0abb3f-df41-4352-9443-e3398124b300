/**
 * 图片上传和验证工具函数
 */

import { ALLOWED_IMAGE_TYPES, MAX_IMAGE_SIZE_MB, RATIO_9_16, RATIO_343_60, AD_OPERATION_TYPES } from '@/views/Operation/AdManager/constant/adConstants'
import { _updateAdStatus } from '@/api/ads'
import { i18n } from '@/i18n'
/**
 * 校验图片文件类型
 * @param {File} file - 上传的文件对象
 * @returns {boolean} - 类型是否合法
 */
export const checkFileType = file => ALLOWED_IMAGE_TYPES.includes(file.raw.type)

/**
 * 校验图片文件大小
 * @param {File} file - 上传的文件对象
 * @returns {boolean} - 大小是否合法
 */
export const checkFileSize = file => file.size <= MAX_IMAGE_SIZE_MB * 1024 * 1024

/**
 * 校验图片比例
 * @param {File} file - 上传的文件对象
 * @param {string} adType - 广告类型（'POPUP'或'BANNER'）
 * @returns {Promise} - 解析为校验结果
 */
export const checkImageRatio = (file, adType) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = e => {
      const img = new Image()
      img.onload = () => {
        const width = img.width
        const height = img.height
        const actualRatio = width / height
        const TOLERANCE = 0.05 // 容差，防止Js计算精度问题出现的误差

        if (adType === 'POPUP') {
          if (Math.abs(actualRatio - RATIO_9_16) < TOLERANCE) return resolve(true)
          return reject(i18n.t('NquTuvpdB1KBOo7X28kZM'))
        }

        if (adType === 'BANNER') {
          if (Math.abs(actualRatio - RATIO_343_60) < TOLERANCE) return resolve(true)
          return reject(i18n.t('bkT9w9uSvQJwn7GOyfxek'))
        }

        // 其他类型一律不通过
        return reject(i18n.t('aXr65zaaIK8D8KzkjjUMx'))
      }
      img.onerror = () => reject(i18n.t('ipe0_s69Mr1o3M0-Vh_YQ'))
      img.src = e.target.result
    }
    reader.onerror = () => reject(i18n.t('v_1dniF62eoQTZy9tRtjP'))
    reader.readAsDataURL(file.raw)
  })
}

/**
 * 按系列分组处理设备列表
 * @param {Array} devices - 设备列表
 * @returns {Object}  - 分组后的设备选项和设备列表
 */
export const processDeviceList = devices => {
  if (!Array.isArray(devices)) return []
  // 在选项数组前添加两个选项 设备型号 -1代表全部设备 0代表无设备
  devices.unshift(
    {
      machineTypeId: '-1',
      machineTypeName: i18n.t('iqmW63hnN6IZmBcr4Bx_N')
    },
    {
      machineTypeId: '0',
      machineTypeName: i18n.t('CBX9YAaJlHGAGvxJjAX20')
    }
  )
  // 按设备系列分组
  const groupedDevices = {}
  devices.forEach(device => {
    // 提取系列名称，假设系列名称是型号的第一部分
    const seriesMatch = device.machineTypeName.match(/^([A-Z]+)/)
    const series = seriesMatch ? seriesMatch[0] : i18n.t('lg.logDict.other')

    if (!groupedDevices[series]) {
      groupedDevices[series] = []
    }

    groupedDevices[series].push(device)
  })
  const options = Object.keys(groupedDevices).map(series => ({
    label: series,
    options: groupedDevices[series]
  }))
  const deviceList = options.flatMap(item => item.options.map(option => option.machineTypeId)).filter(id => id !== '-1' && id !== '0')
  // 转换为el-select组需要的格式
  return { options, deviceList }
}

/**
 * 格式化数字，保留两位小数
 * @param {number} num - 需要格式化的数字
 * @returns {string} - 格式化后的数字字符串
 */
export const formatNumber = num => {
  return num.toFixed(2)
}

/**
 * 广告详情中获取日期选择器的快捷选项配置
 * @returns {Object} - 日期选择器配置
 */
export const getDatePickerOptions = () => {
  return {
    disabledDate(time) {
      return time.getTime() + 3600 * 1000 * 24 > Date.now()
    },
    shortcuts: [
      {
        text: i18n.t('0gU5oOZxYWY5c8diw5GGt'),
        onClick(picker) {
          const yesterday = new Date()
          yesterday.setTime(yesterday.getTime() - 3600 * 1000 * 24)
          picker.$emit('pick', [yesterday, yesterday])
        }
      },
      {
        text: i18n.t('B3sJW7aZoEUiyVh30YerS'),
        onClick(picker) {
          const end = new Date()
          end.setTime(end.getTime() - 3600 * 1000 * 24)
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
          picker.$emit('pick', [start, end])
        }
      },
      {
        text: i18n.t('ZoUjvBsJRkXXrvCva1pLY'),
        onClick(picker) {
          const end = new Date()
          end.setTime(end.getTime() - 3600 * 1000 * 24)
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
          picker.$emit('pick', [start, end])
        }
      }
    ]
  }
}

/**
 * 获取最近7天的日期范围（不包含今天）
 * @returns {Array} - [开始日期, 结束日期]
 */
export const getLast7DaysRange = (useZeroTime = false) => {
  const now = new Date()
  // 计算昨天日期
  const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 23, 59, 59, 999)
  const end = useZeroTime ? new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59, 999) : new Date(yesterday)
  const start = useZeroTime
    ? new Date(end.getFullYear(), end.getMonth(), end.getDate() - 6, 0, 0, 0, 0)
    : (() => {
        const s = new Date()
        s.setTime(end.getTime() - 3600 * 1000 * 24 * 6)
        return s
      })()
  return [start, end]
}

/**
 * 处理广告状态变更的通用方法
 * @param {Object} params - 参数对象
 * @param {string} params.id - 广告ID
 * @param {string} params.operationType - 操作类型，来自AD_OPERATION_TYPES常量
 * @param {string} params.remark - 操作备注
 * @param {Function} params.onSuccess - 成功回调
 * @param {Function} params.onError - 错误回调
 * @param {Function} params.onConflict - 发生广告冲突时的回调
 * @returns {Promise} - 执行结果
 */
export const handleAdStatusChange = async ({ id, operationType, remark, onSuccess, onError, onConflict }) => {
  if (!id) {
    typeof onError === 'function' && onError(i18n.t('-BryKeYUPdPBoc7NnBDjn'))
    return
  }

  try {
    const res = await _updateAdStatus({ id, operationType, remark })

    // 处理广告冲突情况
    if (operationType === AD_OPERATION_TYPES.PUBLISH && res.code === '50330' && Array.isArray(res.data)) {
      typeof onConflict === 'function' && onConflict(res.data)
      return
    }

    if ((operationType === AD_OPERATION_TYPES.PUBLISH && res.code === '50331') || (res.code && res.code !== '1' && res.ret !== 1)) {
      typeof onError === 'function' && onError(res.msg || i18n.t('cxagxiJXpUhAZ_HjQvqTX'))
      return
    }

    // 处理其他错误情况
    if ((operationType === AD_OPERATION_TYPES.PUBLISH && res.code === '50334') || (res.code && res.code !== '1' && res.ret !== 1)) {
      typeof onError === 'function' && onError(res.msg || i18n.t('cxagxiJXpUhAZ_HjQvqTX'))
      return
    }

    // 成功回调
    if (res.ret === 1 || res.ret === true) {
      typeof onSuccess === 'function' && onSuccess()
    } else {
      typeof onError === 'function' && onError(i18n.t('cxagxiJXpUhAZ_HjQvqTX'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      typeof onError === 'function' && onError(i18n.t('cxagxiJXpUhAZ_HjQvqTX'))
    }
  }
}

/**
 * 发布广告
 * @param {Object} params - 参数对象
 * @param {string} params.id - 广告ID
 * @param {Function} params.onSuccess - 成功回调
 * @param {Function} params.onError - 错误回调
 * @param {Function} params.onConflict - 冲突回调
 * @returns {Promise} - 执行结果
 */
export const publishAd = async ({ id, onSuccess, onError, onConflict }) => {
  return handleAdStatusChange({
    id,
    operationType: AD_OPERATION_TYPES.PUBLISH,
    remark: i18n.t('1eoXePBAdeMp4mp4IXK97'),
    onSuccess,
    onError,
    onConflict
  })
}

/**
 * 下线广告
 * @param {Object} params - 参数对象
 * @param {string} params.id - 广告ID
 * @param {Function} params.onSuccess - 成功回调
 * @param {Function} params.onError - 错误回调
 * @returns {Promise} - 执行结果
 */
export const offlineAd = async ({ id, onSuccess, onError }) => {
  return handleAdStatusChange({
    id,
    operationType: AD_OPERATION_TYPES.OFFLINE,
    remark: i18n.t('d0nknHU7xudZOANlToBfT'),
    onSuccess,
    onError
  })
}

/**
 * 回退广告到草稿状态
 * @param {Object} params - 参数对象
 * @param {string} params.id - 广告ID
 * @param {Function} params.onSuccess - 成功回调
 * @param {Function} params.onError - 错误回调
 * @returns {Promise} - 执行结果
 */
export const rollbackAd = async ({ id, onSuccess, onError }) => {
  return handleAdStatusChange({
    id,
    operationType: AD_OPERATION_TYPES.ROLLBACK,
    remark: i18n.t('F997Le_b3iMzyjviH1w_b'),
    onSuccess,
    onError
  })
}
