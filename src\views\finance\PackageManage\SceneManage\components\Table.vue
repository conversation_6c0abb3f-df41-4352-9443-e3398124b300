<template>
  <DataTable
    class="scene-table"
    :data-list="dataList"
    :pagination-config="paginationConfig"
    :data-config="dataConfig"
    :table-config="{ isNeedNo: true }"
    @paginationChange="handlePaginationChange"
    height="calc(100% - 77px)"
    v-loading="loading"
  >
    <!-- 场景名称 -->
    <template #sceneName="{record}">
      {{ record.sceneName }}
    </template>

    <!-- 关联设备型号 -->
    <template #machineTypeList="{value}">
      <template v-if="value && value.length > 0">
        {{ value.map(item => item.machineTypeName).join('、') }}
      </template>
      <span v-else>-</span>
    </template>

    <!-- 启用状态 -->
    <template #enableStatus="{record}">
      <el-switch v-model="record.enableStatus" @change="val => handleStatusChange(val, record)"></el-switch>
    </template>

    <!-- 排序 -->
    <template #sort="{record}">
      {{ record.sort }}
    </template>

    <!-- 操作 -->
    <template #operation="{record}">
      <el-button type="text" @click="handleEdit(record)">编辑</el-button>
    </template>
  </DataTable>
</template>

<script>
import DataTable from '@/components/Common/DataTable'
import { dataConfig, defaultPaginationConfig } from '../config'
import { _fetchSceneList, _updateSceneStatus } from '@/api/scene'
import { isAbroad } from '@/utils/judge'
export default {
  name: 'SceneTable',
  components: {
    DataTable
  },

  data() {
    return {
      dataList: [],
      dataConfig,
      paginationConfig: { ...defaultPaginationConfig },
      loading: false,
      currentSearchConditions: {} // 当前搜索条件
    }
  },
  computed: {
    defaultLang() {
      return this.getDefaultLang()
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    isAbroad,
    getDefaultLang() {
      return isAbroad() ? 'en' : 'cn'
    },
    // 获取数据
    async fetchData(searchConditions = {}) {
      this.loading = true
      try {
        this.currentSearchConditions = searchConditions
        const { pageIndex, pageSize } = this.paginationConfig
        const params = {
          ...searchConditions,
          pageIndex,
          pageSize
        }

        // 如果有设备类型IDs，转换为后端需要的字符串格式
        if (params.machineTypeIds && Array.isArray(params.machineTypeIds) && params.machineTypeIds.length > 0) {
          params.machineTypeId = params.machineTypeIds.join(',')
          delete params.machineTypeIds
        }

        const response = await _fetchSceneList(params)
        const data = response.data
        this.dataList = (data.records || []).map(record => {
          //listdata中的sceneInternationals是多语言数据，需要根据当前语言获取对应的场景名称
          const sceneInternational = record.sceneInternationals?.find(item => item.lang === this.defaultLang) || record.sceneInternationals?.[0]
          return {
            ...record,
            sceneName: sceneInternational?.content || '--'
          }
        })
        this.paginationConfig.total = data.total || (data.pages && data.size ? data.pages * data.size : 0)
      } catch (error) {
        console.error('获取数据失败', error)
      } finally {
        this.loading = false
      }
    },
    // 处理分页变化
    handlePaginationChange(page, pageSize) {
      this.paginationConfig.pageIndex = page
      this.paginationConfig.pageSize = pageSize

      this.fetchData(this.currentSearchConditions)
    },

    // 处理状态变化
    async handleStatusChange(val, record) {
      this.loading = true
      console.log('val', val)
      try {
        const res = await _updateSceneStatus({
          sceneId: record.sceneId,
          enableStatus: val
        })
        if (res.ret === 1) {
          this.$message.success(val === true ? '启用成功' : '禁用成功')
          this.fetchData(this.currentSearchConditions)
        } else {
          this.$message.error(res.msg)
          this.$nextTick(() => {
            record.enableStatus = val === true ? 0 : 1
          })
        }
      } catch (error) {
        console.error('更新状态失败', error)
        this.$message.error('更新状态失败')
        // 还原状态
        this.$nextTick(() => {
          record.enableStatus = !val
        })
      } finally {
        this.loading = false
      }
    },

    // 处理编辑
    handleEdit(record) {
      this.$emit('edit', record)
    },

    // 刷新数据
    refresh(searchConditions) {
      // 如果传入了搜索条件，重置页码到第一页
      if (searchConditions) {
        this.paginationConfig.pageIndex = 1
      }
      this.fetchData(searchConditions || this.currentSearchConditions)
    },

    // 重置数据
    reset() {
      // 清空当前搜索条件
      this.currentSearchConditions = {}
      // 重置页码到第一页
      this.paginationConfig.pageIndex = 1
      // 重新获取数据
      this.fetchData({})
    }
  }
}
</script>

<style lang="scss" scoped>
.scene-table {
  flex: 1;
}
</style>
