import { httpPost, httpGet } from '@/utils/http'

/**
 * 获取场景列表
 * @param {Object} params - 查询参数
 * @param {boolean} params.enableStatus - 启用状态（0.停用 1.启用）
 * @param {string} params.machineTypeId - 设备型号
 * @param {string} params.sceneId - 场景id
 * @returns {Promise}
 */
export function _fetchSceneList(params) {
  return httpGet('/client/scene/list.do', params)
}

/**
 * 编辑场景列表
 * @param {Object} data - 场景数据
 * @param {Array} data.machineTypeList - 关联设备：传勾选全部设备。 没有更新不传即可
 * @param {integer} data.machineTypeList[].machineTypeId - 设备类型id
 * @param {string} data.sceneId - 场景id
 * @param {Array} data.sceneInternationals - 国际化
 * @param {integer} data.sceneInternationals[].businessType - 业务类型（1.套餐名称2.套餐内容 3.广告url 4. 广告image 5.场景 6.套餐类型）
 * @param {string} data.sceneInternationals[].content - 内容
 * @param {integer} data.sceneInternationals[].id - id
 * @param {string} data.sceneInternationals[].lang - 语言类型 简体中文-cn 繁体中文-hk 西班牙语-es 葡萄牙语-pt 俄语-ru 德语-de 法语-fr
 * @param {integer} data.sort - 排序
 * @returns {Promise}
 */
export function _updateScene(data) {
  return httpPost('/client/scene/update.do', data)
}

/**
 * 会员中心启用
 * @param {Object} data - 启用状态数据
 * @param {boolean} data.enableStatus - 启用状态（0.停用 1.启用）
 * @param {string} data.sceneId - 场景id
 * @returns {Promise}
 */
export function _updateSceneStatus(data) {
  return httpPost('/client/scene/enable.do', data)
}

/**
 * 获取设备类型
 * @param {Object} params - 查询参数
 * @param {string} params.sceneId - 场景类型：1 时 可选参数（场景id）
 * @param {string} params.type - 场景类型：1：套餐类型与场景管理(默认值： 1)
 * @returns {Promise}
 */
export function _getMachineTypeForScene(params) {
  return httpGet('/client/machineType/get-machine-type.do', params)
}
