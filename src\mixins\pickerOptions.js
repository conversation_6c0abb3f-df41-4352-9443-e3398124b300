import { i18n } from '@/i18n'
let pickerOptionMixinx = {
  data() {
    let _this = this
    return {
      todayStartTime: null,
      todayEndTime: null,
      yesterdayStartTime: null,
      yesterdayEndTime: null,
      thisWeekStartTime: null,
      thisWeekEndTime: null,
      lastWeekStartTime: null,
      lastWeekEndTime: null,
      thisMonthStartTime: null,
      thisMonthEndTime: null,
      lastMonthStartTime: null,
      lastMonthEndTime: null,
      lastSixMonthStartTime: null,
      lastSixMonthEndTime: null,
      pickerOptions: {
        shortcuts: [
          {
            text: this.$t('XbMQZa2UhMAnIgRdfYmBE'),
            onClick(picker) {
              picker.$emit('pick', [_this.todayStartTime, _this.todayEndTime])
            }
          },
          {
            text: this.$t('0gU5oOZxYWY5c8diw5GGt'),
            onClick(picker) {
              picker.$emit('pick', [_this.yesterdayStartTime, _this.yesterdayEndTime])
            }
          },
          {
            text: this.$t('trhhfwertdgdfgqWERs45'),
            onClick(picker) {
              picker.$emit('pick', [_this.thisWeekStartTime, _this.todayEndTime])
            }
          },
          {
            text: this.$t('rerdsbfgrygRG8retEqrg'),
            onClick(picker) {
              picker.$emit('pick', [_this.lastWeekStartTime, _this.lastWeekEndTime])
            }
          },
          {
            text: this.$t('YI-CDEbvHMaK4Qo-P0C54'),
            onClick(picker) {
              picker.$emit('pick', [_this.thisMonthStartTime, _this.todayEndTime])
            }
          },
          {
            text: this.$t('35sfdgwERGRGg534sbvfv'),
            onClick(picker) {
              picker.$emit('pick', [_this.lastMonthStartTime, _this.lastMonthEndTime])
            }
          },
          //近半年
          {
            text: this.$t('K-bOltnmVObygLynJo1Zl'),
            onClick(picker) {
              picker.$emit('pick', [_this.lastSixMonthStartTime, _this.lastSixMonthEndTime])
            }
          }
        ]
      },
      speedOptions: {
        shortcuts: [
          {
            text: this.$t('0gU5oOZxYWY5c8diw5GGt'),
            onClick(picker) {
              picker.$emit('pick', [_this.yesterdayStartTime, _this.yesterdayEndTime])
            }
          },
          {
            text: this.$t('rerdsbfgrygRG8retEqrg'),
            onClick(picker) {
              picker.$emit('pick', [_this.lastWeekStartTime, _this.lastWeekEndTime])
            }
          },
          {
            text: this.$t('35sfdgwERGRGg534sbvfv'),
            onClick(picker) {
              picker.$emit('pick', [_this.lastMonthStartTime, _this.lastMonthEndTime])
            }
          }
        ]
      }
    }
  },
  mounted() {
    let now = new Date() //当前日期
    //获取今天时间
    this.todayStartTime = new Date(now.setHours(0, 0, 0, 0)) //获取当天零点的时间
    this.todayEndTime = new Date(now.setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1) //获取当天23:59:59的时间
    //获取昨天时间
    this.yesterdayStartTime = new Date(now.setHours(0, 0, 0, 0) - 24 * 60 * 60 * 1000)
    this.yesterdayEndTime = new Date(now.setHours(0, 0, 0, 0) - 1) //获取昨天23:59:59的时间
    // let dateMode = TUI.languageDateFormat(lang)  //日历组件中文格式和英文格式、发文格式要区分开来
    let nowDayOfWeek = now.getDay()
    let nowYear = now.getYear() //当前年
    let nowMonth = now.getMonth() //月
    let nowDay = now.getDate() //日
    nowDayOfWeek = nowDayOfWeek == 0 ? 7 : nowDayOfWeek // 如果是周日，就变成周七
    nowYear += nowYear < 2000 ? 1900 : 0
    //获取近半年开始时间
    this.lastSixMonthStartTime = new Date(nowYear, nowMonth - 6, nowDay, 0, 0, 0)
    //获取近半年结束时间
    this.lastSixMonthEndTime = new Date(nowYear, nowMonth, nowDay, 23, 59, 59)
    //获取这周周一零点的时间
    this.thisWeekStartTime = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek + 1)
    //获取上周周一零点的时间
    this.lastWeekStartTime = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek - 7 + 1)
    //获取上周周日23:59:59的时间
    this.lastWeekEndTime = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek, 23, 59, 59)
    //获取这月第一天零点的时间
    this.thisMonthStartTime = new Date(nowYear, nowMonth, 1)
    let lastMonthDate = new Date() //上月日期
    lastMonthDate.setDate(1)
    lastMonthDate.setMonth(lastMonthDate.getMonth() - 1)
    let lastMonth = lastMonthDate.getMonth()
    let _lastMonth = lastMonth + 1
    let _startY = nowYear
    let _startM = _lastMonth
    let _endY = nowYear
    let _endM = _lastMonth + 1
    if (_endM > 12) {
      _endM = _endM - 12
      _startY = _startY - 1
    }
    let monthStartDate = new Date(_startY, _startM - 1, 1)
    let monthEndDate = new Date(_endY, _endM - 1, 1)
    let days = (monthEndDate - monthStartDate) / (1000 * 60 * 60 * 24)
    //获得上月开始时间
    this.lastMonthStartTime = new Date(_startY, _startM - 1, 1)
    //获得上月结束时间
    this.lastMonthEndTime = new Date(_startY, _startM - 1, days, 23, 59, 59)
  }
}

export default pickerOptionMixinx
