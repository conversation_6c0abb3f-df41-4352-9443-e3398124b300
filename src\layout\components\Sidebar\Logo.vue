<template>
  <div class="sidebar-logo-container" :class="{ collapse: collapse, customClass: isCustomClass }" :style="{ background: $cookies.get('bgcColor') }">
    <transition name="sidebarLogoFade">
      <div v-if="collapse" key="collapse" class="sidebar-logo-link" @click="toggleSideBar">
        <div :class="['collapse-logo', customizedClass]"><img :src="logoUrl" style="vertical-align:middle; width: 44px" /></div>
      </div>
      <div v-else key="expand" class="sidebar-logo-link" @click="toggleSideBar">
        <div :class="['sidebar-logo', customizedClass]"><img :src="innerUrl" style="vertical-align:middle" /></div>
      </div>
    </transition>
  </div>
</template>

<script>
import here_inner from '@/assets/img/login/here/here_inner.png'
import ihere_inner from '@/assets/img/login/ihere/ihere_inner.png'

import here from '@/assets/img/login/here/here_fold.png'
import ihere from '@/assets/img/login/ihere/ihere_fold.png'

export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      title: '在这儿',
      innerUrl: here_inner,
      logoUrl: here,
      bgcColor: 'white',
      customizedClass: ''
    }
  },
  mounted() {
    let site = this.$cookies.get('site')
    switch (site) {
      case 'here':
        this.innerUrl = here_inner
        this.logoUrl = here
        break
      case 'ihere':
        this.innerUrl = ihere_inner
        this.logoUrl = ihere
        break
      default:
        break
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    }
  },
  computed: {
    isCustomClass() {
      return this.$cookies.get('site') === 'letztalkvideo' || this.$cookies.get('site') === 'rastrocar'
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  .sidebar-logo-link {
    .collapse-logo.custom {
      img {
        width: 48px;
      }
    }
    .sidebar-logo.custom {
      img {
        width: 191px;
      }
    }
    .collapse-logo.customOsrit {
      img {
        width: 29px !important;
      }
    }
    .collapse-logo.customQgps {
      img {
        width: 33px !important;
      }
    }
    .collapse-logo.customTrackind {
      img {
        width: 36px !important;
      }
    }
    .collapse-logo.customAutofast {
      img {
        width: 29px !important;
      }
    }
    .sidebar-logo.customAutofast {
      img {
        width: 155px !important;
      }
    }

    .collapse-logo.customRaqillatrust {
      img {
        width: 38px !important;
      }
    }
    .collapse-logo.customSatech {
      img {
        width: 42px !important;
      }
    }
    .collapse-logo.customAnka {
      img {
        width: 37px !important;
      }
    }
    .collapse-logo.customZaphira {
      img {
        width: 37px !important;
      }
    }
    .collapse-logo.customUbicol {
      img {
        width: 38px !important;
      }
    }
    .collapse-logo.customElitegps {
      img {
        width: 38px !important;
      }
    }
    .collapse-logo.customAvrgps {
      img {
        width: 39px !important;
      }
    }
    .collapse-logo.customimperialgps {
      img {
        width: 40px !important;
      }
    }
    .collapse-logo.customizeWastea {
      img {
        width: 39px !important;
      }
    }
    .collapse-logo.customizeStatgps {
      img {
        width: 38px !important;
      }
    }
    .sidebar-logo.customizeStatgps {
      img {
        width: 76% !important;
      }
    }
    .collapse-logo.customPathfinder {
      img {
        width: 31px !important;
      }
    }
    .sidebar-logo.customPathfinder {
      img {
        width: auto !important;
      }
    }
    .collapse-logo.customNova {
      img {
        width: 39px !important;
      }
    }
    .sidebar-logo.customNova {
      img {
        width: auto !important;
      }
    }
    .collapse-logo.customHasnet {
      img {
        width: 39px !important;
      }
    }
    .sidebar-logo.customHasnet {
      img {
        width: auto !important;
      }
    }
    .collapse-logo.customTcbGps {
      img {
        width: 42px !important;
      }
    }
    .collapse-logo.customEcofly {
      img {
        width: 37px !important;
      }
    }
    .sidebar-logo.customEcofly {
      img {
        width: auto !important;
      }
    }
    .collapse-logo.customUnequal {
      img {
        width: 42px !important;
      }
    }
    .collapse-logo.customGpsfirst {
      img {
        width: auto !important;
      }
    }
    .sidebar-logo.customGpsfirst {
      img {
        width: auto !important;
      }
    }
    .sidebar-logo.customGocam {
      img {
        width: auto !important;
      }
    }
    .collapse-logo.customGlobaltrack {
      img {
        width: 39px !important;
      }
    }
    .sidebar-logo.customGlobaltrack {
      img {
        width: auto !important;
      }
    }
    .collapse-logo.customAlbtrack {
      img {
        width: 40px !important;
      }
    }
    .sidebar-logo.customAlbtrack {
      img {
        width: auto !important;
      }
    }
    .collapse-logo.customUtrack {
      img {
        width: 39px !important;
      }
    }
    .sidebar-logo.customUtrack {
      img {
        width: auto !important;
      }
    }
    .collapse-logo.customEagle {
      img {
        width: 39px !important;
      }
    }
    .sidebar-logo.customEagle {
      img {
        width: auto !important;
      }
    }
  }
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 60px;
  line-height: 60px;
  text-align: center;
  overflow: hidden;
  padding: 20px 0;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    text-align: center;
    cursor: pointer;
    & .sidebar-logo {
      vertical-align: middle;
      margin-right: 12px;
      height: 100%;
      width: 100%;
      background-repeat: no-repeat;
      background-position: center;
      border: none;
    }

    & .collapse-logo {
      line-height: 70px;
      width: 100%;
      height: 100%;
      background-repeat: no-repeat;
      background-position: 50% 50%;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 70px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
  &.customClass {
    padding: 0 !important;
    background-color: #fff !important;
  }
}
</style>
