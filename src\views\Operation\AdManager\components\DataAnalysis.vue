<template>
  <div class="data-analysis">
    <!-- 未上线状态的缺省页 -->
    <div v-if="showEmptyState" class="empty-state">
      <span class="empty-text">{{ $t('tJI29weqgBdl7X8Q22xXm') }}</span>
    </div>

    <!-- 已上线状态的数据展示 -->
    <template v-else>
      <div class="analysis-header">
        <div class="date-info">
          <span>{{ $t('eRqgRC5edGZp2h9L4B0Wx', [statisticsEndDate]) }}</span>
        </div>
        <!-- 日期选择组件  点击开始日期和结束日期组件，弹出弹窗可以选择日期范围，最大可选范围1个月--默认选中最近7天（不包含今天）-->
        <div class="date-picker">
          <el-date-picker
            v-model="dateRange"
            popper-class="analysis-date-picker-popper"
            type="daterange"
            range-separator="-"
            :start-placeholder="$t('lg.selectStartTime')"
            :end-placeholder="$t('lg.selectEndTime')"
            :picker-options="pickerOptions"
            @change="handleDateChange"
          />
        </div>
      </div>
      <div class="analysis-content">
        <div class="analysis-row">
          <div class="stat-card">
            <div class="stat-value">{{ totalViews }}</div>
            <div class="stat-title">{{ $t('X0_QBixRCOYRGZyOIlRuw') }}</div>
          </div>

          <div class="chart-container">
            <line-chart
              :options="{
                title: $t('n7TbeWPx6ERiuCLdi1xMo'),
                unit: $t('UiI3Qz1DnMzuUOsVXVsFz'),
                seriesName: $t('1ykDVrd_FE2ioXA_6at7N'),
                color: '#0068FF',
                height: '245px'
              }"
              :data="{
                xAxis: dateList,
                series: viewsData,
                extraData: viewsExtraData
              }"
            />
          </div>
        </div>

        <div class="analysis-row">
          <div class="stat-card">
            <div class="stat-value">{{ totalVisitors }}</div>
            <div class="stat-title">{{ $t('5n4RaESXtvz5GiDax_JZt') }}</div>
          </div>

          <div class="chart-container">
            <line-chart
              :options="{
                title: $t('Qq5z7_IdzCpASYspXX9xc'),
                unit: $t('fQKeYjglc9g0-0oWVktBZ'),
                seriesName: $t('alO9uZXdsebWQhFqe3vS2'),
                color: '#6AD2FF',
                height: '245px'
              }"
              :data="{
                xAxis: dateList,
                series: visitorsData,
                extraData: visitorsExtraData
              }"
            />
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import LineChart from './LineChart.vue'
import { _fetchAdData } from '@/api/ads'
import { getDatePickerOptions, getLast7DaysRange } from '@/views/Operation/AdManager/utils'
import { timeConvert } from '@/utils/date'
export default {
  name: 'DataAnalysis',
  components: {
    LineChart
  },
  props: {
    adInfo: {
      type: Object,
      default: () => ({})
    },
    adStatus: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      totalViews: 0,
      totalVisitors: 0,
      statisticsEndDate: '',
      // 访问量数据
      viewsData: [],
      // 访客数据
      visitorsData: [],
      // 日期数据
      dateList: [],
      // 访问量环比和昨日数据
      viewsExtraData: [],
      // 访客环比和昨日数据
      visitorsExtraData: [],
      // 日期选择范围
      dateRange: [],
      pickerOptions: getDatePickerOptions()
    }
  },
  computed: {
    // 判断是否显示缺省状态
    showEmptyState() {
      return ['DRAFT', 'NOT_ONLINE'].includes(this.adStatus)
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    timeConvert,
    // 初始化数据
    async initData() {
      this.dateRange = getLast7DaysRange(true)
      await this.loadAdData()
    },

    // 加载广告数据
    async loadAdData() {
      const params = {
        id: this.adInfo.id || this.$route.query.id,
        startDate: this.dateRange[0] ? timeConvert(this.dateRange[0]) : '',
        endDate: this.dateRange[1] ? timeConvert(this.dateRange[1]) : ''
      }

      // 处理同一天的日期范围，设置为当天的开始和结束时间
      if (params.startDate === params.endDate) {
        const dateStr = timeConvert(params.startDate, 'local').split(' ')[0]
        params.startDate = timeConvert(`${dateStr} 00:00:00`)
        params.endDate = timeConvert(`${dateStr} 23:59:59`)
      }

      const dataRes = await _fetchAdData(params)

      if (dataRes.ret === 1 && dataRes.data) {
        // 设置统计截止时间
        console.log('dataRes.data.latestStatTime', dataRes.data.latestStatTime)

        this.statisticsEndDate = timeConvert(dataRes.data.latestStatTime, 'local')

        // 处理数据列表
        const dataList = dataRes.data.dataList || []
        // 提取并转换数据
        this.dateList = dataList.map(item => timeConvert(item.statDate, 'local', 'YYYY-MM-DD'))
        this.viewsData = dataList.map(item => item.viewCount)
        this.visitorsData = dataList.map(item => item.visitorCount)

        // 提取日环比和昨日数据
        this.viewsExtraData = dataList.map(item => ({
          dayOnDay: item.dayOnDay,
          yesterdayValue: item.yesterdayViewCount
        }))

        this.visitorsExtraData = dataList.map(item => ({
          dayOnDay: item.uvDayOnDay,
          yesterdayValue: item.yesterdayVisitorCount
        }))

        // 设置总计数据
        this.totalViews = dataRes.data.totalViewCount || 0
        this.totalVisitors = dataRes.data.totalVisitorCount || 0
      } else {
        this.$message.error(this.$t('dwAD15TZTKz8D9Aeii6hE'))
      }
    },

    // 处理日期变化
    handleDateChange(value) {
      if (value && value.length === 2) {
        //选择时间范围只能小于等于30天
        if (value[1] - value[0] > 30 * 24 * 60 * 60 * 1000 || value[0] - value[1] > 30 * 24 * 60 * 60 * 1000) {
          this.$message.error(this.$t('Lwkp0P2tS_8L9TCqgGY9H'))
          this.dateRange = getLast7DaysRange()
          return
        }
        this.loadAdData()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.data-analysis {
  padding: 0px 24px 24px 24px;
  height: 100%;
  min-height: calc(100vh - 220px);
  .empty-state {
    height: 330px;
    background-image: url('~@/assets/img/no-data-bg.png');
    background-repeat: no-repeat;
    background-size: 440px 330px;
    background-position: center top;
    display: flex;
    justify-content: center;
    align-items: flex-end;

    .empty-text {
      font-size: 16px;
      color: #333;
      line-height: 24px;
      font-weight: 500;
    }
  }
  .analysis-header {
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .date-info {
      font-size: 16px;
      color: #333;
      line-height: 24px;
      font-weight: 500;
    }
  }

  .analysis-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
  }

  .analysis-row {
    display: flex;
    gap: 24px;

    .stat-card {
      width: 35%;
      flex-shrink: 0;
      padding: 30px 24px;

      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .stat-value {
        font-size: 36px;
        font-weight: bold;
        color: #333;
        margin-bottom: 12px;
      }

      .stat-title {
        font-size: 16px;
        color: #666;
      }
    }

    .chart-container {
      flex: 1;
      background-color: #fff;
      border-radius: 8px;
      padding: 20px;
    }
  }
}

@media screen and (max-width: 1200px) {
  .data-analysis {
    .analysis-row {
      flex-direction: column;
      .stat-card {
        width: 100%;
        padding: 20px;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .data-analysis {
    padding: 16px;
  }
}
</style>
<style lang="scss">
.analysis-date-picker-popper {
  .el-date-table__row .today div span {
    //日期选择器中，原生ui高亮今天选项，因为业务需求不允许选中今天选项，故将高亮去掉，防止误导操作
    border: none;
    color: #707eae;
    background-color: #f2f6fc;
    font-weight: normal;
  }
}
</style>
