import { httpPost, httpGet, httpPostDownload } from '@/utils/http'

/**
 * 获取设备类型
 * @param {Object} params - 查询参数
 * @param {string} params.sceneId - 场景类型：1 时 可选参数（场景id）
 * @param {string} params.type - 场景类型：1：套餐类型与场景管理(默认值： 1)
 * @returns {Promise}
 */
export function _getMachineType(params) {
  return httpGet('/client/machineType/get-machine-type.do', params)
}

/**
 * 获取套餐类型列表
 * @param {Object} params - 查询参数
 * @param {string} params.commonSearch - 查询文本
 * @param {boolean} params.enableStatus - 启用状态（0.停用 1.启用）
 * @param {string} params.endTime - 结束时间
 * @param {Array} params.machineTypeIds - 设备型号ID数组
 * @param {integer} params.offset - 偏移量
 * @param {string} params.packTypeId - 套餐类型id
 * @param {string} params.packTypeName - 套餐类型名称，模糊查询
 * @param {integer} params.pageIndex - 页码
 * @param {integer} params.pageSize - 数量
 * @param {string} params.startTime - 开始时间
 * @param {integer} params.userId - 用户id
 * @param {integer} params.userType - 用户类型
 * @returns {Promise}
 */
export function _fetchPackageTypeList(params) {
  return httpPost('/client/packType/list.do', params)
}

/**
 * 新增套餐类型列表
 * @param {Object} data - 套餐类型数据
 * @param {Array} data.functionList - 关联设备：增值功能项。 没有更新不传即可
 * @param {integer} data.functionList[].funcId - 功能id
 * @param {string} data.functionList[].funcName - 功能名称
 * @param {Array} data.machineTypeList - 关联设备：传勾选全部设备。 没有更新不传即可
 * @param {integer} data.machineTypeList[].machineTypeId - 设备类型id
 * @param {string} data.machineTypeList[].machineTypeName - 设备类型名称
 * @param {integer} data.packClassify - 套餐一级分类，(字典值？)100：设备服务类、101：软件服务类、102：设备激活类
 * @param {string} data.packTypeId - 套餐类型id：新增不传
 * @param {Array} data.packTypeInternationals - 国际化。 没有更新不传即可
 * @param {integer} data.packTypeInternationals[].businessType - 业务类型（1.套餐名称2.套餐内容 3.广告url 4. 广告image 5.场景 6.套餐类型）
 * @param {string} data.packTypeInternationals[].content - 内容
 * @param {integer} data.packTypeInternationals[].id - id
 * @param {string} data.packTypeInternationals[].lang - 语言类型 简体中文-cn 繁体中文-hk 西班牙语-es 葡萄牙语-pt 俄语-ru 德语-de 法语-fr
 * @param {string} data.sceneId - 场景id
 * @param {integer} data.sort - 排序
 * @returns {Promise}
 */
export function _createPackageType(data) {
  return httpPost('/client/packType/create.do', data)
}

/**
 * 编辑套餐类型列表
 * @param {Object} data - 套餐类型数据
 * @param {Array} data.functionList - 关联设备：增值功能项。 没有更新不传即可
 * @param {integer} data.functionList[].funcId - 功能id
 * @param {string} data.functionList[].funcName - 功能名称
 * @param {Array} data.machineTypeList - 关联设备：传勾选全部设备。 没有更新不传即可
 * @param {integer} data.machineTypeList[].machineTypeId - 设备类型id
 * @param {string} data.machineTypeList[].machineTypeName - 设备类型名称
 * @param {integer} data.packClassify - 套餐一级分类，(字典值？)100：设备服务类、101：软件服务类、102：设备激活类
 * @param {string} data.packTypeId - 套餐类型id
 * @param {Array} data.packTypeInternationals - 国际化。 没有更新不传即可
 * @param {integer} data.packTypeInternationals[].businessType - 业务类型（1.套餐名称2.套餐内容 3.广告url 4. 广告image 5.场景 6.套餐类型）
 * @param {string} data.packTypeInternationals[].content - 内容
 * @param {integer} data.packTypeInternationals[].id - id
 * @param {string} data.packTypeInternationals[].lang - 语言类型 简体中文-cn 繁体中文-hk 西班牙语-es 葡萄牙语-pt 俄语-ru 德语-de 法语-fr
 * @param {string} data.sceneId - 场景id
 * @param {integer} data.sort - 排序
 * @returns {Promise}
 */
export function _updatePackageType(data) {
  return httpPost('/client/packType/update.do', data)
}

/**
 * 删除套餐类型列表
 */
export function _deletePackageType(data) {
  return httpPost('/client/packType/delete.do', data)
}

/**
 * 会员中心启用
 * @param {Object} data - 启用状态数据
 * @param {boolean} data.enableStatus - 启用状态（0.停用 1.启用）
 * @param {string} data.packTypeId - 套餐类型id
 * @returns {Promise}
 */
export function _updatePackageTypeStatus(data) {
  return httpPost('/client/packType/enable.do', data)
}

/**
 * 套餐新增
 * @param {Object} data - 套餐数据
 * @param {string} data.additionalDesc - 补充描述
 * @param {string} data.createTime - 创建时间
 * @param {string} data.endTime - 套餐结束时间
 * @param {Array} data.functionRules - 增值功能
 * @param {integer} data.functionRules[].calculateByMonth - 按月计算;0.否 1.是
 * @param {string} data.functionRules[].createTime - 创建时间
 * @param {integer} data.functionRules[].funcId - 功能ID
 * @param {integer} data.functionRules[].packId - 套餐id
 * @param {integer} data.functionRules[].param - 功能参数;用来表示录音时长、可查询天数、定位间隔
 * @param {number} data.functionRules[].primePrice - 成本价
 * @param {string} data.functionRules[].remark - 备注
 * @param {integer} data.functionRules[].ruleId - 主键;雪花id
 * @param {string} data.functionRules[].updateTime - 更新时间
 * @param {string} data.functionRules[].validDuration - 有效时长
 * @param {integer} data.functionRules[].validDurationUnit - 生效规则;1.天 2.月 3.年
 * @param {integer} data.functionRules[].validType - 生效类型;1.当月生效 2.次月生效
 * @param {number} data.guidingPrice - 指导价
 * @param {integer} data.isDailyPrice - 是否开启每日价格：1.是 0.否
 * @param {string} data.level - 套餐等级： 字典值（2.9.0 套餐新增）
 * @param {integer} data.packClassify - 套餐一级分类，100：设备服务类、101：软件服务类、102：设备激活类
 * @param {string} data.packContent - 套餐内容
 * @param {integer} data.packId - 主键
 * @param {string} data.packName - 套餐名称
 * @param {integer} data.packType - 套餐类型
 * @param {string} data.packTypeId - 套餐类型id（2.9.0 套餐新增）
 * @param {Array} data.packageInternationals - 国际化
 * @param {integer} data.packageInternationals[].businessType - 业务类型（1.套餐名称2.套餐内容 3.广告url 4. 广告image 5.场景 6.套餐类型）
 * @param {string} data.packageInternationals[].content - 内容
 * @param {integer} data.packageInternationals[].id - id
 * @param {string} data.packageInternationals[].lang - 语言类型 简体中文-cn 繁体中文-hk 西班牙语-es 葡萄牙语-pt 俄语-ru 德语-de 法语-fr
 * @param {string} data.packageNature - 套餐属性： 字典值（2.9.0 套餐新增）
 * @param {number} data.primePrice - 成本价
 * @param {string} data.remark - 捆绑套餐说明
 * @param {string} data.sceneId - 场景id （2.9.0 套餐新增）
 * @param {integer} data.serviceProviderId - 服务商id
 * @param {integer} data.standard - 是否为标配套餐;1.是 0.否
 * @param {string} data.startTime - 套餐开始时间
 * @param {integer} data.universal - 是否为通用套餐：1.是 0.否
 * @param {string} data.updateTime - 更新时间
 * @param {integer} data.wirelessType - 适用类型;0.有线 1.无线 2.all
 * @returns {Promise}
 */
export function _createPackage(data) {
  return httpPost('/client/packageConfig/create.do', data)
}

/**
 * 根据场景id获取套餐类型列表
 * @param {Object} data - 套餐类型数据
 * @param {string} data.sceneId - 场景id
 * @returns {Promise}
 */
export function _getPackTypeListByScene(data) {
  return httpGet('/client/packType/listByScene.do', data)
}
