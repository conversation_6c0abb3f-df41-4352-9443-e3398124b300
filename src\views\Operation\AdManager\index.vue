<!-- 广告管理 -->
<template>
  <div class="ad-manager">
    <div class="header-bar">
      <base-button change-icon icon="add" type="primary" @click="handleAddAd">{{ $t('新增广告') }}</base-button>
      <div class="search-area">
        <el-form :inline="true" class="search-form">
          <el-form-item :label="$t('Bkq0Yczk7b865d7nN8M8W')" v-if="!showMoreCodition">
            <el-input v-model="searchParams.name" :placeholder="$t('bzVAxsJEtQtawIDU5RBt1')" clearable />
          </el-form-item>
          <el-form-item>
            <base-button type="primary" @click="handleSearchList" v-if="!showMoreCodition">{{ $t('lg.query') }}</base-button>
            <el-button type="text" class="toggle-btn" @click="showMoreCodition = !showMoreCodition">
              {{ showMoreCodition ? $t('EG4bzTy443aMfL9yHir_p') : $t('XYgitqAwgNUZ_WMaw417U') }}
              <svg-icon icon-class="Down" :class="{ 'icon-rotate': showMoreCodition }"></svg-icon>
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <MoreConditionBar
      v-if="showMoreCodition"
      :searchParams.sync="searchParams"
      :targetUserOptions="targetUserOptions"
      :adPositionOptions="adPositionOptions"
      :statusOptions="statusOptions"
      @search="handleSearchList"
      @reset="handleReset"
    />

    <DataTable
      class="ad-manager-table"
      :data-list="dataList"
      :pagination-config="paginationConfig"
      :data-config="dataConfig"
      :table-config="{ isNeedNo: false }"
      @paginationChange="handlePaginationChange"
      @sort-change="handleSortChange"
      :height="tableHeight"
    >
      <template #targetUserType="{value}">
        {{ targetUserOptions.find(item => item.value === value)?.label || '-' }}
      </template>

      <template #adImg="{value, record}">
        <el-image
          v-if="value"
          :src="value"
          style="max-height: 50px; max-width: 100px; cursor: pointer; vertical-align: middle;"
          :preview-src-list="record.allImages && record.allImages.length > 0 ? record.allImages : [value]"
          fit="contain"
          :preview-teleported="true"
        >
          <div slot="error" style="width: 100px; height: 50px; display: flex; align-items: center; justify-content: center; color: #ccc; font-size: 12px;">
            {{ $t('iWYURze5gw6IuatRIrbE9') }}
          </div>
          <template #placeholder>
            <div style="width: 100px; height: 50px; display: flex; align-items: center; justify-content: center; color: #ccc; font-size: 12px;">
              {{ $t('k9HNUSJDi33dfFV3JFI0F') }}
            </div>
          </template>
        </el-image>
        <span v-else style="color: #ccc; font-size: 12px;">{{ $t('vt1MZfWXyMZzyFU7ZD6kc') }}</span>
      </template>

      <template #sort="{value, record}">
        <el-input :value="value" @input="val => handleChangeSortValue(val, record)" @change="handleBlurSortValue(record)" style="width: 50px;" />
      </template>
      <template #adPosition="{value}">
        {{ adPositionOptions.find(item => item.value === value)?.label || '-' }}
      </template>
      <template #adType="{value}">
        {{ adTypeOptions.find(item => item.value === value)?.label || '-' }}
      </template>
      <template #adPeriod="{record}"> {{ timeConvert(record.startTime, 'local') }} - {{ timeConvert(record.endTime, 'local') }} </template>
      <template #status="{value}">
        <span :class="[value === 'ONLINE' ? 'ljdw-color-green' : '']">{{ statusMap[value] }}</span>
      </template>
      <template #updateTime="{value}">{{ timeConvert(value, 'local') }}</template>
      <template #operatorBtn="{record}">
        <!-- 所有状态都有详情 -->
        <span class="ljdw-color-blue cursor-point" style="margin-right: 10px" @click="handleDetail(record)">{{ $t('d2rfxOdgIK8sx-aXn_UjT') }}</span>

        <!-- 草稿状态(DRAFT):  发布、编辑、删除 -->
        <template v-if="record.status === 'DRAFT'">
          <span class="ljdw-color-blue cursor-point" style="margin-right: 10px" @click="handlePublish(record)">{{ $t('s5dkA82oFKqgptUbqsic4') }}</span>
          <span class="ljdw-color-blue cursor-point" style="margin-right: 10px" @click="handleEditAd(record)">{{ $t('lg.edit') }}</span>
          <span class="ljdw-color-blue cursor-point" @click="handleRemove(record)">{{ $t('lg.delete') }}</span>
        </template>

        <!-- 待上线状态(NOT_ONLINE): 回退、删除 -->
        <template v-else-if="record.status === 'NOT_ONLINE'">
          <span class="ljdw-color-blue cursor-point" style="margin-right: 10px" @click="handleRollback(record)">{{ $t('8Y9n6AMUzmJgLppRkiPqC') }}</span>
          <span class="ljdw-color-blue cursor-point" @click="handleRemove(record)">{{ $t('lg.delete') }}</span>
        </template>

        <!-- 已上线状态(ONLINE): 下线 -->
        <template v-else-if="record.status === 'ONLINE'">
          <span class="ljdw-color-blue cursor-point" @click="handleOffline(record)">{{ $t('ybYXmIEwb2gFT3jw0NhiZ') }}</span>
        </template>

        <!-- 已过期(EXPIRED)或已下线(OFFLINE)状态: 删除 -->
        <template v-else-if="record.status === 'EXPIRED' || record.status === 'OFFLINE'">
          <span class="ljdw-color-blue cursor-point" @click="handleRemove(record)">{{ $t('lg.delete') }}</span>
        </template>
      </template>
    </DataTable>

    <AdConflictDialog v-if="conflictDialogVisible" :visible.sync="conflictDialogVisible" :ad-type="nowAdType" :ad-list="conflictAdsList" />
  </div>
</template>

<script>
import DataTable from '@/components/Common/DataTable'
import { mapState } from 'vuex'
import { timeConvert } from '@/utils/date'

import {
  dataConfig,
  statusMap,
  targetUserOptions,
  adPositionOptions,
  statusOptions,
  pickerOptions,
  getInitialSearchParams,
  defaultPaginationConfig
} from './constant/config'
import MoreConditionBar from './components/MoreConditionBar'
import { _fetchAdList, _deleteAd, _updateAdStatus, _updateAd, _fetchAdDetail, _fetchAdUserCount } from '@/api/ads'
import AdConflictDialog from './components/AdConflictDialog.vue'
import { AD_OPERATION_TYPES, AD_TYPE_OPTIONS } from './constant/adConstants'
import { publishAd, offlineAd, rollbackAd } from './utils'
export default {
  name: 'AdManager',
  components: {
    DataTable,
    MoreConditionBar,
    AdConflictDialog
  },
  data() {
    return {
      timeConvert,
      statusMap,
      dataConfig: Object.freeze(dataConfig),
      targetUserOptions,
      adPositionOptions,
      adTypeOptions: AD_TYPE_OPTIONS,
      statusOptions,
      searchParams: getInitialSearchParams(),
      pickerOptions,
      expireTimeSpan: null,
      dataList: [],
      dataListLoading: false,
      paginationConfig: { ...defaultPaginationConfig },
      showMoreCodition: false,
      conflictDialogVisible: false,
      conflictAdsList: [],
      nowAdType: '' // 当前操作的这条广告的广告类型：弹窗或者banner，用于给冲突弹窗组件传入
    }
  },
  computed: {
    ...mapState({
      systemDateFormat: state => state.user.systemDateFormat
    }),
    tableHeight() {
      return 'calc(100% - 66px)'
    }
  },
  methods: {
    handleReset() {
      this.searchParams = getInitialSearchParams()
    },
    handlePaginationChange({ pageSize, pageIndex }) {
      this.paginationConfig.pageIndex = pageIndex
      this.paginationConfig.pageSize = pageSize ?? this.paginationConfig.pageSize
      this.handleSearchList()
    },
    handleAddAd() {
      // 跳转到新增广告页面
      this.$router.push('/ad/addedit')
    },
    handleEditAd(record) {
      this.$router.push(`/ad/addedit?id=${record.id}`)
    },
    //sort-change事件虽然没在DataTable中手动emit，但是原生事件还是会触发
    handleSortChange({ prop, order }) {
      const oderByAsc = order ? (order === 'ascending' ? true : false) : ''
      this.searchParams.sortAsc = oderByAsc
      this.handleSearchList()
    },
    async handleRemove(record) {
      await this.$confirm(this.$t('dRz9guMYbTUs-t4KKVMoI'), this.$t('lg.notification'), {
        confirmButtonText: this.$t('lg.confirm'),
        cancelButtonText: this.$t('lg.cancel'),
        type: 'warning'
      })
      try {
        await _deleteAd(record.id)
        this.$message.success(this.$t('cvyH4iJuWCiLfB_Wsn4Nn'))
        this.handleSearchList()
      } catch (error) {
        this.$message.error(this.$t('cUE4imnis7N3pb9QDkywH'))
      }
    },
    async handleSearchList() {
      if (this.searchParams.dateRange && this.searchParams.dateRange.length === 2) {
        // 使用timeConvert函数将本地时间转换为UTC时间
        this.searchParams.startTime = timeConvert(this.searchParams.dateRange[0])
        this.searchParams.endTime = timeConvert(this.searchParams.dateRange[1])
      } else {
        this.searchParams.startTime = null
        this.searchParams.endTime = null
      }
      this.dataListLoading = true
      try {
        const params = {
          ...this.searchParams,
          offset: (this.paginationConfig.pageIndex - 1) * this.paginationConfig.pageSize,
          pageIndex: this.paginationConfig.pageIndex,
          pageSize: this.paginationConfig.pageSize,
          lang: this.$i18n.locale || 'en'
        }
        delete params.dateRange
        const res = await _fetchAdList(params)
        const currentLang = this.$i18n.locale || 'en'
        this.dataList = (res.data?.records || []).map(item => {
          const langContent = item.langContents?.find(content => content.lang === currentLang)
          const defaultLangContent = item.langContents?.find(content => content.lang === 'en') || item.langContents?.[0]
          const allImages = (item.langContents || []).map(content => content.image).filter(image => image && image.trim() !== '')

          return {
            ...item,
            adImg: langContent?.image || defaultLangContent?.image || '',
            allImages: allImages.length > 0 ? allImages : []
          }
        })
        this.paginationConfig.total = res.data?.total || 0
      } catch (error) {
        this.$message.error(this.$t('FPiSGK9vmdXLttuGBLCNy'))
      } finally {
        this.dataListLoading = false
      }
    },

    handleDetail(record) {
      if (!record.id) {
        this.$message.error(this.$t('-BryKeYUPdPBoc7NnBDjn'))
        return
      }
      this.$router.push(`/ad/detail?id=${record.id}`)
    },
    async handlePublish(record) {
      this.nowAdType = record.adType
      this.$confirm(this.$t('QMXVAEUa1l0LSMMq1P-GE'), this.$t('lg.notification'), {
        confirmButtonText: this.$t('lg.confirm'),
        cancelButtonText: this.$t('lg.cancel'),
        type: 'warning'
      })
        .then(async () => {
          //发布前先调接口检查用户数，如果用户数为0则不可以发布
          console.log('record', record)
          const deviceModel = record.relateDevices.map(item => item.machineTypeId).join(',')
          const params = {
            targetUserType: record.targetUserType,
            deviceModel: deviceModel
          }
          const res = await _fetchAdUserCount(params)
          if (res.ret === 1 && res.data !== undefined) {
            if (res.data.userCount === 0) {
              this.$message.error(this.$t('xhfKJiat7T-bprrBbtlKc'))
              return
            }
          }

          await publishAd({
            id: record.id,
            onSuccess: () => {
              this.$message.success(this.$t('8cuHAuf6V9y1S0ktrujuE'))
              this.handleSearchList()
            },
            onError: msg => {
              this.$message.error(msg || this.$t('9qwy42KyBhnFOtUfCP4L5'))
            },
            onConflict: conflictList => {
              this.conflictAdsList = conflictList
              this.conflictDialogVisible = true
            }
          })
        })
        .catch(() => {})
    },

    async handleRollback(record) {
      await this.$confirm(this.$t('p8YaGbXkOnRHfH73h6uwL'), this.$t('lg.notification'), {
        confirmButtonText: this.$t('lg.confirm'),
        cancelButtonText: this.$t('lg.cancel'),
        type: 'warning'
      })
        .then(async () => {
          await rollbackAd({
            id: record.id,
            onSuccess: () => {
              this.$message.success(this.$t('WUHni97FVPSbV261NuNyM'))
              this.handleSearchList()
            },
            onError: msg => {
              this.$message.error(msg || this.$t('52qKTZxNp_787WjWjlGIw'))
            }
          })
        })
        .catch(() => {})
    },

    async handleOffline(record) {
      await this.$confirm(this.$t('Xflr8gmyOVSwPDsoDtigU'), this.$t('lg.notification'), {
        confirmButtonText: this.$t('lg.confirm'),
        cancelButtonText: this.$t('lg.cancel'),
        type: 'warning'
      })
        .then(async () => {
          await offlineAd({
            id: record.id,
            onSuccess: () => {
              this.$message.success(this.$t('46L3Sa2n3_HRwD4n9EMdB'))
              this.handleSearchList()
            },
            onError: msg => {
              this.$message.error(msg || this.$t('ta6lpPTi4NhVLI6x0xtqy'))
            }
          })
        })
        .catch(() => {
          //
        })
    },
    async handleChangeSortValue(value, record) {
      const updatedList = [...this.dataList]
      const index = updatedList.findIndex(item => item.id === record.id)
      updatedList[index].sort = value
      this.dataList = JSON.parse(JSON.stringify(updatedList))
    },
    async handleBlurSortValue(record) {
      const index = this.dataList.findIndex(item => item.id === record.id)
      const value = this.dataList[index].sort
      const reg = /^[0-9]{1,2}$/
      if (!reg.test(value)) {
        this.$message.error(this.$t('4XmNWhQoR-eEcZdOzqJR9'))
        this.handleSearchList()
        return
      }
      const res = await _updateAd({ id: record.id, sort: value, operationType: AD_OPERATION_TYPES.SORT })
      if (res.ret === 1) {
        this.$message.success(this.$t('jM1EyGoVHeSsOrb84SzPr'))
        this.handleSearchList()
      } else {
        const msg = res.msg || this.$t('QR-j-6cQyMmEYZUipjhag')
        this.$message.error(msg)
        this.handleSearchList()
      }
    }
  },
  mounted() {
    this.handleSearchList()
  }
}
</script>

<style lang="scss" scoped>
.ad-manager {
  height: calc(100% - 32px);
  box-sizing: border-box;
  margin: 16px;
  padding: 16px;
  padding-bottom: 0;
  background: #ffffff;
  overflow: auto;
  border-radius: $containerRadius;
  display: flex;
  flex-direction: column;
  .header-bar {
    display: flex;
    align-items: flex-start;
    .search-area {
      flex: 1;
    }

    .search-form {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-end;
    }
  }

  .ad-manager-table {
    flex: 1;
    overflow: auto;
  }
}
</style>
