<template>
  <div class="panel-content">
    <SearchForm ref="SearchForm" @search="formSearch" @reset="handleReset" :formParams="formParams" @batch-search="batchSearchVisible = true" />
    <Table
      ref="WithdrawRecordTable"
      :dataLoading="dataLoading"
      :searchParams="searchParams"
      :tableData="tableData"
      :total="total"
      :showRefund="showRefund"
      @pageChange="handleSearch"
      @sizeChange="handleSizeChange"
      @showDetail="showDetail"
      @refresh="handleSearch"
    />
    <Detail :visible.sync="detailVisible" dialogType="1" :info="rowInfo" />
    <!-- 批量查询 -->
    <BatchSearch ref="BatchSearch" :visible.sync="batchSearchVisible" @confirm="handleBatchSearch"></BatchSearch>
  </div>
</template>

<script>
import SearchForm from './components/SearchForm.vue'
import Table from './components/Table.vue'
import Detail from '@/views/finance/FinancialManagement/WithdrawVerify/components/Detail.vue'
import BatchSearch from './components/BatchSearch.vue'

import { timeConvert, lastMonthNowDate } from '@/utils/date.js'
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'
import { _getRefundList, _getWithdrawalList, _exportWhithrawalList } from '@/api/order.js'
export default {
  components: { SearchForm, Table, Detail, BatchSearch },
  props: {
    changeTabInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formParams: {
        busTradeNo: '',
        prependFilterType: 2,
        orderOrPayVal: '',
        imei: '',
        refundStatus: '',
        serviceProviderId: '',
        applyTime: [lastMonthNowDate(), dayjs().format('YYYY-MM-DD 23:59:59')],
        reviewerTime: [],
        imeis: null,
        tradeNos: null
      },
      searchParams: {
        pageNo: 1,
        pageSize: 15
      },
      dataLoading: false,
      tableData: [],
      total: 0,
      showRefund: false,
      rowInfo: {},
      detailVisible: false,
      batchSearchVisible: false
    }
  },
  computed: {
    ...mapGetters(['currencyParams'])
  },
  mounted() {
    this.handleSearch()
  },
  methods: {
    handleSearch() {
      // this.getWithdrawalsTotal()
      this.getWithdrawalList()
    },
    formSearch() {
      let params = {}
      console.log('this.formParams.applyTime', this.formParams.applyTime)
      if (this.formParams.busTradeNo) {
        params.busTradeNo = this.formParams.busTradeNo
      }
      if (this.formParams.prependFilterType === 1 && this.formParams.orderOrPayVal) {
        params.tradeNo = this.formParams.orderOrPayVal
      }
      if (this.formParams.prependFilterType === 2 && this.formParams.orderOrPayVal) {
        params.refundNo = this.formParams.orderOrPayVal
      }
      if (this.formParams.imei) {
        params.imei = this.formParams.imei
      }
      if (this.formParams.refundStatus) {
        params.refundStatus = this.formParams.refundStatus
      }
      if (this.formParams.serviceProviderId) {
        params.serviceProviderId = this.formParams.serviceProviderId
      }
      if (this.formParams.applyTime && this.formParams.applyTime.length) {
        params.applyStartTime = timeConvert(this.formParams.applyTime[0])
        params.applyEndTime = timeConvert(this.formParams.applyTime[1])
      } else if (!this.formParams.reviewerTime || !this.formParams.reviewerTime.length) {
        //2025-07-16 修改为不传时间，不清楚原来的逻辑为什么要这样做，但是现在出现的情况是清空查询日期时仍然会带上时间导致查不到数据，
        // 所以暂时先注释掉，如果后续有需要再打开
        //  params.applyStartTime = timeConvert(lastMonthNowDate())
        //  params.applyEndTime = timeConvert(dayjs().format('YYYY-MM-DD 23:59:59'))
      }
      if (this.formParams.reviewerTime && this.formParams.reviewerTime.length) {
        params.reviewerStrartTime = timeConvert(this.formParams.reviewerTime[0])
        params.reviewerEndTime = timeConvert(this.formParams.reviewerTime[1])
      }
      if (this.formParams.imeis) {
        params.imeis = this.formParams.imeis
        delete params.applyStartTime
        delete params.applyEndTime
        delete params.reviewerStrartTime
        delete params.reviewerEndTime
      }
      if (this.formParams.tradeNos) {
        params.tradeNos = this.formParams.tradeNos
        delete params.applyStartTime
        delete params.applyEndTime
        delete params.reviewerStrartTime
        delete params.reviewerEndTime
      }
      this.searchParams = { ...params, pageNo: 1, pageSize: this.searchParams.pageSize }
      this.handleSearch()
      this.$emit('refreshAccount')
    },
    // 获取列表数据
    async getWithdrawalList() {
      this.dataLoading = true
      try {
        let res = await _getRefundList({
          ...this.searchParams,
          isWithdrawalReview: 0,
          currency: this.currencyParams
          // serviceProviderId: '1659403011845259264'
          // serviceProviderId: this.$cookies.get('bisUserId')
        })
        if (res.ret === 1) {
          this.tableData = res.data || []
          this.total = res.total || 0
          this.showRefund = res.extend?.showRefundOperation
        } else {
          this.$message.error(res.msg)
        }
        this.dataLoading = false
      } catch (error) {
        this.dataLoading = false
        throw new Error(error)
      }
    },
    handleReset() {
      this.formParams = {
        busTradeNo: '',
        prependFilterType: 2,
        orderOrPayVal: '',
        imei: '',
        refundStatus: '',
        applyTime: [lastMonthNowDate(), dayjs().format('YYYY-MM-DD 23:59:59')],
        reviewerTime: [],
        imeis: null,
        tradeNos: null
      }
      this.formSearch()
    },
    handleSizeChange(size) {
      this.searchParams.pageSize = size
      this.searchParams.pageNo = 1
      this.handleSearch()
    },
    // 提现详情弹窗
    showDetail(row) {
      this.rowInfo = row
      this.detailVisible = true
    },
    // 批量搜索
    handleBatchSearch(info) {
      this.batchSearchVisible = false
      this.formParams = {
        busTradeNo: '',
        prependFilterType: 2,
        orderOrPayVal: '',
        imei: '',
        refundStatus: '',
        applyTime: [],
        reviewerTime: [],
        ...info
      }
      this.formSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-content {
  box-sizing: border-box;
  padding: 0 20px 10px;
  // height: calc(100vh - 280px);
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
