<template>
  <div class="c-data-table">
    <el-table
      ref="dataTableRef"
      :id="className"
      v-loading="tableConfig.load || false"
      v-on="$listeners"
      v-bind="tableConfig"
      :data="dataList"
      :empty-text="$t('lg.noData')"
      :height="height"
    >
      <el-table-column v-if="tableConfig.isNeedCheckBox" type="selection" width="55" :selectable="handleSelectTable"> </el-table-column>
      <el-table-column v-if="tableConfig.isNeedNo" :label="$t('lg.serial')" align="left" width="86px">
        <template slot-scope="scope">
          {{ (paginationConfig.pageIndex - 1) * paginationConfig.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="item in dataConfig"
        :key="item.label"
        :label="$t(item.label)"
        :prop="item.prop"
        v-bind="item.configs"
        :sortable="item.sortable || false"
        :width="item.width || ''"
        :align="item.align || 'left'"
      >
        <template slot-scope="{ row }">
          <slot :name="item.key" :record="row" :value="row[item.prop]">{{ row[item.prop] || '-' }}</slot>
        </template>
        <template #header>
          <slot :name="item.key + 'Header'">{{ $t(item.label) }}</slot>
        </template>
      </el-table-column>
    </el-table>
    <div class="c-data-table__pagination" v-if="!tableConfig.isHidePagination">
      <base-pagination
        :current-page.sync="paginationConfig.pageIndex"
        :page-sizes="[10, 15, 20, 30]"
        :page-size="paginationConfig.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="paginationConfig.total"
        @current-change="page => change('pageIndex', page)"
        @size-change="size => change('pageSize', size)"
      >
      </base-pagination>
      <slot name="bottom"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DataTable',
  props: {
    tableConfig: {
      type: Object,
      default: () => {}
    },
    dataConfig: {
      type: Array,
      default: () => []
    },
    paginationConfig: {
      type: Object,
      default: () => {}
    },
    dataList: {
      type: Array,
      default: () => []
    },
    className: {
      type: String,
      default: ''
    },
    selectable: {
      type: [Function, Boolean],
      default: false
    },
    height: {
      type: String,
      default: ''
    }
  },
  methods: {
    getSelections() {
      return this.$refs.dataTableRef.selection
    },
    handleSelectTable(record) {
      if (this.selectable) {
        return this.selectable(record)
      }
      return true
    },
    selectionChange(row) {
      if (this.tableConfig.selectionChange) {
        this.tableConfig.selectionChange(row)
      }
    },
    change(key, value) {
      if (key === 'pageSize') {
        this.$emit('paginationChange', { [key]: value, pageIndex: 1 })
        return
      }
      this.$emit('paginationChange', { [key]: value })
    },

    clearSelection() {
      this.$refs.dataTableRef.clearSelection()
    },
    toggleRowSelection(item, flag) {
      return this.$refs.dataTableRef.toggleRowSelection(item, flag)
    },
    toggleAllSelection() {
      this.$refs.dataTableRef.toggleAllSelection()
    },
    setTableScrollBottom() {
      this.$refs.dataTableRef.bodyWrapper.scrollTop = this.$refs.dataTableRef.bodyWrapper.scrollHeight
    }
  }
}
</script>

<style lang="scss" scoped>
.c-data-table__pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  position: relative;
}
</style>
