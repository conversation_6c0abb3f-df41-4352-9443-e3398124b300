<template>
  <div class="line-chart-container">
    <div class="chart-title">{{ options.title }}</div>
    <div class="chart-wrapper" ref="chartRef"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'LineChart',
  props: {
    // 图表配置选项
    options: {
      type: Object,
      default: () => ({
        title: '',
        unit: '',
        showHeader: true,
        seriesName: '',
        color: '#0068FF',
        height: '300px',
        smooth: true
      })
    },
    // 图表数据
    data: {
      type: Object,
      required: true,
      default: () => ({
        xAxis: [],
        series: [],
        extraData: []
      })
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    chartOptions() {
      return {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255, 255, 255)',
          textStyle: {
            color: '#707EAE',
            fontWeight: 400
          },
          formatter: params => {
            const dataItem = params[0]
            let content = dataItem.axisValue + '<br/>'
            // 当前值
            const currentValue = dataItem.value
            // 使用后端返回的环比数据
            const currentIndex = dataItem.dataIndex
            const extData = this.data.extraData?.[currentIndex] || {}
            const compareRatio = extData.dayOnDay || '-'
            const yesterdayValue = extData.yesterdayValue || '-'

            content += `<div style ='width:100%; height:5px; '></div>`
            content += `<div style=" width:7px;height:7px;border:1px solid #ffffff;margin-right:5px;border-radius:10px;display:inline-block;background-color:${this.options.color};"></div>
                     ${this.options.seriesName}: ${currentValue}<br/>`
            content += `<div style="margin-left:13px; margin-top:5px; ">日环比：<span style="color:${
              compareRatio > 0 ? 'red' : '#0068FF'
            }"> ${compareRatio}</span> &nbsp;&nbsp;&nbsp;&nbsp;昨日：${yesterdayValue}</div> `
            return content
          },
          extraCssText: 'padding:14px;box-shadow:0px 0px 7px 0px rgba(0, 0, 0, 0.15);border-radius:5px;'
        },
        grid: {
          top: '40px',
          left: '48px',
          right: '30px',
          bottom: '30px',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.data.xAxis,
          axisLine: {
            lineStyle: {
              color: '#D9D9D9'
            }
          },
          axisTick: {
            length: 2,
            alignWithLabel: true
          },
          axisLabel: {
            margin: 11,
            align: 'center',
            color: '#2F2F2F'
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#D9D9D9'
            }
          },
          axisLabel: {
            margin: 23,
            align: 'center',
            color: '#2F2F2F',
            formatter: value => {
              return value
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          name: this.options.unit
        },
        series: [
          {
            name: this.options.seriesName,
            type: 'line',
            data: this.data.series,
            showSymbol: false,
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: this.options.color
            },
            emphasis: {
              itemStyle: {
                shadowColor: '#BEF2FF',
                shadowBlur: 5
              }
            },
            smooth: this.options.smooth
          }
        ]
      }
    }
  },
  watch: {
    // 监听属性变化，更新图表
    data: {
      handler() {
        this.updateChart()
      },
      deep: true
    },
    options: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.resizeChart)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeChart)
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    initChart() {
      this.$nextTick(() => {
        if (this.chart) {
          this.chart.dispose()
        }
        const chartDom = this.$refs.chartRef
        this.chart = echarts.init(chartDom)
        this.updateChart()
      })
    },
    updateChart() {
      if (this.chart) {
        this.chart.setOption(this.chartOptions)
      }
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.line-chart-container {
  width: 100%;
  height: 100%;

  .chart-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .unit-info {
    font-size: 12px;
    color: #999;
  }

  .chart-wrapper {
    width: 50vw;
    height: v-bind('options.height');
  }
}
</style>
