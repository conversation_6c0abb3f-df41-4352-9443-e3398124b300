<template>
  <DataTable
    class="package-type-table"
    :data-list="dataList"
    :pagination-config="paginationConfig"
    :data-config="dataConfig"
    :table-config="{ isNeedNo: true }"
    @paginationChange="handlePaginationChange"
    height="calc(100% - 77px)"
    v-loading="loading"
  >
    <!-- 套餐类型名称 -->
    <template #packTypeName="{record}">
      {{ record.packTypeName }}
    </template>
    <!-- 关联设备型号 -->
    <template #machineTypeList="{value}">
      <template v-if="value && value.length > 0">
        {{ value.map(item => item.machineTypeName).join('、') }}
      </template>
      <span v-else>-</span>
    </template>

    <!-- 增值项 -->
    <template #functionList="{value}">
      <template v-if="value && value.length > 0">
        {{ value.map(item => item.funcName).join('、') }}
      </template>
      <span v-else>-</span>
    </template>
    <!-- 排序号 -->
    <template #sort="{record}">
      {{ record.sort === '' ? '-' : record.sort }}
    </template>
    <!-- 会员中心启用状态 -->
    <template #enableStatus="{record}">
      <el-switch v-model="record.enableStatus" :active-value="true" :inactive-value="false" @change="val => handleStatusChange(val, record)"></el-switch>
    </template>

    <!-- 操作 -->
    <template #operation="{record}">
      <el-button type="text" @click="handleEdit(record)">编辑</el-button>
      <el-button type="text" @click="handleDelete(record)">删除</el-button>
    </template>
  </DataTable>
</template>

<script>
import DataTable from '@/components/Common/DataTable'
import { dataConfig, defaultPaginationConfig } from '../config'
import { _fetchPackageTypeList, _updatePackageTypeStatus, _deletePackageType } from '@/api/packageType'
import { isAbroad } from '@/utils/judge'

export default {
  components: {
    DataTable
  },
  data() {
    return {
      dataList: [],
      dataConfig,
      paginationConfig: { ...defaultPaginationConfig },
      loading: false,
      currentSearchConditions: {} // 本地存储当前搜索条件
    }
  },
  computed: {
    defaultLang() {
      return this.getDefaultLang()
    }
  },
  methods: {
    isAbroad,
    getDefaultLang() {
      return isAbroad() ? 'en' : 'cn'
    },
    async fetchData(searchConditions = {}) {
      this.loading = true
      try {
        // 更新本地搜索条件
        this.currentSearchConditions = searchConditions

        const { pageIndex, pageSize } = this.paginationConfig
        const params = {
          ...searchConditions,
          pageIndex,
          pageSize
        }
        const response = await _fetchPackageTypeList(params)
        const data = response.data
        this.dataList = (data.records || []).map(record => {
          // 处理场景名称的多语言显示
          let sceneName = record.sceneName
          let packTypeName = record.packTypeName
          let sceneId = record.sceneId
          if (record.sceneInternationals && Array.isArray(record.sceneInternationals)) {
            const sceneInternational = record.sceneInternationals.find(item => item.lang === this.defaultLang) || record.sceneInternationals[0]
            sceneName = sceneInternational?.content || record.sceneName || '--'
          }
          if (record.packTypeInternationals && Array.isArray(record.packTypeInternationals)) {
            const packTypeInternational = record.packTypeInternationals.find(item => item.lang === this.defaultLang) || record.packTypeInternationals[0]
            packTypeName = packTypeInternational?.content || record.packTypeName || '--'
          }
          return {
            ...record,
            sceneName,
            packTypeName,
            sceneId
          }
        })
        this.paginationConfig.total = data.total || (data.pages && data.size ? data.pages * data.size : 0)
      } catch (error) {
        console.error('获取数据失败', error)
        this.$message.error('获取数据失败')
        this.dataList = []
        this.paginationConfig.total = 0
      } finally {
        this.loading = false
      }
    },

    // 处理分页变化
    handlePaginationChange(page, pageSize) {
      this.paginationConfig.pageIndex = page
      this.paginationConfig.pageSize = pageSize
      this.fetchData(this.currentSearchConditions)
    },

    // 处理状态变化
    async handleStatusChange(val, record) {
      // 调用API更新状态
      this.loading = true
      try {
        const res = await _updatePackageTypeStatus({
          packTypeId: record.packTypeId,
          enableStatus: val
        })
        if (res.ret === 1) {
          this.$message.success(val === true ? '启用成功' : '禁用成功')
          this.fetchData(this.currentSearchConditions)
        } else {
          this.$message.error(res.msg)
          this.$nextTick(() => {
            record.enableStatus = val === true ? false : true
          })
        }
      } catch (error) {
        console.error('更新状态失败', error)
        this.$message.error('更新状态失败')
        // 还原状态
        this.$nextTick(() => {
          record.enableStatus = val === 1 ? 0 : 1
        })
      } finally {
        this.loading = false
      }
    },

    // 处理编辑
    handleEdit(record) {
      this.$emit('edit', record)
    },

    // 处理删除
    handleDelete(record) {
      this.$confirm('该套餐类型下存在套餐，删除将同步删除所有关联套餐，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          this.loading = true
          try {
            // 调用删除API
            const res = await _deletePackageType({
              packTypeId: record.packTypeId
            })
            if (res.ret === 1) {
              // 删除成功后刷新数据
              this.$message.success('删除成功')
              this.fetchData(this.currentSearchConditions)
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            console.error('删除失败', error)
            this.$message.error('删除失败')
          } finally {
            this.loading = false
          }
        })
        .catch(() => {})
    },

    // 刷新数据
    refresh(searchConditions) {
      // 如果传入了搜索条件，重置页码到第一页
      if (searchConditions) {
        this.paginationConfig.pageIndex = 1
      }
      this.fetchData(searchConditions || this.currentSearchConditions)
    },

    // 重置数据
    reset() {
      // 清空当前搜索条件
      this.currentSearchConditions = {}
      // 重置页码到第一页
      this.paginationConfig.pageIndex = 1
      // 重新获取数据
      this.fetchData({})
    }
  },
  created() {
    this.fetchData()
  }
}
</script>

<style lang="scss" scoped>
.package-type-table {
  height: 100%;
}
</style>
