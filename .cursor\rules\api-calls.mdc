---
description: 
globs: 
alwaysApply: true
---
# API调用规范

本项目使用axios进行API调用。

## API目录结构
- [api/](mdc:src/api) 目录包含所有API请求
- 按模块或功能划分不同的API文件

## 请求方式
项目中API请求通常遵循以下模式：

```js

import { httpGet, httpPost, httpPostDownload, httpPostUpload } from '@/utils/http'
import qs from 'qs'

/**
 * 新建广告
 * @param {Object} adInfo
 * @returns {Promise}
 */
export function _createAd(adInfo) {
  return httpPost('/client/ads/create.do', adInfo)
}

/**
 * 更新广告
 * @param {Object} adInfo
 * @returns {Promise}
 */
export function _updateAd(adInfo) {
  return httpPost('/client/ads/update.do', adInfo)
}
```

## 命名规范
api接口通常习惯以下划线开始，同时具有语义化，如：
```js
export function _updateAd(adInfo) {
  return httpPost('/client/ads/update.do', adInfo)
}

```

## 使用示例
在组件中使用API：

```js
import { fetchData } from '@/api/some-module'

export default {
  data() {
    return {
      listData: []
    }
  },
  methods: {
    async getData() {
      try {
        const res = await fetchData({ page: 1 })
        this.listData = res.data
      } catch (error) {
        console.error(error)
      }
    }
  },
  created() {
    this.getData()
  }
}
```
