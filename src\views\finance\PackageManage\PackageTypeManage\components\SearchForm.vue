<template>
  <!-- 搜索表单 -->
  <div class="search-form">
    <el-form :inline="true" :model="searchConditions" class="search-form-inline">
      <el-form-item label="套餐类型">
        <el-input v-model="searchConditions.packTypeName" placeholder="请输入套餐类型名称" clearable style="width: 200px" />
      </el-form-item>
      <el-form-item label="设备型号">
        <el-select
          v-model="searchConditions.machineTypeIds"
          placeholder="请选择设备型号"
          clearable
          multiple
          filterable
          collapse-tags
          style="width: 250px"
          @change="handleChange"
        >
          <div style="padding: 8px 20px; border-bottom: 1px solid #e4e7ed;">
            <el-checkbox :value="isAllSelected">
              全选
            </el-checkbox>
          </div>
          <el-option-group v-for="group in deviceOptions" :key="group.label" :label="group.label">
            <el-option v-for="item in group.options" :key="item.machineTypeId" :label="item.machineTypeName" :value="item.machineTypeId" />
          </el-option-group>
        </el-select>
      </el-form-item>
      <el-form-item label="启用状态">
        <el-select v-model="searchConditions.enableStatus" placeholder="请选择启用状态" clearable style="width: 200px" @change="handleChange">
          <el-option label="启用" :value="true" />
          <el-option label="禁用" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
      <el-button class="ml-30 swd-search-header-container-right" type="primary" @click="handleAdd">新增</el-button>
    </el-form>
  </div>
</template>

<script>
import { processDeviceListForMultiSelect, isAllDevicesSelected } from '@/utils/deviceHelper'
import { _getMachineType } from '@/api/packageType'
import { isAbroad } from '@/utils/judge'
export default {
  data() {
    return {
      searchConditions: {
        machineTypeIds: []
      },
      deviceOptions: [],
      allDeviceIds: [],
      sceneOptions: []
    }
  },
  computed: {
    defaultLang() {
      return isAbroad() ? 'en' : 'zh'
    },
    // 是否全选
    isAllSelected() {
      return isAllDevicesSelected(this.searchConditions.machineTypeIds, this.allDeviceIds)
    }
  },
  created() {
    this.fetchDeviceOptions()
  },
  methods: {
    handleSearch() {
      this.$emit('search', this.searchConditions)
    },
    handleReset() {
      // 清空自己的搜索条件
      this.searchConditions = {
        machineTypeIds: []
      }
      // 通知父组件进行重置
      this.$emit('reset')
    },
    handleAdd() {
      this.$emit('add')
    },

    // 统一处理条件变化
    handleChange() {
      this.handleSearch()
    },

    // 处理全选/取消全选
    handleSelectAll(checked) {
      if (checked) {
        // 全选：将所有设备ID赋值给选中数组
        this.searchConditions.machineTypeIds = [...this.allDeviceIds]
      } else {
        // 取消全选：清空选中数组
        this.searchConditions.machineTypeIds = []
      }
      this.handleChange()
    },

    async fetchDeviceOptions() {
      try {
        const res = await _getMachineType()
        if (res.ret === 1 && res.data) {
          const { options, allDeviceIds } = processDeviceListForMultiSelect(res.data)
          this.deviceOptions = options
          this.allDeviceIds = allDeviceIds
        }
      } catch (error) {
        console.error('获取设备列表失败:', error)
        this.$message.error('获取设备列表失败，请刷新重试')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.ml-30 {
  margin-left: 30px;
}
.swd-search-header-container-right {
  float: right;
}
</style>
