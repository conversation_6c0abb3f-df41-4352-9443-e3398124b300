/* 
    广告管理
*/
import { httpGet, httpPost, httpPostDownload, httpPostUpload } from '@/utils/http'
import qs from 'qs'

/**
 * 新建广告
 * @param {Object} adInfo
 * @returns {Promise}
 */
export function _createAd(adInfo) {
  return httpPost('/client/ads/create.do', adInfo)
}

/**
 * 更新广告
 * @param {Object} adInfo
 * @returns {Promise}
 */
export function _updateAd(adInfo) {
  return httpPost('/client/ads/update.do', adInfo)
}

/**
 * 删除广告
 * @param {number} id 广告ID
 * @returns {Promise}
 */
export function _deleteAd(id) {
  return httpPost(`/client/ads/delete.do?id=${id}`)
}

/**
 * 查询广告列表
 * @param {Object} params
 * @returns {Promise}
 */
export function _fetchAdList(params) {
  return httpPost('/client/ads/list.do', params)
}

/**
 * 查询广告详情
 * @param {number} id 广告ID
 * @returns {Promise}
 */
export function _fetchAdDetail(id) {
  return httpPost(`/client/ads/detail.do?id=${id}`)
}

/**
 * 更新广告状态
 * @param {Object} statusInfo
 * @returns {Promise}
 */
export function _updateAdStatus(statusInfo) {
  return httpPost('/client/ads/status/update.do', statusInfo)
}

/**
 * 查询广告数据
 * @param {Object} params
 * @returns {Promise}
 */
export function _fetchAdData(params) {
  return httpPost('/client/ads/data.do', params)
}

/**
 * 查询广告用户数
 * @param {Object} params
 * @returns {Promise}
 */
export function _fetchAdUserCount(params) {
  return httpPost('/client/ads/user/count.do', params)
}

/**
 * 查询现有广告列表
 * @returns {Promise}
 */
export function _fetchExistAdList() {
  return httpGet('/client/ads/simple/list.do')
}
