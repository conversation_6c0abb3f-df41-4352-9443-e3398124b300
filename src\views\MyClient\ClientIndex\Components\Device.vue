<template>
  <div class="client-tab-panel-container">
    <div class="device-operate">
      <div class="flex center">
        <base-button
          class="c-device__button"
          type="default"
          icon="sale"
          change-icon
          @click="deviceOperate({ type: 'sale' })"
          v-if="permissionArr.indexOf('sale:batch') >= 0"
          :content="$t('lg.limits.Bulk_sales')"
          >{{ $t('lg.limits.Bulk_sales') }}</base-button
        >
        <base-button
          class="c-device__button__renewal"
          type="default"
          change-icon
          icon="money"
          @click="deviceOperate({ type: 'renew' })"
          v-if="userType !== 2 && permissionArr.indexOf('account:batch:renew') !== -1"
          :content="$t('lg.limits.Batch_renewal')"
          >{{ $t('lg.limits.Batch_renewal') }}</base-button
        >
        <base-button
          class="c-device__button__import"
          type="default"
          change-icon
          icon="import"
          @click="deviceOperate({ type: 'import' })"
          v-if="userType !== 2"
          :content="$t('lg.limits.Batch_Import')"
          >{{ $t('lg.limits.Batch_Import') }}</base-button
        >
        <base-button
          class="c-device__button__export"
          type="default"
          change-icon
          icon="upGradation"
          @click="deviceOperate({ type: 'upGradation' })"
          v-if="permissionArr.indexOf('car:manager:upgrade') >= 0"
          :content="$t('lg.limits.car_manager_upgrade')"
          >{{ $t('lg.limits.car_manager_upgrade') }}</base-button
        >
        <el-button @click="deviceOperate({ type: 'group' })" v-if="userType === 2 && permissionArr.indexOf('device:group:list') >= 0"
          ><svg-icon icon-class="device-group"></svg-icon>{{ $t('3bKE4RZjKEEnP-6Lvd3QO') }}</el-button
        >
        <el-button @click="deviceOperate({ type: 'renew' })" v-if="userType === 2">
          <svg-icon icon-class="device_batch_renew"></svg-icon>{{ $t('lg.logDict.renew') }}</el-button
        >
        <base-dropdown style="margin-left:24px;" v-if="userType !== 2 && showGroupFlag">
          <el-button type="text"
            >{{ $t('lg.limits.More') }}
            <svg-icon icon-class="Down" class="Down-size"></svg-icon>
          </el-button>
          <base-dropdown-menu slot="dropdown">
            <base-dropdown-item @click.native="deviceOperate({ type: 'transfer' })" v-if="permissionArr.indexOf('device:transfer:batch') >= 0"
              ><svg-icon style="margin-right:8px;" icon-class="client_transfer_2"></svg-icon>{{ $t('0HfLeevl06T5-c-6Hybn9') }}</base-dropdown-item
            >
            <base-dropdown-item
              class="flex center"
              @click.native="deviceOperate({ type: 'police' })"
              v-if="permissionArr.indexOf('device:transfer:batch') >= 0"
            >
              <pc-police style="margin-right:8px;" size="16px" />{{ $t('batchAlarmSettings') }}</base-dropdown-item
            >
            <base-dropdown-item
              class="flex center"
              @click.native="deviceOperate({ type: 'instructions' })"
              v-if="permissionArr.indexOf('device:transfer:batch') >= 0"
            >
              <pc-instruction style="margin-right:8px;" size="16px" />{{ $t('batchInstructions') }}</base-dropdown-item
            >
            <base-dropdown-item @click.native="deviceOperate({ type: 'editInfo' })" v-if="permissionArr.indexOf('device:update') >= 0"
              ><svg-icon style="margin-right:8px;" icon-class="device_edit_info"></svg-icon>{{ $t('RIiHXilwzDZZXOC0RFjS4') }}</base-dropdown-item
            >
            <base-dropdown-item @click.native="deviceOperate({ type: 'editModel' })" v-if="permissionArr.indexOf('device:update:batch:model') >= 0"
              ><svg-icon style="margin-right:8px;" icon-class="device_edit"></svg-icon>{{ $t('pWAH6OlawcOyPzpCQiD3t') }}</base-dropdown-item
            >
            <base-dropdown-item @click.native="deviceOperate({ type: 'editServiceTime' })" v-if="permissionArr.indexOf('device:update:user:expire') >= 0"
              ><svg-icon style="margin-right:8px;" icon-class="device_edit_expire"></svg-icon>{{ $t('lg.limits.Modify_user_expiration') }}</base-dropdown-item
            >
            <base-dropdown-item @click.native="deviceOperate({ type: 'delete' })" v-if="permissionArr.indexOf('device:delete') >= 0"
              ><svg-icon style="margin-right:8px;" icon-class="device_delete"></svg-icon>{{ $t('lg.limits.Delete_device') }}</base-dropdown-item
            >
            <base-dropdown-item @click.native="deviceOperate({ type: 'group' })" v-if="permissionArr.indexOf('device:group:list') >= 0"
              ><svg-icon style="margin-right:8px;" icon-class="device-group"></svg-icon>{{ $t('3bKE4RZjKEEnP-6Lvd3QO') }}</base-dropdown-item
            >
            <base-dropdown-item @click.native="deviceOperate({ type: 'task' })">{{ $t('批量设置定时任务') }}</base-dropdown-item>
          </base-dropdown-menu>
        </base-dropdown>
      </div>
      <div class="inside-device-search-panel">
        <el-checkbox class="device-search-include-sub" v-model="checkboxStatus" @change="checkSubStatus">{{ $t('Z8MQX4KbfoflrmZEy5Vj6') }}</el-checkbox>
        <DeviceSearch ref="device_search" @search="deviceAdvancedSearch" @show-batch-dialog="showBatchSearchDialog"></DeviceSearch>
        <el-button type="text" @click="showMoreDeviceSearchCondition = !showMoreDeviceSearchCondition"
          >{{ showMoreDeviceSearchCondition ? $t('EG4bzTy443aMfL9yHir_p') : $t('XYgitqAwgNUZ_WMaw417U') }}
          <svg-icon
            icon-class="Down"
            class="Down-size devide-search-Mul-Con"
            :class="{ 'devide-search-Mul-Con-roate': showMoreDeviceSearchCondition }"
          ></svg-icon>
        </el-button>
      </div>
    </div>
    <div>
      <AdvanceDeviceSearch ref="AdvanceDeviceSearch" @reset="deviceSearchReset" @confirm="multiConditionSearch" :visible.sync="showMoreDeviceSearchCondition" />
    </div>
    <div class="device-manage">
      <el-table
        v-loading="loading"
        fit
        :data="deviceData"
        :height="tableHeight"
        ref="deviceTable"
        class="bueatyScroll"
        header-row-class-name="common-table-tr"
        @select="handleSelectionChange"
        @select-all="handleSelection"
        @filter-change="filterChange"
        @sort-change="sortDeviceTable"
      >
        <el-table-column type="selection" width="42" align="center"> </el-table-column>
        <el-table-column type="index" width="70">
          <template v-slot:header>
            <el-tooltip :content="$t('lg.serial')" placement="top-start" :disabled="$t('lg.serial').length < 10">
              <span>{{ $t('lg.serial') }}</span>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            {{ (deviceSearchParam.pageIndex - 1) * deviceSearchParam.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="machineName" min-width="140">
          <template v-slot:header>
            <el-tooltip :content="$t('lg.machineName')" placement="top-start" :disabled="$t('lg.machineName').length < 10">
              <span>{{ $t('lg.machineName') }}</span>
            </el-tooltip>
          </template>
          <template v-slot="scope">
            {{ scope.row.machineName | EmptyIntoLine }}
          </template>
        </el-table-column>
        <el-table-column prop="imei" label="IMEI" min-width="168">
          <template v-slot="{ row }">
            <div class="device-imei-container">
              <span class="device-imei" @click.stop="openDetailDialog(row)">{{ row.imei }}</span>
              <el-tooltip effect="dark" :content="$t('r9QdHdk_7rac9hv4aw9cl')" placement="top">
                <img class="copy-imei" @click.stop="copyValue(row.imei, $event)" src="@/assets/img/icon/copy.png" />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <!-- 套餐类型 -->
        <el-table-column v-if="columns.toClientServiceType && !isTestUser" :label="$t('0rxp9_tAb1nkVwt7ZfEG9')" min-width="140" key="21">
          <template #header>
            <el-tooltip :content="$t('0rxp9_tAb1nkVwt7ZfEG9')" placement="top-start">
              <span>{{ $t('0rxp9_tAb1nkVwt7ZfEG9') }}</span>
            </el-tooltip>
          </template>
          <template v-slot="{ row }">
            <span v-if="row.clientServiceStatus" style="color: #3370ff;cursor: pointer" @click="openPackageDetail(row)">{{
              row.clientServiceStatus | packServiceFilter
            }}</span>
            <span style="margin-left:25px" v-else>-</span>
          </template>
        </el-table-column>
        <!--绑定状态-->
        <el-table-column key="25" v-if="columns.carBoundStatus" min-width="80">
          <template v-slot:header>
            <el-tooltip :content="$t('gQI5MVNOUbZlMcC0kHzKK')" placement="top-start" :disabled="$t('n_gGRUBDnHrrFFZNMBWQF').length < 10">
              <span>{{ $t('gQI5MVNOUbZlMcC0kHzKK') }}</span>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <div v-if="scope.row.carBoundStatus !== undefined">
              {{ scope.row.carBoundStatus === 1 ? $t('iVMYDWp97LRmLGrbbnnsD') : $t('Oeez9vYceazcpBOlH3Xq-') }}
            </div>
            <div v-else>
              —
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="columns.packageVision" width="220" prop="packVersion" :label="$t('套餐版本')">
          <template v-slot="{ row }"> {{ getCurrentPackageVersion(row.packVersion) }}</template>
        </el-table-column>
        <el-table-column v-if="columns.simNO" prop="simNO" min-width="140" key="3">
          <template v-slot:header>
            <el-tooltip :content="$t('2WVzsA1lduRG2ofkXI1gv')" placement="top-start" :disabled="$t('2WVzsA1lduRG2ofkXI1gv').length < 10">
              <span>{{ $t('2WVzsA1lduRG2ofkXI1gv') }}</span>
            </el-tooltip>
          </template>
          <template v-slot="scope">
            {{ scope.row.simNO | EmptyIntoLine }}
          </template>
        </el-table-column>
        <el-table-column v-if="columns.carNO" prop="carNO" min-width="140" key="4">
          <template v-slot:header>
            <el-tooltip :content="$t('p533TIv1HaDSMgWw1t4a_')" placement="top-start" :disabled="$t('p533TIv1HaDSMgWw1t4a_').length < 10">
              <span>{{ $t('p533TIv1HaDSMgWw1t4a_') }}</span>
            </el-tooltip>
          </template>
          <template v-slot="scope">
            {{ !scope.row.carNO || scope.row.machineTypeAscription == 1 ? '-' : scope.row.carNO }}
          </template>
        </el-table-column>
        <el-table-column key="22" v-if="columns.petName" prop="carNO" min-width="140">
          <template v-slot:header>
            <el-tooltip :content="$t('V7ZEeKcoq4ZacqGumFBso')" placement="top-start" :disabled="$t('V7ZEeKcoq4ZacqGumFBso').length < 10">
              <span>{{ $t('V7ZEeKcoq4ZacqGumFBso') }}</span>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            {{ !scope.row.carNO || scope.row.machineTypeAscription == 2 ? '-' : scope.row.carNO }}
          </template>
        </el-table-column>
        <el-table-column v-if="columns.iccid" prop="iccid" label="Iccid" min-width="180">
          <template slot-scope="{ row }">
            {{ row.iccid || '-' }}
          </template>
        </el-table-column>
        <el-table-column key="5" v-if="columns.machineType">
          <template v-slot:header>
            <el-tooltip :content="$t('PdDwaW3ZMpo7a1kMh1QTQ')" placement="top-start" :disabled="$t('PdDwaW3ZMpo7a1kMh1QTQ').length < 10">
              <span>{{ $t('PdDwaW3ZMpo7a1kMh1QTQ') }}</span>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            {{ scope.row.machineType | machineType | EmptyIntoLine }}
          </template>
        </el-table-column>
        <!--                  状态-->
        <el-table-column
          key="6"
          width="120px"
          align="left"
          v-if="columns.serviceState"
          :label="$t('lg.status')"
          column-key="serviceState"
          :filter-multiple="false"
          :filters="
            treeType == 'all'
              ? [
                  { text: $t('flK64mYHgrj-k7-D2XupZ'), value: 'online' },
                  { text: $t('dtFn22xfEx789uFKvaG_n'), value: 'offline' },
                  { text: $t('M_dYy2C4ldXbU0GcSh8XQ'), value: 'unused' },
                  { text: $t('xg72stb27826td26d1276'), value: 'expire' }
                ]
              : [
                  { text: $t('flK64mYHgrj-k7-D2XupZ'), value: 'online' },
                  { text: $t('dtFn22xfEx789uFKvaG_n'), value: 'offline' },
                  { text: $t('M_dYy2C4ldXbU0GcSh8XQ'), value: 'unused' }
                ]
          "
        >
          <template v-slot:header>
            <span>{{ $t('lg.status') }}<svg-icon icon-class="down_full" className="device_status_filter"></svg-icon></span>
          </template>
          <template slot-scope="scope">
            <svg-icon v-if="scope.row.class == 'online'" className="device_status_icon" icon-class="device_status_online"></svg-icon>
            <svg-icon v-if="scope.row.class != 'online'" className="device_status_icon" icon-class="device_status_offLine" :class="scope.row.class"></svg-icon>
            <span :class="scope.row.class">{{ scope.row.deviceStateStatus }}</span>
          </template>
        </el-table-column>
        <!--                  今日里程-->
        <el-table-column key="7" v-if="columns.dayMileage" prop="dayMileage" min-width="135" sortable="custom">
          <template v-slot:header>
            <el-tooltip :content="$t('XsXoMT3CDfSMMxm_H8UGU')" placement="top-start" :disabled="$t('XsXoMT3CDfSMMxm_H8UGU').length < 10">
              <span>{{ $t('XsXoMT3CDfSMMxm_H8UGU') }}</span>
            </el-tooltip>
          </template>
          <template v-slot="scope">
            {{ (scope.row.dayMileage / 1000) | EmptyIntoLine }}
          </template>
        </el-table-column>
        <!--                  电压-->
        <el-table-column key="8" v-if="columns.voltage" min-width="80">
          <template v-slot:header>
            <el-tooltip :content="$t('n_gGRUBDnHrrFFZNMBWQF')" placement="top-start" :disabled="$t('n_gGRUBDnHrrFFZNMBWQF').length < 10">
              <span>{{ $t('n_gGRUBDnHrrFFZNMBWQF') }}</span>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <div v-if="scope.row.carStatus">
              {{ scope.row | voltageFilter }}
            </div>
            <div v-else>
              —
            </div>
          </template>
        </el-table-column>
        <!--超速阈值-->
        <el-table-column key="9" v-if="columns.overSpeedValue" prop="overSpeedValue" min-width="135" align="left">
          <template v-slot:header>
            <el-tooltip :content="$t('7ddIPjWcWGS0crmj1wera')" placement="top-start" :disabled="$t('7ddIPjWcWGS0crmj1wera').length < 10">
              <span>{{ $t('7ddIPjWcWGS0crmj1wera') }}</span>
            </el-tooltip>
          </template>
          <template v-slot="scope">
            {{ scope.row.overSpeedValue | EmptyIntoLine }}
          </template>
        </el-table-column>
        <!--                  销售时间-->
        <el-table-column key="10" v-if="columns.saleTime" prop="saleTime" min-width="145" sortable="custom">
          <template v-slot:header>
            <el-tooltip :content="$t('lg.saleTime')" placement="top-start" :disabled="$t('lg.saleTime').length < 10">
              <span>{{ $t('lg.saleTime') }}</span>
            </el-tooltip>
          </template>
          <template v-slot="scope">
            {{ scope.row.saleTime | dataFilter }}
          </template>
        </el-table-column>
        <!--                  上线时间-->
        <el-table-column key="11" v-if="columns.activeTime" prop="activeTime" min-width="140" sortable="custom">
          <template v-slot:header>
            <el-tooltip :content="$t('2JODYAuyHeVxEZXdVhZAt')" placement="top-start" :disabled="$t('2JODYAuyHeVxEZXdVhZAt').length < 10">
              <span>{{ $t('2JODYAuyHeVxEZXdVhZAt') }}</span>
            </el-tooltip>
          </template>
          <template v-slot="scope">
            {{ scope.row.activeTime | dataFilter }}
          </template>
        </el-table-column>
        <!--                  用户到期-->
        <el-table-column key="12" v-if="columns.serviceTime" prop="serviceTime" min-width="140" sortable="custom">
          <template v-slot:header>
            <el-tooltip :content="$t('pp7ggW3s8XBX7RhFkk1_v')" placement="top-start" :disabled="$t('pp7ggW3s8XBX7RhFkk1_v').length < 10">
              <span>{{ $t('pp7ggW3s8XBX7RhFkk1_v') }}</span>
            </el-tooltip>
          </template>
          <template v-slot="scope">
            {{ scope.row.serviceTime | EmptyIntoLine }}
          </template>
        </el-table-column>
        <!--                  平台到期-->
        <el-table-column key="13" v-if="userInfo.userType <= 1 && columns.platformTime" prop="platformTime" min-width="140" sortable="custom">
          <template v-slot:header>
            <el-tooltip :content="$t('xe_DoA8ge_gmef88_vh4p')" placement="top-start" :disabled="$t('xe_DoA8ge_gmef88_vh4p').length < 10">
              <span>{{ $t('xe_DoA8ge_gmef88_vh4p') }}</span>
            </el-tooltip>
          </template>
          <template v-slot="scope">
            {{ scope.row.platformTime | EmptyIntoLine }}
          </template>
        </el-table-column>
        <!--                  导入时间-->
        <el-table-column key="14" v-if="columns.joinTime" prop="joinTime" min-width="140" sortable="custom">
          <template v-slot:header>
            <el-tooltip :content="$t('gEN4YPGMoNz8HYKg2kgMi')" placement="top-start" :disabled="$t('gEN4YPGMoNz8HYKg2kgMi').length < 10">
              <span>{{ $t('gEN4YPGMoNz8HYKg2kgMi') }}</span>
            </el-tooltip>
          </template>
          <template v-slot="scope">
            {{ scope.row.joinTime | dataFilter }}
          </template>
        </el-table-column>
        <!--油耗-->
        <el-table-column key="15" v-if="columns.fuelConSumption" prop="fuelConSumption" min-width="140">
          <template v-slot:header>
            <el-tooltip :content="$t('5NW3PUUAmaqAZxIV48c2V')" placement="top-start" :disabled="$t('5NW3PUUAmaqAZxIV48c2V').length < 10">
              <span>{{ $t('5NW3PUUAmaqAZxIV48c2V') }}</span>
            </el-tooltip>
          </template>
          <template v-slot="scope">
            {{ scope.row.fuelConSumption | EmptyIntoLine }}
          </template>
        </el-table-column>
        <!--                  温度-->
        <el-table-column key="16" v-if="columns.temp" min-width="140">
          <template v-slot:header>
            <el-tooltip :content="$t('hs24f25MR-Kkr7mTiNgrI')" placement="top-start" :disabled="$t('hs24f25MR-Kkr7mTiNgrI').length < 10">
              <span>{{ $t('hs24f25MR-Kkr7mTiNgrI') }}</span>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <div v-if="scope.row.carStatus">
              {{ scope.row | tempFilter | EmptyIntoLine }}
            </div>
          </template>
        </el-table-column>
        <el-table-column key="17" v-if="columns.icon" prop="icon" min-width="70">
          <template v-slot:header>
            <el-tooltip :content="$t('g0Z4WYTOSoNVw6icvU8Xa')" placement="top-start" :disabled="$t('g0Z4WYTOSoNVw6icvU8Xa').length < 10">
              <span>{{ $t('g0Z4WYTOSoNVw6icvU8Xa') }}</span>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <img
              :src="scope.row.icon"
              :class="{ 'pet-icon': scope.row.carType === 33 || scope.row.carType === 34, 'pet-default-icon': scope.row.carType === 22 }"
            />
          </template>
        </el-table-column>
        <!--                  最后位置-->
        <el-table-column key="18" v-if="columns.lastLocation" min-width="140">
          <template v-slot:header>
            <el-tooltip :content="$t('uWFemTPKIbGOyQ17ugfkE')" placement="top-start" :disabled="$t('uWFemTPKIbGOyQ17ugfkE').length < 10">
              <span>{{ $t('uWFemTPKIbGOyQ17ugfkE') }}</span>
            </el-tooltip>
          </template>
          <template slot-scope="{ row }">
            <div>
              <i v-if="row.isLoading" class="el-icon-loading"></i>
              <span v-else-if="row.carStatus && row.location === undefined" @click="showAddress(row)" class="ljdw-color-blue cursor-point">{{
                $t('vBpDexpv7D_KNy_aaxmZx')
              }}</span>
              <span v-else>{{ row.location || '-' }}</span>
            </div>
          </template>
        </el-table-column>
        <!--                 分组-->
        <el-table-column key="19" v-if="columns.group" min-width="140">
          <template v-slot:header>
            <el-tooltip :content="$t('3bKE4RZjKEEnP-6Lvd3QO')" placement="top-start" :disabled="$t('3bKE4RZjKEEnP-6Lvd3QO').length < 10">
              <span>{{ $t('3bKE4RZjKEEnP-6Lvd3QO') }}</span>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <el-select @change="handleGroupChange($event, scope.row)" v-model="scope.row.carGroupId" class="device-group-select"
              ><el-option v-for="item in carGroup" :key="item.carGroupId" :value="item.carGroupId" :label="item.name"></el-option
            ></el-select>
          </template>
        </el-table-column>
        <el-table-column key="20" v-if="columns.remark" prop="remark" min-width="140" show-overflow-tooltip>
          <template v-slot:header>
            <el-tooltip :content="$t('lg.remark')" placement="top-start" :disabled="$t('lg.remark').length < 10">
              <span>{{ $t('lg.remark') }}</span>
            </el-tooltip>
          </template>
          <template v-slot="scope">
            {{ scope.row.remark | EmptyIntoLine }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('lg.operator')" fixed="right" min-width="170" align="center">
          <template v-slot:header>
            <span @click="openCustomColumn"
              >{{ $t('lg.operator') }}<svg-icon style="color:#3399FF;width:11px;cursor:pointer;" icon-class="shaixuantiaojian" class="moreOperation"></svg-icon
            ></span>
          </template>
          <template slot-scope="scope">
            <span
              class="swd-default-a"
              v-if="userInfo.userType !== 2 && permissionArr.indexOf('sale:batch') > 0"
              @click.stop="deviceOperate({ type: 'sale', selectedDevice: [scope.row] })"
              >{{ $t('lg.logDict.sale') }}</span
            >
            <span class="swd-default-split" v-if="userInfo.userType !== 2 && permissionArr.indexOf('sale:batch') > 0"> | </span>
            <span class="swd-default-a" @click.stop="openDetailDialog(scope.row)">{{ $t('lg.details') }}</span>
            <span class="swd-default-split"> | </span>
            <base-dropdown v-if="deviceOperaMore">
              <span class="swd-default-a" @click.stop
                >{{ $t('lg.limits.More') }}
                <svg-icon style="color:#3399FF;" icon-class="Down" class="Down-size"></svg-icon>
              </span>
              <base-dropdown-menu slot="dropdown">
                <!--                          追踪-->
                <base-dropdown-item v-if="permissionArr.indexOf('device:monitor:track') > 0" @click.native="handleDeviceDropdown('locate', scope.row)">{{
                  $t('2_GOBlwzPbyr5HQDEsUOK')
                }}</base-dropdown-item>
                <!--                          回放-->
                <base-dropdown-item v-if="permissionArr.indexOf('device:monitor:playback') > 0" @click.native="handleDeviceDropdown('replay', scope.row)">{{
                  $t('FNZ9KgOe3_nZPHLr-gemV')
                }}</base-dropdown-item>
                <!-- 新增续费 -->
                <base-dropdown-item @click.native="handleDeviceDropdown('renew', scope.row)" v-if="userInfo.userType !== 2">
                  {{ $t('lg.logDict.renew') }}
                </base-dropdown-item>
                <!-- 权限续费问产品 -->
                <base-dropdown-item
                  @click.native="handleDeviceDropdown('charge', scope.row)"
                  v-if="userInfo.userType !== 2 && permissionArr.indexOf('device:renew') > 0"
                  >{{ $t('lg.logDict.renew') }}</base-dropdown-item
                >
                <base-dropdown-item @click.native="userCharge" v-if="userInfo.userType === 2">{{ $t('lg.logDict.renew') }}</base-dropdown-item>
                <!--                          转移-->
                <base-dropdown-item
                  @click.native="handleDeviceDropdown('transfer', scope.row)"
                  v-if="userInfo.userType !== 2 && permissionArr.indexOf('device:transfer:batch') > 0"
                  >{{ $t('u8042nAxHpYgsu7a58sQ6') }}</base-dropdown-item
                >
                <!--                          重置密码-->
                <base-dropdown-item
                  @click.native="handleDeviceDropdown('reset', scope.row)"
                  v-if="userInfo.userType !== 2 && permissionArr.indexOf('device:reset:password') > 0"
                  >{{ $t('lg.resetPsw') }}</base-dropdown-item
                >
              </base-dropdown-menu>
            </base-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页器 -->
      <div class="swd-pagination-container">
        <div class="swd-pagination-container__cancel">
          <!--          <el-popconfirm :title="$t('是否确认取消选中')" popper-class="swd-pagination-popconfirm" @confirm="handleConfirm">-->
          <!--            <template #reference>-->
          <!--              <span class="cursor-point ljdw-color-blue">{{ $t('取消选中') }}</span>-->
          <!--            </template>-->
          <!--          </el-popconfirm>-->
          <span class="swd-pagination-container__selected">{{ $t('已选中') }}：{{ selectedDevice.length }}</span>
        </div>
        <base-pagination
          :current-page.sync="deviceSearchParam.pageIndex"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="deviceSearchParam.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="deviceTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <!-- <span class="pagination_total">{{ $t('-mwXOBzafJJzS5ECa96HK', [deviceTotal]) }}</span> -->
        </base-pagination>
        <el-tooltip :content="$t('8Hy4f3sEGqYcZA0E2Tgwm')" placement="top">
          <el-button type="primary" @click="handleExport" class="swd-download-btn">
            <svg-icon icon-class="client_download" class="download-icon"></svg-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 批量查询 -->
    <BatchSearch ref="DeviceBatchSearch" @batch-search="deviceAdvancedSearch"></BatchSearch>

    <!-- 自定义列 -->
    <CustomColumn @change="changeColumns" :customColumnsIndex="columnsIndex" :customColumns="columns" :visible.sync="showCustomColumn" />
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import handleCopy from '@/utils/clipboard'
import DeviceSearch from '@/components/Device/DeviceSearch' //  设备搜索
import BatchSearch from '@/components/Device/DeviceBatchSearch' // 设备批量搜索
import AdvanceDeviceSearch from '@/components/Device/AdvanceDeviceSearch' // 更多条件
import CustomColumn from '@/components/Device/CustomColumn' // 自定义列
import { resetDevice, exportDeviceList, batchSearchDevice } from '@/api/device.js'
import { getParentUserInfo } from '@/api/system.js'
import { packagePath } from '@/constant/common'
import { TimeStringToUTC, pwdReserValue } from '@/utils/common.js'
import { loadAddress } from '@/utils/address.js'
import { queryUserPerFence } from '@/api/public'
import { getCarGroup, moveCarToGroup } from '@/api/monitor.js'
import { PcInstruction, PcPolice } from '@/assets/icon'
import publicUserpic from '@/assets/img/userpic.png'
import { PACKAGE_VERSION } from '@/assets/js/contants'

const isDev = process.env.NODE_ENV === 'development'
let _api = ''
if (isDev) {
  _api = '/api'
}
export default {
  props: {
    // 权限
    permissionArr: {
      type: Array,
      default: () => []
    },
    // 设备搜索参数
    searchParam: {
      type: Object,
      default: () => {}
    },
    // 树类型
    treeType: {
      type: String,
      default: 'all'
    },
    // 过期类型
    expireType: {
      type: [Number, String],
      default: null
    },
    // 过期时间
    expireTime: {
      type: [Number, String],
      default: null
    }
  },
  components: { DeviceSearch, AdvanceDeviceSearch, BatchSearch, CustomColumn, PcPolice, PcInstruction },
  data() {
    return {
      loading: false,
      deviceSearchParam: {
        // imei: '863058054594301',
        targetUserId: undefined,
        pageSize: 15,
        pageIndex: 1,
        subFlage: false,
        requestParames: undefined,
        // “0-今日里程”、“1-用户到期”、“2-平台到期”、“3-导入时间”、“ 4-销售时间”、“5-激活时间”
        orderBy: undefined,
        asc: undefined,
        status: '',
        expireType: undefined,
        expireMaxDay: undefined,
        machineType: undefined,
        machineName: undefined,
        clientServiceStatus: undefined
      },
      userType: null,
      showGroupFlag: false,
      showMoreDeviceSearchCondition: false, // 展开更多条件
      tableHeight: 'calc(100% - 52px)', // 表格高度
      moreSearchConditionHeight: 0, // 更多条件的高度
      checkboxStatus: false, // 包含下级
      selectedDevice: [], // 表格勾选项
      selectedId: [],
      deviceData: [], // 表格数据
      deviceTotal: 0, // 表格数据数量
      columns: {
        machineName: true,
        imei: true,
        simNO: false,
        carNO: false,
        machineType: false,
        serviceState: false,
        dayMileage: false,
        voltage: false,
        overSpeedValue: false,
        saleTime: false,
        activeTime: false,
        serviceTime: false,
        platformTime: false,
        fuelConSumption: false,
        temp: false,
        icon: false,
        lastLocation: false,
        group: false,
        remark: false,
        joinTime: false,
        toClientServiceType: false,
        petName: false,
        iccid: false,
        packageVision: false,
        carBoundStatus: true
      },
      showCustomColumn: false, // 自定义列弹窗
      deviceOperaMore: false, // 表格更多按钮
      // 设别列表的操作列
      columnsIndex: localStorage.getItem('table-custom-index') || '3,5,6,11,13,1,21,25',
      carGroup: [],
      // 根据自定义列 获取额外参数
      requestParames: []
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    ...mapState({
      isTestUser: state => state.user.isTestUser
    })
  },
  watch: {
    deviceSearchParam: {
      immediate: true,
      handler(val) {
        this.$emit('update:searchParam', val)
      },
      deep: true
    },
    showMoreDeviceSearchCondition(val) {
      if (val) {
        this.moreSearchConditionHeight = 70
      } else {
        this.moreSearchConditionHeight = 0
      }
    }
  },
  created() {
    let _arr = [...this.permissionArr]
    if (
      _arr.indexOf('device:transfer:batch') !== -1 ||
      _arr.indexOf('device:update') !== -1 ||
      _arr.indexOf('device:update:batch:model') !== -1 ||
      _arr.indexOf('device:update:batch:expire') !== -1 ||
      _arr.indexOf('device:delete') !== -1 ||
      _arr.indexOf('device:group:list') !== -1
    ) {
      this.showGroupFlag = true
    }

    if (
      _arr.indexOf('device:monitor:track') !== -1 ||
      _arr.indexOf('device:monitor:playback') !== -1 ||
      _arr.indexOf('device:transfer:batch') !== -1 ||
      _arr.indexOf('device:update:batch:expire') !== -1 ||
      _arr.indexOf('device:renew') !== -1 ||
      _arr.indexOf('device:reset:password') !== -1
    ) {
      this.deviceOperaMore = true
    }

    this.userType = parseInt(this.$cookies.get('userType'))

    this.columnsIndex.split(',').forEach(index => {
      this.columns[Object.keys(this.columns)[index - 1]] = true
    })
  },
  mounted() {
    // 获取设备列表表头
    this.getDeviceColumn()
    // 获取设备分组
    this.getCarGroupFun()
    // 获取设备列表数据
    // this.advancedSearchDeviceFun()
  },
  methods: {
    async handleGroupChange(carGroupId, { carId }) {
      await moveCarToGroup({
        carGroupId,
        carId
      })
      this.$message.success(this.$t('rVAoxxyYFdp_tC9Dnyb6M'))
    },
    handleConfirm() {
      this.selectedDevice = []
      this.selectedId = []
      this.$refs.deviceTable.clearSelection()
    },
    getCurrentPackageVersion(value) {
      const record = PACKAGE_VERSION.find(item => item.value === Number(value))
      return this.$t((record && record.introduction) || '-')
    },
    // 刷新设备表格
    refreshDeviceTable({ targetUserId }) {
      this.deviceSearchParam.targetUserId = targetUserId
      this.$refs['device_search'].reset()
      // 清空筛选条件
      this.deviceSearchParam.status = ''
      this.$refs.deviceTable.clearFilter()
      // 清除搜索状态
      this.clearSearchStatus()
      this.$refs.AdvanceDeviceSearch.searchStatusReset()
    },
    // 清除搜索状态
    clearSearchStatus() {
      this.deviceSearchParam.carNO = undefined
      this.deviceSearchParam.endTime = undefined
      this.deviceSearchParam.expireEndTime = undefined
      this.deviceSearchParam.expireMaxDay = undefined
      this.deviceSearchParam.expireStartTime = undefined
      this.deviceSearchParam.expireTimeType = undefined
      this.deviceSearchParam.expireType = undefined
      this.deviceSearchParam.machineName = undefined
      this.deviceSearchParam.imei = undefined
      this.deviceSearchParam.simNO = undefined
      this.deviceSearchParam.startTime = undefined
      this.deviceSearchParam.timeType = undefined
      this.deviceSearchParam.machineType = undefined
      this.deviceSearchParam.packVersion = undefined
    },
    // 点击包含下级
    checkSubStatus() {
      this.deviceSearchParam.pageIndex = 1
      this.deviceSearchParam.subFlage = this.checkboxStatus
      this.advancedSearchDeviceFun()
    },
    // 获取表格 分组列数据
    async getCarGroupFun(id) {
      let res = await getCarGroup({ userId: id })
      if (res.ret) {
        this.carGroup = res.data
        this.carGroup.forEach((item, i) => {
          if (item.carGroupId === 0) {
            this.carGroup[i].name = this.$t('Y9r-CsONpxxRmg00jzjLm')
          }
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设备搜索
    deviceAdvancedSearch(obj) {
      let { isBatch } = obj
      if (isBatch) {
        this.checkboxStatus = true
      }
      this.deviceSearchParam.status = ''
      this.deviceSearchParam.machineName = undefined
      this.deviceSearchParam.imei = undefined
      this.deviceSearchParam.imeiArray = undefined
      this.deviceSearchParam.simNO = undefined
      this.deviceSearchParam.simNoArray = undefined
      this.deviceSearchParam.carNO = undefined
      this.deviceSearchParam.expireType = undefined
      this.deviceSearchParam.expireMaxDay = undefined
      this.deviceSearchParam.timeType = undefined
      this.deviceSearchParam.startTime = undefined
      this.deviceSearchParam.endTime = undefined
      this.deviceSearchParam.expireTimeType = undefined
      this.deviceSearchParam.expireStartTime = undefined
      this.deviceSearchParam.expireEndTime = undefined
      this.deviceSearchParam.pageIndex = 1
      this.deviceSearchParam.machineType = undefined
      this.deviceSearchParam.clientServiceStatus = undefined
      this.deviceSearchParam.packVersion = undefined
      this.deviceSearchParam.carBoundStatus = undefined
      this.deviceSearchParam = Object.assign(this.deviceSearchParam, obj)
      this.advancedSearchDeviceFun()
    },
    // 获取设备数据
    async advancedSearchDeviceFun() {
      this.loading = true
      if (this.treeType === 'expire') {
        /*
         服务状态类型
         1：平台即将过期
         2：平台到期，
         3：用户即将过期，
         4：用户过期，
         5：有serviceTime过期数量（首页过期跳转），
         6：有serviceTime即将过期数量（首页即将过期跳转）
       */
        if (this.expireTime === 0 && this.expireType === 0) {
          this.deviceSearchParam.expireType = 4
        } else if (this.expireTime === 0 && this.expireType === 1) {
          this.deviceSearchParam.expireType = 2
        } else if (this.expireTime === 7 && this.expireType === 0) {
          this.deviceSearchParam.expireType = 3
        } else if (this.expireTime === 7 && this.expireType === 1) {
          this.deviceSearchParam.expireType = 1
        } else if (this.expireTime === 30 && this.expireType === 0) {
          this.deviceSearchParam.expireType = 3
        } else if (this.expireTime === 30 && this.expireType === 1) {
          this.deviceSearchParam.expireType = 1
        } else if (this.expireTime === 0 && this.expireType === 2) {
          // 服务过期== 用户过期
          this.deviceSearchParam.expireType = 4
        } else if (this.expireTime === 7 && this.expireType === 2) {
          // 服务即将过期 == 用户即将过期
          this.deviceSearchParam.expireType = 3
        } else if (this.expireTime === 30 && this.expireType === 2) {
          // 服务即将过期 == 用户即将过期
          this.deviceSearchParam.expireType = 3
        } else {
          this.deviceSearchParam.expireType = null
        }
        this.deviceSearchParam.expireMaxDay = this.expireTime
      }
      let params = { ...this.deviceSearchParam }

      let res = await batchSearchDevice(params)

      if (res.ret == 1) {
        this.deviceTotal = res.total
        this.deviceData = res.data
        this.deviceData.forEach(item => {
          if (!Object.prototype.hasOwnProperty.call(item, 'carStatus') || !item.carStatus) {
            //未用
            item.deviceStateStatus = this.$t('M_dYy2C4ldXbU0GcSh8XQ')
            item.class = 'inactive'
          } else if (item.serviceState == 1 || item.serviceState == 2) {
            // 新规则增加缓冲期状态 '2'
            //到期
            item.deviceStateStatus = this.$t('CzvhWCze3pQCK5M_1KoaB')
            item.class = 'expire'
          } else if (item.carStatus.online === 1) {
            //在线
            item.deviceStateStatus = this.$t('flK64mYHgrj-k7-D2XupZ')
            item.class = 'online'
          } else {
            //离线
            item.deviceStateStatus = this.$t('dtFn22xfEx789uFKvaG_n')
            item.class = 'offline'
          }
          if (!isDev) {
            item.icon = `${packagePath}/images/carIcon/` + item.carType + '.png'
          } else {
            item.icon = '/images/carIcon/' + item.carType + '.png'
          }
          this.$set(item, 'location', undefined)
          this.$set(item, 'isLoading', false)
        })
        this.$nextTick(() => {
          this.$refs.deviceTable.doLayout()
          this.selectedDevice.forEach(record => {
            this.$refs.deviceTable.toggleRowSelection(
              this.deviceData.find(item => item.carId === record.carId),
              true
            )
          })
        })
      } else {
        this.$message.error(res.msg)
      }

      this.loading = false
    },
    // 设备搜索重置
    deviceSearchReset() {
      this.showMoreDeviceSearchCondition = false
      this.$refs.device_search.mainSearchButtonReset() // 重置设备搜索主按钮
      this.checkboxStatus = false
      this.deviceSearchParam.subFlage = false
      this.deviceAdvancedSearch({})
    },
    // 更多条件搜索确定
    multiConditionSearch(obj) {
      this.showMoreDeviceSearchCondition = false
      this.deviceSearchParam.status = ''
      this.deviceSearchParam = Object.assign(this.deviceSearchParam, obj)
      this.deviceSearchParam.machineName = ''
      this.advancedSearchDeviceFun()
    },
    // 打开批量搜索弹窗
    showBatchSearchDialog() {
      this.$refs.DeviceBatchSearch.showBatchSearch = true
      this.$refs.DeviceBatchSearch.init()
    },
    // 表格勾选
    handleSelectionChange(e, row) {
      if (this.selectedId.includes(row.carId)) {
        this.selectedDevice = this.selectedDevice.filter(item => item.carId !== row.carId)
        this.selectedId = this.selectedId.filter(item => item !== row.carId)
      } else {
        this.selectedId.push(row.carId)
        this.selectedDevice.push(row)
      }
    },
    handleSelection(selections) {
      selections = selections.filter(item => item)
      const ids = !selections.length ? this.deviceData.map(item => item.carId) : selections.map(item => item.carId)
      if (!selections.length) {
        this.selectedId = this.selectedId.filter(item => !ids.includes(item))
        this.selectedDevice = this.selectedDevice.filter(item => !ids.includes(item.carId))
      } else {
        ids.forEach((id, index) => {
          if (!this.selectedId.includes(id)) {
            this.selectedId.push(id)
          }
          if (!this.selectedDevice.some(item => item.carId === id)) {
            this.selectedDevice.push(selections[index])
          }
        })
      }
    },
    clearSelection() {
      this.selectedDevice = []
      this.$refs.deviceTable.clearSelection()
    },
    // 表格排序
    sortDeviceTable(column) {
      switch (column.prop) {
        case 'dayMileage':
          this.deviceSearchParam.orderBy = 'dayMileage'
          break
        case 'serviceTime':
          this.deviceSearchParam.orderBy = 'serviceTime'
          break
        case 'platformTime':
          this.deviceSearchParam.orderBy = 'platformTime'
          break
        case 'joinTime':
          this.deviceSearchParam.orderBy = 'joinTime'
          break
        case 'saleTime':
          this.deviceSearchParam.orderBy = 'saleTime'
          break
        case 'activeTime':
          this.deviceSearchParam.orderBy = 'activeTime'
          break
        default:
          this.deviceSearchParam.orderBy = null
          break
      }
      if (column.order === null) {
        this.deviceSearchParam.asc = null
      } else if (column.order === 'descending') {
        this.deviceSearchParam.asc = false
      } else {
        this.deviceSearchParam.asc = true
      }
      this.advancedSearchDeviceFun()
    },
    // 设置设备状态并筛选查询
    setStatusAndFilter(filterObject) {
      let columns = this.$refs.deviceTable.columns
      columns.forEach(item => {
        if (item.columnKey === 'serviceState') item.filteredValue[0] = filterObject.serviceState[0] // 表格筛选选择
      })

      this.filterChange(filterObject)
    },
    // 表格过滤
    filterChange(filters) {
      this.deviceSearchParam.expireType = undefined
      this.deviceSearchParam.expireStartTime = undefined
      this.deviceSearchParam.expireEndTime = undefined

      if (filters['subFlage']) {
        this.deviceSearchParam.subFlage = this.checkboxStatus = true
      }
      if (filters['serviceState']) {
        let arr = filters['serviceState']
        if (!arr.length) {
          this.handleStatusChange('all')
        } else if (arr[0] === 'online') {
          this.handleStatusChange('online')
        } else if (arr[0] === 'offline') {
          this.handleStatusChange('offline')
        } else if (arr[0] === 'unused') {
          this.handleStatusChange('unused')
        } else if (arr[0] === 'expire') {
          // 表格状态过期过滤，跟用户信息资源总览的过期过滤一样
          let obj = {
            expireType: 5,
            expireStartTime: null,
            expireEndTime: null
          }
          obj.expireStartTime = '2001-07-18 00:00:00' // 国内时间
          obj.expireEndTime = TimeStringToUTC(new Date().Format('yyyy-MM-dd') + ' 00:00:00')
          this.deviceSearchParam = Object.assign(this.deviceSearchParam, obj)
          this.handleStatusChange('expire')
        }
      }
    },
    // 设备状态筛选查询
    handleStatusChange(status) {
      this.deviceSearchParam.pageIndex = 1 // 第一页
      switch (status) {
        case 'all':
          this.deviceSearchParam.status = ''
          this.advancedSearchDeviceFun()
          break
        case 'online':
          this.deviceSearchParam.status = 1
          this.advancedSearchDeviceFun()
          break
        case 'offline':
          this.deviceSearchParam.status = 2
          this.advancedSearchDeviceFun()
          break
        case 'unused':
          this.deviceSearchParam.status = 3
          this.advancedSearchDeviceFun()
          break
        case 'expire':
          this.deviceSearchParam.status = 4
          this.advancedSearchDeviceFun()
          break
        default:
          break
      }

      this.$emit('update:searchParam', this.deviceSearchParam)
    },
    // 更多操作-打开自定义列
    openCustomColumn() {
      this.showCustomColumn = true
    },
    // 复制imei
    copyValue(text, e) {
      let value = text
      handleCopy(
        value,
        e,
        () => {
          this.$message({
            message: this.$t('nEXvu2BRLQvSN2q4zNh16'),
            type: 'success',
            duration: 1000
          })
          // console.log('这是复制成功回调函数')
        },
        () => {
          // console.log('这是复制失败回调函数')
        }
      )
    },
    // 选择更多下拉选项
    deviceOperate({ type, selectedDevice }) {
      this.$emit('device-operate', { type, selectedDevice: selectedDevice || this.selectedDevice })
    },
    // 打开设备详情
    openDetailDialog(info) {
      this.$emit('open-device-detail', { activeName: 'detail', deviceInfo: info, deviceUserId: info.userId })
    },
    // 点击pageSize
    handleSizeChange(e) {
      this.deviceSearchParam.pageIndex = 1
      this.deviceSearchParam.pageSize = parseInt(e)
      this.advancedSearchDeviceFun()
    },
    // 点击页码
    handleCurrentChange(e) {
      this.deviceSearchParam.pageIndex = e
      this.advancedSearchDeviceFun()
    },
    //  导出
    handleExport() {
      if (this.deviceTotal > 10000) {
        this.$message.warning(this.$t('kO825bS74aibjof-TZHgI', [10000]))
        return
      }
      let param = this.deviceSearchParam
      param.exportType = 1
      exportDeviceList(param).then(res => {
        let a = document.createElement('a')
        let blob = new Blob([res], { type: 'application/ms-excel' })
        let objectUrl = URL.createObjectURL(blob)
        a.setAttribute('href', objectUrl)
        let fileName = new Date()
        fileName = fileName.getFullYear() + '' + (fileName.getMonth() + 1) + fileName.getDate()
        a.setAttribute('download', `${fileName}.xlsx`)
        a.click()
      })
    },
    // 表格-操作-更多
    handleDeviceDropdown(command, row) {
      switch (command) {
        // case 'detail':
        //   this.(row)
        //   break
        case 'locate':
          this.locateDevice(row)
          break
        case 'replay':
          this.replayTrack(row)
          break
        // case 'sale':
        //   this.singleSale(row)
        //   break
        case 'transfer':
          this.$emit('device-operate', { type: 'transfer', selectedDevice: [row] })
          break
        case 'charge':
          this.$emit('device-operate', { type: 'charge', selectedDevice: [row] })
          break
        case 'reset':
          this.resetDeviceFun(row)
          break
        case 'renew':
          this.$emit('open-device-detail', { activeName: 'recharge', deviceInfo: row })
          break
        default:
          break
      }
    },
    // 重置密码
    resetDeviceFun(row) {
      this.$confirm(this.$t('igFoXwa2BzYkyYAJDqU9R', [row.machineName]), '', {
        confirmButtonText: this.$t('lg.confirm'),
        cancelButtonText: this.$t('lg.cancel'),
        type: 'warning',
        showClose: false
      })
        .then(() => {
          // 生成随机密码
          let resetPwdValue = pwdReserValue()
          let resetAccount = row.imei
          resetDevice({ carId: row.carId, password: resetPwdValue }).then(res => {
            if (res.ret) {
              // 打开复制弹窗
              this.$emit('open-copy', { resetAccount, resetPwdValue })
              // this.$refs.Copy.copyDialog = true
            }
          })
        })
        .catch(() => {})
    },
    // 追踪
    locateDevice(row) {
      const { href } = this.$router.resolve({
        name: 'Track',
        query: { carId: row.carId }
      })
      window.open(href, '_blank')
    },
    // 回放
    replayTrack(row) {
      //  没有经纬度直接拦截
      if (!row.carStatus || !row.carStatus.lat || !row.carStatus.lon) {
        this.$message.error(this.$t('rEY8A9V_1uxYDZCM5MwZq'))
        return
      }
      const { href } = this.$router.resolve({
        name: 'History',
        query: {
          carId: row.carId,
          machineType: row.machineType,
          userId: row.userId,
          lat: row.carStatus.lat,
          lon: row.carStatus.lon,
          lang: this.$cookies.get('lang'),
          wiretype: '',
          carType: row.carType
        }
      })
      window.open(href, '_blank')
    },
    // 续费
    async userCharge() {
      let res = await getParentUserInfo()
      if (res.ret === 1) {
        let serviceData = res.data
        if (serviceData.imageURL) {
          serviceData.imageURL = '../..' + _api + '/image/getImage.do?imageId=' + serviceData.imageURL + '&token=' + this.$cookies.get('token')
        } else {
          if (!serviceData.imageURL) {
            serviceData.imageURL = publicUserpic
          }
        }

        this.$emit('service-renew', { serviceData })
      }
    },
    // 获取设备列表 表头
    async getDeviceColumn() {
      let res = await queryUserPerFence({
        token: this.$cookies.get('token'),
        userId: this.$cookies.get('userId'),
        perfenceId: 1
      })
      if (res) {
        if (undefined === res.data || undefined === res.data.perfenceString) {
          return
        }
        if (this.requestParames.length > 0) {
          this.deviceSearchParam.requestParames = this.requestParames.join(',')
          this.advancedSearchDeviceFun()
        }
        this.$nextTick(() => {
          this.$refs.deviceTable.doLayout()
        })
      }
    },

    // 改变自定义列
    changeColumns(obj, columnsIndex) {
      this.columns = obj
      this.requestParames = []
      this.columnsIndex = columnsIndex.toString()
      localStorage.setItem('table-custom-index', this.columnsIndex)
      this.columns.dayMileage ? this.requestParames.push('mileage') : true
      this.columns.overSpeedValue ? this.requestParames.push('overSpeed_value') : true
      if (this.requestParames.length > 0) {
        this.deviceSearchParam.requestParames = this.requestParames.join(',')
        this.advancedSearchDeviceFun()
      }
      // console.log(this.deviceSearchParam.requestParames)
      this.$nextTick(() => {
        this.$refs.deviceTable.doLayout()
      })
    },
    // 查看地址
    showAddress(row) {
      row.isLoading = true
      loadAddress({
        lon: row.carStatus.lon,
        lat: row.carStatus.lat,
        lonC: row.carStatus.lonc,
        latC: row.carStatus.latc,
        businessType: 19,
        carId: row.carId
      }).then(res => {
        row.isLoading = false
        row.location = res
      })
    },
    // 打开套餐详情
    openPackageDetail(info) {
      this.deviceInfo = info
      this.$emit('openPackageDetail', info)
    }
  },
  filters: {
    // 套餐类型
    packageFilter: function(value) {
      let str
      switch (value) {
        case 1:
          str = this.$t('vCx8UWyPG3dwdwKY7yN7I')
          break
        case 2:
          str = this.$t('yFydQode-qpYNF6prCBa8')
          break
        default:
          str = '-'
      }
      return str
    },
    voltageFilter: function(value) {
      if (value.carStatus.exData && value.carStatus.exData.indexOf('v=') > 0) {
        let arr = value.carStatus.exData.split(';')
        arr = arr.filter(item => {
          return item.indexOf('v=') === 0
        })
        let num = Number(arr[0].substring(2, arr[0].length))
        return (num / 1000).toFixed(2) + 'V'
      } else {
        return '—'
      }
    },
    tempFilter: function(value) {
      if (value.carStatus.exData && value.carStatus.exData.indexOf('t=') > 0) {
        let arr = value.carStatus.exData.split(';')
        arr = arr.filter(item => {
          return item.indexOf('t=') === 0
        })
        if (arr.length > 0) {
          let num = Number(arr[0].substring(2, arr[0].length))
          return num
        }
        return ''
      } else {
        return ''
      }
    },
    EmptyIntoLine(val) {
      if (!val && val !== 0) return '—'
      return val
    },
    dataFilter(val) {
      let str = '—'
      if (val) {
        str = new Date(val).Format('yyyy-MM-dd')
      }
      return str
    }
  }
}
</script>

<style lang="scss" scoped>
.client-tab-panel-container {
  box-sizing: border-box;
  height: 100%;
  padding: 0 20px 10px;
  .device-operate {
    @include flex-row(space-between);
    padding-bottom: 13px;
    .el-button {
      font-size: 14px;
      svg {
        margin-right: 5px;
        font-size: 16px;
        color: #666666;
      }
      &:hover,
      &:focus {
        svg {
          color: #3370ff;
        }
      }
    }
    .inside-device-search-panel {
      display: flex;
      align-items: center;
      .device-search-include-sub {
        margin-right: 20px;
        background-color: #f5f5f5ff;
        height: 32px;
        line-height: 32px;
        padding: 0 10px;
        border-radius: 6px;
      }
      .devide-search-Mul-Con {
        transition: transform 0.3s;
      }
      .devide-search-Mul-Con-roate {
        transform: rotateZ(-180deg);
      }
    }
  }
  .device-manage {
    box-sizing: border-box;
    height: calc(100% - 45px);
    ::v-deep .device-group-select {
      .el-input {
        padding-left: 0;
        .el-input__inner {
          border: 0;
          padding-left: 0;
        }
      }
    }
    .online {
      color: #42b041;
    }
    .offline {
      color: #8c8c8c;
    }
    .inactive {
      color: #d8d8d8;
    }
    .expire {
      color: #ff6648;
      & + span {
        color: #ff6648;
      }
    }
    .device_status_icon {
      width: 17px;
      height: 17px;
      margin-right: 4px;
      vertical-align: sub;
    }
    .moreOperation {
      width: 16px !important;
      height: 16px;
      margin-left: 27px;
      position: absolute;
      top: 3px;
    }

    .pet-icon {
      height: 32px;
    }
    .pet-default-icon {
      width: 15px;
      height: 15px;
    }

    .device-imei-container {
      &:hover {
        .copy-imei {
          display: inline-block;
        }
      }
    }
    ::v-deep .el-table {
      .cell {
        .device-imei {
          color: #3370ff;
          &:hover {
            cursor: pointer;
          }
        }
      }
    }

    .swd-pagination-container {
      position: relative;
      padding: 10px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #ffffff;
      .swd-download-btn {
        position: absolute;
        right: 0;
      }
    }
    // .pagination {
    //   width: 100%;
    //   margin-top: 20px;
    //   background-color: #ffffff;
    //   text-align: center;
    //   ::v-deep .el-pagination {
    //     display: inline-block;
    //   }
    // }
  }

  ::v-deep .el-table__header-wrapper {
    .el-table__column-filter-trigger {
      opacity: 0;
    }
    .highlight {
      color: $primary;
      svg {
        filter: drop-shadow($primary 333px 0px);
        transform: translateX(-333px);
        color: $primary;
      }
    }
  }
}

.export-button {
  width: 50px;
  height: 32px;
  @include flex-row;
  float: right;
}
.download-icon {
  font-size: 18px;
  color: #fff;
}

::v-deep .common-table-tr {
  font-size: 14px;
  th {
    height: 54px;
    .cell {
      // font-family: PingFangSC-Regular, PingFang SC !important;
      font-weight: 600;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    &.is-center {
      .cell {
        justify-content: center;
      }
    }
  }

  svg {
    font-size: 11px;
    margin-left: 5px;
    vertical-align: baseline;
  }
}
.Down-size {
  font-size: 12px !important;
  margin-right: 0 !important;
}
.el-button--text {
  font-size: 12px;
}
.device_status_filter {
  cursor: pointer;
  &:hover {
    filter: drop-shadow($primary 333px 0px);
    transform: translateX(-333px);
    color: $primary;
  }
}
</style>

<style lang="scss">
.c-device__button:hover svg {
  path {
    stroke: $primary;
  }
}
.c-device__button__renewal:hover {
  path:nth-child(2) {
    stroke: $primary;
  }
  path:nth-child(3) {
    fill: $primary;
  }
}
.c-device__button__import:hover {
  path:nth-child(2) {
    fill: $primary;
  }
}
.c-device__button__export:hover {
  .svg__stroke {
    stroke: $primary;
  }
}
.swd-pagination-popconfirm .el-popconfirm__main {
  margin: 14px 0;
}
.swd-pagination-container__cancel {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
.swd-pagination-container__selected {
  color: #707eae;
}
</style>
