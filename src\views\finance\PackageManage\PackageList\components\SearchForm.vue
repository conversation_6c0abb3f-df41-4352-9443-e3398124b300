<template>
  <div class="search-form">
    <el-form :inline="true">
      <el-form-item :label="$t('uigvo2391weT6pPqyfxYg') + ':'">
        <el-input class="width-179" clearable v-model.trim="searchParams.packName"></el-input>
      </el-form-item>
      <el-form-item :label="$t('qzwEjrvjWERzbbwcvI5rK') + ':'">
        <PackageType v-model="searchParams.packType" :list="packageTypeList" :default-props="{ children: 'childrens', label: 'name' }" param-key="code" />
      </el-form-item>
      <el-form-item :label="$t('lg.status') + ':'">
        <el-select class="width-179" v-model="searchParams.onSale">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <base-button type="primary" @click="handleSearch">
          {{ $t('lg.query') }}
        </base-button>
        <base-button color="#606266FF" type="default" icon="reset" class="reset-button__hover" @click="handleReset">
          {{ $t('lg.reset') }}
        </base-button>
      </el-form-item>
    </el-form>

    <div class="btn">
      <el-button type="default" @click="showEditUnit">{{ $t('2akZqOLanUYhOK--8J8k9') }}({{ voiceType | voiceTypeFilterVer3 }})</el-button>
    </div>
  </div>
</template>

<script>
import { getLastMonthNowDate } from '@/utils/common.js'
import pickerOptionMixinx from '@/mixins/pickerOptions.js'
import { mapGetters } from 'vuex'
import PackageType from '@/views/finance/PackageManage/PackageSetting/components/PackageType.vue'
import { getPackageClassify } from '@/api/order'
export default {
  components: { PackageType },
  mixins: [pickerOptionMixinx],
  data() {
    return {
      unit: 'M',
      isCNLang: null, // 中文状态
      pickDateArr: [new Date(getLastMonthNowDate()), new Date()],
      searchParams: {
        packName: '',
        packType: undefined,
        onSale: undefined,
        serviceProviderId: this.$cookies.get('bisUserId')
      },
      packTypeOptions: [
        { label: this.$t('dashloadi-NdxYrTzYiTv'), value: undefined },
        { label: this.$t('-sHvr4dqzhBg8zhkGisc4'), value: 1 },
        { label: this.$t('WNFXoZ2k0AQRuxAeHiePH'), value: 2 },
        { label: this.$t('WYZoPhp_aCouZhtTNRx_v'), value: 3 },
        { label: this.$t('ulJ0IYNw9yhO3xtOejMkq'), value: 4 },
        { label: this.$t('BxnsRGI8yOGF3AIgmEDPA'), value: 5 },
        { label: this.$t('S5BLPa5cR1lfkPtX8C9ra'), value: 6 },
        { label: this.$t('ZVF3com1sFG-XrZC-livm'), value: 7 },
        { label: this.$t('p0b0S03PgYUBMU8N6cMsY'), value: 8 },
        { label: this.$t('by_j84zg2KXDekxc0ORKK'), value: 9 },
        { label: this.$t('gi1Rx6yaRf1mw_Ma10Og7'), value: 10 },
        { label: this.$t('5l9_lUSa6C_lFR_eESOlJ'), value: 11 },
        { label: this.$t('PjztEjunq2DdH8CUhaTom'), value: 12 },
        { label: this.$t('unLEXxQ5Se8zqvgBrB8Qc'), value: 13 }
      ],
      statusOptions: [
        { label: this.$t('dashloadi-NdxYrTzYiTv'), value: undefined },
        { label: this.$t('cu1pVC4m4_ExdeETrqlaH'), value: 1 },
        { label: this.$t('_7CU8hvXPxWIHZ2Hw09lG'), value: 0 }
      ],
      packageTypeList: []
    }
  },
  computed: {
    ...mapGetters(['voiceType'])
  },
  created() {
    this.isCNLang = this.$cookies.get('lang') === 'cn' || this.$cookies.get('lang') === 'tw' ? true : false
  },
  mounted() {
    this.handleSearch()
    this.getPackageClassifyList()
  },
  methods: {
    // 打开编辑单位弹窗
    showEditUnit() {
      this.$emit('showEditUnit')
    },
    handleReset() {
      this.searchParams = {
        packName: '',
        packType: undefined,
        onSale: undefined,
        serviceProviderId: this.$cookies.get('bisUserId')
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$emit('search', this.searchParams)
    },
    async getPackageClassifyList() {
      const result = await getPackageClassify()
      console.log('result', result)

      this.packageTypeList = result.data
    }
  }
}
</script>

<style lang="scss" scoped>
.search-form {
  padding: 0px 20px;
  display: flex;
  justify-content: space-between;
}
.data-picker-wdth {
  width: 210px;
}

.el-form-item.el-form-item--small {
  margin-right: 30px;
}
.width-179 {
  width: 179px;
}
</style>
