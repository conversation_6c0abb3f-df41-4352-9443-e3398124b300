import { DOMESTIC_SITE_URLS } from '@/constant/common'
let IS_ABROAD = null
let env = process.env.NODE_ENV
let region = process.env.VUE_APP_REGION
// 根据域名判断是否是国外网站
export function isAbroad() {
  if (env === 'development') {
    // 开发环境： 本地存储判断是否是国外网站，代码没有setItem入口，这里判断是方便用浏览器脚本实现在开发环境切换国内外
    const localStorageValue = localStorage.getItem('judgeAbroad')
    // 正确处理字符串到布尔值的转换
    return localStorageValue === 'true'
  } else {
    // 生产环境：可以缓存结果
    if (IS_ABROAD === null) {
      IS_ABROAD = region === 'overseas'
    }
    return !!IS_ABROAD
  }
}
