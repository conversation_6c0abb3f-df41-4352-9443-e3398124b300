<template>
  <base-dialog
    :title="editRecord.length ? `${$t('lg.edit') + $t('lg.serviceProvide')}` : $t('K5b1Or2vbenyxUOLm6DzC')"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    @open="handleOpen"
    :before-close="handleClose"
    width="632px"
    @cancel="handleClose"
    @ok="handleNewServiceProviderOk"
  >
    <!-- 提示 -->
    <tips-block v-if="!Object.keys(editRecord).length" class="tips-box">
      <template v-slot:prefix>
        <img src="@/icons/images/tip3.png" alt="" class="expire-tip" />
      </template>
      <template v-slot:content>
        <span class="text">{{ $t('xgzOVj5OyHFE7FtwzEISW') }}</span>
      </template>
    </tips-block>
    <!-- 服务商 -->
    <!-- <SubAccount v-for="(item, index) in ServiceProviderEditVO" :key="index" v-model="item.value" @close="handleDelete(index)" /> -->
    <!-- 滚动容器 -->

    <div class="scroll-container" v-if="visible">
      <AddService
        v-for="(item, index) in ServiceProviderEditVO"
        ref="addServiceRef"
        :edit-record="editRecord[index]"
        :componentIndex="index"
        :key="item.key"
        @close="handleDelete(index)"
      />
    </div>

    <!-- 添加 -->
    <div class="add-container" v-if="!Object.keys(editRecord).length" @click="addServiceProvider">
      <PcFinanceAdd size="20" class="ljdw-pr-8" />
      <span>{{ $t('JNNF3uGWW4u7fYgJtmHcL') }}</span>
    </div>
    <div class="settins-container" v-if="configObj.isServiceAdmin && editRecord.length">
      <ServiceProviderSettings ref="settingsRef" :edit-record="editRecord[0]" />
    </div>
  </base-dialog>
</template>

<script>
import { _addServiceProvider, _queryServiceConfig, updateProportion } from '@/api/order.js'
import { mapGetters } from 'vuex'
// 组件
import TipsBlock from '@/components/Bussiness/TipsBlock.vue'
import AddService from './AddService.vue'
import ServiceProviderSettings from './ServiceProviderSettings'
import { getIsShowNewServiceProvider } from '../config'
import { PcFinanceAdd } from '@/assets/icon'

export default {
  name: 'AddServiceProvider',
  components: {
    TipsBlock,
    AddService,
    PcFinanceAdd,
    ServiceProviderSettings
  },
  props: {
    visible: Boolean,
    editRecord: {
      type: Array,
      default: () => []
    }
  },
  inject: ['configObj'],
  data() {
    return {
      // ServiceProviderEditVO: [{ value: { bisUserId: null, proportion: null } }], //服务商编辑实体
      ServiceProviderEditVO: [{ bisUserId: null, proportion: null, key: 0 }], //服务商编辑实体
      proportion: 1, // 可用分账比例
      initKey: 0, // key计数器
      inputDisabled: false, // 输入框禁用
      initInputValue: null, // 输入框初始值
      isShowNewServiceProvider: getIsShowNewServiceProvider(),
      addContractDialogVisible: false
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'currencyParams']),
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val) // openCardDialog改变的时候通知父组件
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        // 初始化参数
        this.initParams()
        // 查询配置比例
        this.queryServiceConfig()
      }
    }
  },

  methods: {
    // 添加设备服务商
    addServiceProvider() {
      // this.ServiceProviderEditVO.push({ value: { bisUserId: null, name: null, proportion: null } })
      this.initKey++
      this.ServiceProviderEditVO.push({ name: null, bisUserId: null, proportion: null, key: this.initKey })
    },
    // 打开
    handleOpen() {},
    // 关闭
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    // 确定
    async handleConfirm() {
      // bisUserId	B端用户id
      // name	服务商名称
      // proportion	比例

      try {
        let arr = []
        let valid = true
        let lists = this.ServiceProviderEditVO
        for (let i = 0; i < lists.length; i++) {
          if (!lists[i].bisUserId) {
            this.$message.error(this.$t('llSrRUSNQCGG3Tzw4CSoM'))
            valid = false
          } else if (!lists[i].proportion && lists[i].proportion !== 0) {
            this.$message.error(this.$t('et_LYLHFrM04XhGjP46tP'))
            valid = false
          } else if ((lists[i].proportion > this.proportion || lists[i].proportion < 1) && this.userInfo.userType !== 0) {
            this.$message.error(this.$t('K-fRsoopgmoiVxQIwvZFl'))
            valid = false
            break
          } else {
            arr.push({
              bisUserId: lists[i].bisUserId,
              proportion: lists[i].proportion,
              name: lists[i].name,
              currency: this.currencyParams
            })
          }
        }
        if (!valid) {
          return
        }
        let count = [...new Set(arr.map(elem => elem.bisUserId))].length
        if (count !== arr.length) {
          this.$message.error(this.$t('NowXQsGP0xEGBs1WjE9_Q'))
          return
        }
        if (arr.length) {
          let res = await _addServiceProvider(arr)
          if (res.ret == 1) {
            this.$message.success(this.$t('6dWutEmqMZnfcebUwMWcH'))
            this.$emit('refreshTable')
            this.handleClose()
          } else {
            this.$message.error(res.msg)
          }
        } else {
          this.$message.error(this.$t('uiwsh872y89cb781c289s'))
        }
      } catch (error) {
        throw new Error(error)
      }
    },
    // 获取分配比例
    // 查询服务商配置信息
    async queryServiceConfig() {
      try {
        let res = await _queryServiceConfig()
        if (res.ret === 1) {
          this.proportion = res.data?.proportion || 1
        }
      } catch (error) {
        throw new Error(error)
      }
    },
    // 删除项
    handleDelete(index) {
      this.ServiceProviderEditVO.splice(index, 1)
    },
    // 组件数据更改
    handleChange(index, info) {
      let obj = this.ServiceProviderEditVO[index]
      let newObj = Object.assign(obj, info)
      this.ServiceProviderEditVO.splice(index, 1, newObj)
    },
    // 初始化组件
    initParams() {
      if (this.userInfo.userType === 0) {
        this.inputDisabled = true
        this.initInputValue = 90
      }
      this.ServiceProviderEditVO = [{ name: null, bisUserId: null, proportion: null, key: 0 }]
      this.proportion = 1
      this.initKey = 0
    },
    // 处理 2024.1.1 新逻辑
    async handleNewServiceProviderOk() {
      const isEdit = this.editRecord.length > 0
      const api = isEdit ? updateProportion : _addServiceProvider
      const params = []
      const ids = new Set()
      for (const comp of this.$refs.addServiceRef) {
        const param = await comp.getFormData()
        if (param) {
          ids.add(param.bisUserId)
          params.push(param)
        }
      }

      if (ids.size !== params.length) {
        this.$message.error(this.$t('NowXQsGP0xEGBs1WjE9_Q'))
        return
      }

      if (this.$refs.addServiceRef.length !== params.length) {
        return
      }

      //编辑服务商显示金额和导出权限
      let configSetting = {}
      if (this.configObj.isServiceAdmin && this.editRecord.length) {
        configSetting = this.$refs.settingsRef.getFormData()
        console.log('configSetting', configSetting)
        console.log('params', params[0])

        await this.$confirm(this.$t('qingquerenfu_fc4bcc'), this.$t('lg.notification'), {
          confirmButtonText: this.$t('lg.confirm'),
          cancelButtonText: this.$t('lg.cancel'),
          type: 'warning'
        })
      }
      const contractNumber = isEdit ? this.$refs.settingsRef.getContractNumber() : null
      const result = await api(isEdit ? { ...params[0], ...configSetting, contractNumber } : params)
      if (!result.ret) {
        return this.$message.error(result.msg)
      }
      this.$message.success(this.$t('6dWutEmqMZnfcebUwMWcH'))
      this.handleClose()
      this.$emit('refreshTable')
    }
  }
}
</script>

<style lang="scss" scoped>
.expire-tip {
  vertical-align: bottom;
}
.tips-box {
  display: flex;
  align-items: center;
  border-radius: 6px;
  .text {
    line-height: 26px;
  }
}
.add-container {
  display: flex;
  align-items: center;
  color: $primary;
  cursor: pointer;
}
.scroll-container {
  position: relative;
  width: 100%;
  // max-height: 500px;
  overflow: auto;
  &::-webkit-scrollbar {
    /*高宽分别对应横竖滚动条的尺寸*/
    width: 5px;
    height: 1px;
  }
  .box {
    margin-bottom: 14px;
  }
}
</style>
