export const dataConfig = [
  {
    label: '场景名称',
    key: 'sceneName',
    prop: 'sceneName'
  },
  {
    label: '关联设备型号',
    key: 'machineTypeList',
    prop: 'machineTypeList',
    width: 850
  },
  {
    label: '会员中心启用',
    key: 'enableStatus',
    prop: 'enableStatus',

    align: 'center'
  },
  {
    label: '排序',
    key: 'sort',
    prop: 'sort',
    width: 100,
    align: 'center'
  },
  {
    label: '操作',
    key: 'operation',
    prop: 'operation',
    width: 150,
    fixed: 'right'
  }
]

// 分页配置
export const defaultPaginationConfig = {
  pageIndex: 1,
  pageSize: 15,
  total: 0
}

// 语言选项配置
export const languageOptions = [
  { label: '英语', value: 'en' },
  { label: '简体中文', value: 'cn' },
  { label: '繁体中文', value: 'hk' },
  { label: '西班牙语', value: 'es' },
  { label: '葡萄牙语', value: 'pt' },
  { label: '俄语', value: 'ru' },
  { label: '德语', value: 'de' },
  { label: '法语', value: 'fr' }
]
