<template>
  <div class="basic-info">
    <el-form label-width="100px" class="ad-form">
      <el-form-item :label="$t('EmaS6irOhwdMjUdKb7kpt')" :inline="true">
        <span
          class="ad-status"
          :class="{
            online: adInfo.status === 'ONLINE',
            offline: adInfo.status === 'OFFLINE',
            expired: adInfo.status === 'EXPIRED',
            notOnline: adInfo.status === 'NOT_ONLINE',
            draft: adInfo.status === 'DRAFT'
          }"
          >{{ adStatus }}</span
        >
      </el-form-item>
      <el-divider></el-divider>
      <el-form-item :label="$t('oFuc9t_2oQw5HruLy8wPn')">
        <span>{{ adInfo.adNo != null ? adInfo.adNo : '-' }}</span>
      </el-form-item>
      <el-form-item :label="$t('sxNR6X6-YPM-rM3UYnGC_')">
        <span>{{ adInfo.name || '-' }}</span>
      </el-form-item>
      <el-form-item :label="$t('fSSnMr-IMjus9Nm2qaloa')">
        <span>{{ targetUserTypeText }}</span>
      </el-form-item>
      <el-form-item :label="$t('7GGCLgtrmSuZ3yX7QKlUB')">
        <span>{{ adPositionText }}</span>
      </el-form-item>
      <!-- 设备型号 -->
      <el-form-item :label="$t('Faq4yaX0d6aAiKJuLoqDy')">
        <span class="wrap-text">{{ getDeviceModelText }}</span>
      </el-form-item>
      <el-form-item :label="$t('s8ggwKHE3jM4A355doFBv')">
        <span>{{ adInfo.startTime ? timeConvert(adInfo.startTime, 'local') : '-' }} - {{ adInfo.endTime ? timeConvert(adInfo.endTime, 'local') : '-' }}</span>
      </el-form-item>
      <el-form-item :label="$t('mziSJbA1dzcUsPsiiLLBE')">
        <span>{{ adTypeText }}</span>
      </el-form-item>
      <el-form-item :label="$t('Q_XGjoL48fZZVJzBlyXW7')">
        <div v-if="adInfo.langContents && adInfo.langContents.length" class="images-container">
          <div v-for="(item, index) in adInfo.langContents" :key="`image-${index}`" class="image-item">
            <el-image v-if="item.image" :src="item.image" fit="cover" :preview-src-list="getImagesList" class="ad-image">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </div>
        </div>
        <span v-else>-</span>
      </el-form-item>
      <el-form-item label="URL：">
        <span>{{ getUrlText }}</span>
      </el-form-item>
      <el-form-item :label="$t('z1M9X6X4Jc_lcHUZPY2yo')">
        <span>{{ adInfo.sort != null ? adInfo.sort : '-' }}</span>
      </el-form-item>
      <el-divider></el-divider>
      <el-form-item :label="$t('Mxt4sLL2QJGL5YrRL4TOQ')">
        <span>{{ adInfo.operator || '-' }}</span>
      </el-form-item>
      <el-form-item :label="$t('EiJhbLu2h-Rx5MX_YXwHW')">
        <span>{{ adInfo.updateTime ? timeConvert(adInfo.updateTime, 'local') : '-' }}</span>
      </el-form-item>
    </el-form>
    <div class="operate-bar" v-if="adInfo && adInfo.status">
      <!-- 草稿状态：发布、编辑、删除 -->
      <template v-if="adInfo.status === 'DRAFT'">
        <el-button type="primary" @click="handlePublish">{{ $t('s5dkA82oFKqgptUbqsic4') }}</el-button>
        <el-button type="primary" @click="handleEdit">{{ $t('lg.edit') }}</el-button>
        <el-button type="danger" @click="handleDelete">{{ $t('lg.delete') }}</el-button>
      </template>

      <!-- 待上线状态：回退、删除 -->
      <template v-else-if="adInfo.status === 'NOT_ONLINE'">
        <el-button type="warning" @click="handleRollback">{{ $t('8Y9n6AMUzmJgLppRkiPqC') }}</el-button>
        <el-button type="danger" @click="handleDelete">{{ $t('lg.delete') }}</el-button>
      </template>

      <!-- 已上线状态：下线 -->
      <template v-else-if="adInfo.status === 'ONLINE'">
        <el-button type="danger" @click="handleOffline">{{ $t('ybYXmIEwb2gFT3jw0NhiZ') }}</el-button>
      </template>

      <!-- 已过期状态：删除 -->
      <template v-else-if="adInfo.status === 'EXPIRED'">
        <el-button type="danger" @click="handleDelete">{{ $t('lg.delete') }}</el-button>
      </template>

      <!-- 已下线状态：删除 -->
      <template v-else-if="adInfo.status === 'OFFLINE'">
        <el-button type="danger" @click="handleDelete">{{ $t('lg.delete') }}</el-button>
      </template>
    </div>
  </div>
</template>

<script>
import { timeConvert } from '@/utils/date'
import { statusMap, adPositionOptions, targetUserOptions, adTypeOptions } from '@/views/Operation/AdManager/constant/config'
import { LANGUAGE_OPTIONS } from '@/views/Operation/AdManager/constant/config'
export default {
  name: 'BasicInfo',
  props: {
    adInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      LANGUAGE_OPTIONS
    }
  },
  computed: {
    adStatus() {
      return statusMap[this.adInfo.status] || this.adInfo.status || '-'
    },
    adPositionText() {
      const found = adPositionOptions.find(item => item.value === this.adInfo.adPosition)
      return found ? found.label : this.adInfo.adPosition || '-'
    },
    targetUserTypeText() {
      const found = targetUserOptions.find(item => item.value === this.adInfo.targetUserType)
      return found ? found.label : this.adInfo.targetUserType || '-'
    },
    adTypeText() {
      const found = adTypeOptions.find(item => item.value === this.adInfo.adType)
      return found ? found.label : this.adInfo.adType || '-'
    },
    getDefaultLangContent() {
      // 优先获取英文内容，如果没有则获取中文内容，如果都没有则获取第一个语言内容
      if (!this.adInfo.langContents || !this.adInfo.langContents.length) return null

      const enContent = this.adInfo.langContents.find(item => item.lang === 'en')
      if (enContent) return enContent

      const cnContent = this.adInfo.langContents.find(item => item.lang === 'cn')
      if (cnContent) return cnContent

      return this.adInfo.langContents[0]
    },
    getImagesList() {
      const imageUrls = []
      if (this.adInfo.langContents) {
        this.adInfo.langContents.forEach(item => {
          if (item.image) {
            imageUrls.push(item.image)
          }
        })
      }
      return imageUrls
    },
    getDeviceModelText() {
      if (!this.adInfo.relateDevices) return '-'
      const deviceModels = this.adInfo.relateDevices.map(item => item.machineTypeName).join(',')
      return deviceModels
    },
    getUrlText() {
      let resText = ''

      this.adInfo?.langContents?.forEach(item => {
        if (item.url) {
          resText += item.url + ','
        }
      })
      return resText.slice(0, -1)
    }
  },
  methods: {
    timeConvert,
    handlePublish() {
      this.$emit('publish')
    },
    handleEdit() {
      this.$emit('edit')
    },
    handleDelete() {
      this.$emit('delete')
    },
    handleOffline() {
      this.$emit('offline')
    },
    handleRollback() {
      this.$emit('rollback')
    }
  }
}
</script>

<style lang="scss" scoped>
.basic-info {
  height: 100%;
  .ad-status {
    font-weight: bold;
    font-size: 14px;
    padding: 5px 10px;
    border-radius: 5px;
    color: #409eff;
    border: 1px solid #409eff;
  }
  .ad-status.online {
    color: #67c23a;
    border: 1px solid #67c23a;
  }
  .ad-status.offline {
    color: #909399;
    border: 1px solid #909399;
  }
  .ad-status.expired {
    color: #f56c6c;
    border: 1px solid #f56c6c;
  }
  .ad-status.notOnline {
    color: #f56c6c;
    border: 1px solid #f56c6c;
  }
  .ad-status.draft {
    color: #409eff;
    border: 1px solid #409eff;
  }
  .operate-bar {
    display: flex;
    padding-left: 10px;
    padding-top: 10px;
    padding-bottom: 10px;

    justify-content: flex-start;
    align-items: center;
    border-top: 1px solid #eee;
    .el-button {
      margin-right: 10px;
    }
  }
  .ad-form {
    max-height: calc(100vh - 245px);

    overflow-y: auto;
  }

  .images-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .image-item {
      position: relative;
      margin-bottom: 15px;

      .ad-image {
        width: 150px;
        height: 100px;
        border-radius: 4px;
        border: 1px solid #eee;

        &:hover {
          border-color: #409eff;
        }

        .image-slot {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          background: #f5f7fa;
          color: #909399;

          i {
            font-size: 24px;
          }
        }
      }

      .image-lang {
        text-align: center;
        margin-top: 5px;
        font-size: 12px;
        color: #606266;
      }
    }
  }
}
::v-deep .el-divider {
  margin: 10px 0;
}
.wrap-text {
  display: inline-block;
  width: 50%;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  line-height: 1.5;
}
</style>
