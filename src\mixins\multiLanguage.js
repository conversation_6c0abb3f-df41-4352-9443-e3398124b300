import { isAbroad } from '@/utils/judge.js'

const LANGUAGE_OPTIONS = [
  { label: '英语', value: 'en' },
  { label: '简体中文', value: 'cn' },
  { label: '繁体中文', value: 'hk' },
  { label: '西班牙语', value: 'es' },
  { label: '葡萄牙语', value: 'pt' },
  { label: '俄语', value: 'ru' },
  { label: '德语', value: 'de' },
  { label: '法语', value: 'fr' }
]

export default {
  data() {
    return {
      currentLang: this.getDefaultLang(),
      languageOptions: LANGUAGE_OPTIONS
    }
  },

  computed: {
    defaultLang() {
      return this.getDefaultLang()
    },
    onDefaultTab() {
      return this.currentLang === this.defaultLang
    }
  },

  methods: {
    isAbroad,

    /**
     * 获取默认语言
     */
    getDefaultLang() {
      return isAbroad() ? 'en' : 'cn'
    },

    /**
     * 初始化多语言数据结构
     * @param {Array} fields - 需要初始化的字段数组
     * @returns {Object} 初始化后的langData对象
     */
    initLangData(fields = []) {
      const langData = {}
      this.languageOptions.forEach(lang => {
        langData[lang.value] = {}
        fields.forEach(field => {
          // 如果字段名包含Id，初始化为undefined，否则为空字符串
          langData[lang.value][field] = field.includes('Id') ? undefined : ''
        })
      })
      return langData
    },

    /**
     * 设置多语言验证规则
     * @param {Object} baseRules - 基础验证规则
     * @param {Array} requiredFields - 必填字段数组
     * @param {Object} messageMap - 字段对应的提示信息 {字段名: 提示信息}
     * @returns {Object} 完整的验证规则
     */
    setupLangValidationRules(baseRules = {}, requiredFields = [], messageMap = {}) {
      const langRules = { ...baseRules }

      // 设置英文必填验证
      requiredFields.forEach(field => {
        langRules[`langData.en.${field}`] = [
          {
            required: true,
            message: messageMap[field] || `请输入${field}`,
            trigger: 'blur'
          }
        ]
      })

      // 如果是国内环境，添加中文验证
      if (!isAbroad()) {
        requiredFields.forEach(field => {
          langRules[`langData.cn.${field}`] = [
            {
              required: true,
              message: messageMap[field] || `请输入${field}`,
              trigger: 'blur'
            }
          ]
        })
      }

      return langRules
    },

    /**
     * 通用的Tab切换验证 - 子组件可重写此方法
     * @param {string} tab - 目标Tab
     * @param {string} oldTab - 当前Tab
     * @returns {boolean} 是否允许切换
     */
    handleBeforeLeave(tab, oldTab) {
      if (this.validateTabSwitch) {
        return this.validateTabSwitch(tab, oldTab)
      }
      return true
    },
    // 自定义Tab切换验证
    validateTabSwitch(tab, oldTab) {
      if (oldTab === 'en') {
        this.$refs.formRef.validate(async valid => {
          if (!valid) {
            this.$message.error(this.$t('FIgAdMCZbTjUXRp-BVY-9'))
            this.$nextTick(() => {
              this.currentLang = oldTab
            })
            return false
          }
        })
      }
      return true
    },
    /**
     * 重置当前语言到默认语言
     */
    resetCurrentLang() {
      this.currentLang = this.defaultLang
    }
  }
}
