// ==UserScript==
// @name         Localhost 国内外网站切换器
// @namespace    http://tampermonkey.net/
// @version      1.2
// @description  油猴脚本，按F2键切换localhost环境下的judgeAbroad状态，用于开发环境测试国内外网站功能。沙箱运行，不会污染本地环境
// <AUTHOR>
// @match        http://localhost:*/*
// @match        https://localhost:*/*
// @match        http://127.0.0.1:*/*
// @match        https://127.0.0.1:*/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    let statusWidget = null;
    const POSITION_KEY = 'judgeAbroadWidgetPosition';

    /**
     * 切换judgeAbroad状态的核心函数
     */
    function toggleJudgeAbroad() {
        const currentValue = localStorage.getItem('judgeAbroad');
        let newValue;

        // 如果没有值或为'false'字符串，设置为'true'
        // 如果为'true'字符串，设置为'false'
        if (!currentValue || currentValue === 'false') {
            newValue = 'true';
        } else {
            newValue = 'false';
        }

        // 设置新值
        localStorage.setItem('judgeAbroad', newValue);

        // 控制台提示
        console.log(`%c[国内外切换] judgeAbroad 已切换为: ${newValue}`,
                   'color: #409EFF; font-weight: bold;');

        // 显示临时提示（可选）
        showTemporaryNotification(`已切换为${newValue === 'true' ? '国外' : '国内'}网站模式`);

        // 重新加载页面
        setTimeout(() => {
            location.reload();
        }, 500); // 延迟500ms让用户看到提示
    }

    /**
     * 保存容器位置到localStorage
     * @param {number} x X坐标
     * @param {number} y Y坐标
     */
    function saveWidgetPosition(x, y) {
        const position = { x, y };
        localStorage.setItem(POSITION_KEY, JSON.stringify(position));
    }

    /**
     * 从localStorage获取容器位置
     * @returns {Object} 位置信息 {x, y}
     */
    function getWidgetPosition() {
        try {
            const saved = localStorage.getItem(POSITION_KEY);
            if (saved) {
                return JSON.parse(saved);
            }
        } catch (e) {
            console.warn('[国内外切换] 位置数据读取失败:', e);
        }
        // 默认位置（右上角）
        return { x: window.innerWidth - 140, y: 20 };
    }

    /**
     * 创建状态显示容器
     */
    function createStatusWidget() {
        const currentValue = localStorage.getItem('judgeAbroad') || 'false';
        const isAbroad = currentValue === 'true';
        
        // 创建主容器
        const widget = document.createElement('div');
        widget.id = 'judgeAbroadWidget';
        widget.innerHTML = `
            <div class="widget-header">
                <span class="widget-title">环境状态</span>
                <button class="widget-close">×</button>
            </div>
            <div class="widget-content">
                <div class="status-indicator ${isAbroad ? 'abroad' : 'domestic'}">
                    <span class="status-dot"></span>
                    <span class="status-text">${isAbroad ? '国外模式' : '国内模式'}</span>
                </div>
                <div class="widget-tip">按F2切换</div>
            </div>
        `;

        // 获取保存的位置
        const savedPosition = getWidgetPosition();
        
        // 设置样式
        widget.style.cssText = `
            position: fixed;
            left: ${savedPosition.x}px;
            top: ${savedPosition.y}px;
            width: 120px;
            background: #ffffff;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 12px;
            cursor: move;
            user-select: none;
            transition: opacity 0.3s ease;
        `;

        // 添加内部样式
        const style = document.createElement('style');
        style.textContent = `
            #judgeAbroadWidget .widget-header {
                background: #f5f7fa;
                padding: 8px 10px;
                border-bottom: 1px solid #e4e7ed;
                border-radius: 7px 7px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: move;
            }
            
            #judgeAbroadWidget .widget-title {
                font-weight: 500;
                color: #303133;
                font-size: 12px;
            }
            
            #judgeAbroadWidget .widget-close {
                background: none;
                border: none;
                font-size: 16px;
                color: #909399;
                cursor: pointer;
                padding: 0;
                width: 16px;
                height: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 2px;
                line-height: 1;
            }
            
            #judgeAbroadWidget .widget-close:hover {
                background: #f56c6c;
                color: white;
            }
            
            #judgeAbroadWidget .widget-content {
                padding: 12px;
            }
            
            #judgeAbroadWidget .status-indicator {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
            }
            
            #judgeAbroadWidget .status-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin-right: 6px;
                display: inline-block;
                flex-shrink: 0;
            }
            
            #judgeAbroadWidget .status-indicator.domestic .status-dot {
                background: #67c23a;
                box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
            }
            
            #judgeAbroadWidget .status-indicator.abroad .status-dot {
                background: #409eff;
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
            
            #judgeAbroadWidget .status-text {
                font-weight: 500;
                color: #303133;
                font-size: 12px;
            }
            
            #judgeAbroadWidget .widget-tip {
                color: #909399;
                text-align: center;
                font-size: 11px;
            }
            
            #judgeAbroadWidget.dragging {
                opacity: 0.8;
                transform: scale(1.02);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(widget);

        // 添加拖拽功能
        makeDraggable(widget);

        // 添加关闭功能
        const closeBtn = widget.querySelector('.widget-close');
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation(); // 阻止事件冒泡
            widget.style.opacity = '0';
            setTimeout(() => {
                if (widget.parentNode) {
                    document.body.removeChild(widget);
                    statusWidget = null;
                }
            }, 300);
        });

        return widget;
    }

    /**
     * 使元素可拖拽
     * @param {HTMLElement} element 要拖拽的元素
     */
    function makeDraggable(element) {
        let isDragging = false;
        let startX = 0;
        let startY = 0;
        let currentX = 0;
        let currentY = 0;

        const header = element.querySelector('.widget-header');

        function handleStart(e) {
            // 如果点击的是关闭按钮，不启动拖拽
            if (e.target.closest('.widget-close')) {
                return;
            }

            isDragging = true;
            element.classList.add('dragging');

            // 获取鼠标/触摸位置
            const clientX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
            const clientY = e.type === 'mousedown' ? e.clientY : e.touches[0].clientY;

            // 计算鼠标相对于元素的位置
            const rect = element.getBoundingClientRect();
            startX = clientX - rect.left;
            startY = clientY - rect.top;

            e.preventDefault();
        }

        function handleMove(e) {
            if (!isDragging) return;

            e.preventDefault();

            // 获取当前鼠标/触摸位置
            const clientX = e.type === 'mousemove' ? e.clientX : e.touches[0].clientX;
            const clientY = e.type === 'mousemove' ? e.clientY : e.touches[0].clientY;

            // 计算新位置
            currentX = clientX - startX;
            currentY = clientY - startY;

            // 限制在窗口范围内
            const maxX = window.innerWidth - element.offsetWidth;
            const maxY = window.innerHeight - element.offsetHeight;

            currentX = Math.max(0, Math.min(currentX, maxX));
            currentY = Math.max(0, Math.min(currentY, maxY));

            // 应用新位置
            element.style.left = currentX + 'px';
            element.style.top = currentY + 'px';
        }

        function handleEnd() {
            if (!isDragging) return;

            isDragging = false;
            element.classList.remove('dragging');

            // 保存位置到localStorage
            saveWidgetPosition(currentX, currentY);

            console.log(`[国内外切换] 位置已保存: x=${currentX}, y=${currentY}`);
        }

        // 鼠标事件
        header.addEventListener('mousedown', handleStart);
        document.addEventListener('mousemove', handleMove);
        document.addEventListener('mouseup', handleEnd);

        // 触摸事件（移动端支持）
        header.addEventListener('touchstart', handleStart, { passive: false });
        document.addEventListener('touchmove', handleMove, { passive: false });
        document.addEventListener('touchend', handleEnd);

        // 防止拖拽时选中文本
        header.addEventListener('selectstart', (e) => e.preventDefault());
    }

    /**
     * 显示临时通知
     * @param {string} message 通知消息
     */
    function showTemporaryNotification(message) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #409EFF;
            color: white;
            padding: 12px 20px;
            border-radius: 4px;
            z-index: 10001;
            font-size: 14px;
            font-family: Arial, sans-serif;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            transition: opacity 0.3s ease;
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 2秒后移除
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 2000);
    }

    /**
     * 键盘事件监听器
     * @param {KeyboardEvent} event 键盘事件
     */
    function handleKeydown(event) {
        // 检测F2键（keyCode: 113, key: 'F2'）
        if (event.key === 'F2' || event.keyCode === 113) {
            event.preventDefault(); // 阻止默认行为
            toggleJudgeAbroad();
        }
    }

    /**
     * 初始化脚本
     */
    function init() {
        // 绑定键盘事件监听器
        document.addEventListener('keydown', handleKeydown, true);

        // 创建状态显示容器
        statusWidget = createStatusWidget();

        // 在控制台显示当前状态
        const currentValue = localStorage.getItem('judgeAbroad') || 'false';
        console.log(`%c[国内外切换] 当前状态: judgeAbroad=${currentValue} (${currentValue === 'true' ? '国外' : '国内'}模式)`,
                   'color: #67C23A; font-weight: bold;');
        console.log('%c[国内外切换] 按F2键可切换国内外网站模式',
                   'color: #909399;');
        
        // 显示位置信息
        const position = getWidgetPosition();
        console.log(`%c[国内外切换] 容器位置: x=${position.x}, y=${position.y}`,
                   'color: #909399;');
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();