<template>
  <div class="more-condition-bar">
    <el-form :inline="true" class="more-condition-form">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item :label="$t('atBMWwI4wBA1fjp9rDMUL')">
            <el-input v-model="localSearchParams.adNo" :placeholder="$t('bzVAxsJEtQtawIDU5RBt1')" @blur="onSearch" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('Bkq0Yczk7b865d7nN8M8W')">
            <el-input v-model="localSearchParams.name" :placeholder="$t('bzVAxsJEtQtawIDU5RBt1')" @blur="onSearch" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('bmp6Q586E8NHP4TOjI9wy')">
            <el-select v-model="localSearchParams.targetUserType" clearable :placeholder="$t('lg.chosetarget')" @change="onSearch">
              <el-option v-for="item in targetUserOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item :label="$t('0PRcq7VkoU6VCeXMG6lv_')">
            <el-date-picker
              v-model="localSearchParams.dateRange"
              type="daterange"
              range-separator="~"
              :start-placeholder="$t('lg.selectStartTime')"
              :end-placeholder="$t('lg.selectEndTime')"
              @change="onSearch"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('pTu4TJ_3imdKY9kGkrkN4')">
            <el-select v-model="localSearchParams.adPosition" clearable :placeholder="$t('wZpmnbEoDIWkbpBtxW5gU')" @change="onSearch">
              <el-option v-for="item in adPositionOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('OgP1t0z_sac6t8wWYfsip')">
            <el-select v-model="localSearchParams.status" clearable :placeholder="$t('uO5Hxlkc71BV8-Ipub1Lv')" @change="onSearch">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="search-btn-area">
      <base-button type="primary" @click="onSearch">{{ $t('lg.query') }}</base-button>
      <base-button color="#606266FF" type="default" icon="reset" class="reset-button__hover" @click="onReset"> {{ $t('lg.reset') }}</base-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MoreConditionBar',
  props: {
    searchParams: {
      type: Object,
      required: true
    },
    targetUserOptions: {
      type: Array,
      default: () => []
    },
    adPositionOptions: {
      type: Array,
      default: () => []
    },
    statusOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      localSearchParams: { ...this.searchParams }
    }
  },
  watch: {
    searchParams: {
      handler(newVal) {
        this.localSearchParams = { ...newVal }
      },
      deep: true
    }
  },
  methods: {
    onSearch() {
      this.$emit('update:searchParams', { ...this.localSearchParams })
      this.$emit('search')
    },
    onReset() {
      this.$emit('reset')
    }
  }
}
</script>

<style lang="scss" scoped>
.more-condition-bar {
  padding-top: 16px;
  border-top: 1px solid #e5e5e5;
  display: flex;
  align-items: flex-start;
}
.more-condition-form {
  flex: 1;
}
.search-btn-area {
  display: flex;
  flex-direction: column;
  gap: 10px;
  .el-button {
    margin-left: 0;
    margin-right: 10px;
  }
}
</style>
