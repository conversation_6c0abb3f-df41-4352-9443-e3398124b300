<template>
  <div class="package-list-edit">
    <base-dialog
      @cancel="handleClose"
      @ok="handleConfirm"
      :title="$t('lg.edit')"
      :visible.sync="dialogVisible"
      width="422px"
      :before-close="handleClose"
      @open="handleOpen"
    >
      <div class="form">
        <el-form ref="form" :model="editForm" label-width="100px" :rules="rules">
          <el-form-item :label="$t('uigvo2391weT6pPqyfxYg') + '：'" prop="packName">
            <el-input class="width-240" v-model="editForm.packName" disabled></el-input>
          </el-form-item>
          <el-form-item :label="$t('eGPJZha0zDcNjnOUxMl5f') + '：'" prop="salePrice" :show-message="false" class="min-margin-bottom">
            <el-input class="width-240" v-model="editForm.salePrice">
              <template slot="suffix">
                <span> {{ currencyParams == 'CNY' ? $t('j_nyP6IyvoRo9kejtwukP') : $t('gQPrng0oObyjc6BgSvrjD') }}</span>
              </template>
            </el-input>
            <div class="tip">
              <span>{{ $t('wgxerTVV3ubJSwGQbIRXK') }}：</span>
              <span>{{ editForm.guidingPrice }}</span>
            </div>
          </el-form-item>
          <el-form-item :label="$t('pWM8WfOGzZzcyBJMF4w4Q') + '：'" prop="discount" :show-message="false">
            <!-- class="min-margin-bottom" -->
            <el-input class="width-240" v-model="editForm.discount">
              <template slot="suffix">
                <span> %</span>
              </template>
            </el-input>
            <!-- <div class="tip" v-if="[9, 12].includes(+this.editForm.packType)">
              <span>{{ $t('lhZ_M3uoptVK76DILnZ2l') }}</span>
            </div> -->
          </el-form-item>
          <el-form-item :label="$t('lg.status') + ':'" prop="onSale">
            <el-select class="width-240" v-model="editForm.onSale">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('HbdiV2RLW3mpJDkZ_KYFG') + ':'" prop="onSale">
            <el-input-number v-model="editForm.sort" controls-position="right" :min="0" :max="9999"></el-input-number>
          </el-form-item>
        </el-form>
      </div>
    </base-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { _editPackageList } from '@/api/order.js'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      editForm: {
        serviceProviderId: this.$cookies.get('bisUserId'),
        agentConfigId: undefined,
        packName: '',
        agentPrice: '',
        guidingPrice: '',
        salePrice: '',
        discount: '',
        onSale: undefined,
        sort: 0
      },
      discountDisabled: false, //手机定位类-9，好友增加类-12，禁用销售折扣
      statusOptions: [
        { label: this.$t('cu1pVC4m4_ExdeETrqlaH'), value: 1 },
        { label: this.$t('_7CU8hvXPxWIHZ2Hw09lG'), value: 0 }
      ],
      rules: {
        packName: [{ required: true }],
        salePrice: [{ required: true, message: '' }],
        discount: [{ type: 'date', required: true, message: '请选择日期', trigger: 'change' }],
        onSale: [{ required: true, trigger: 'change' }]
      }
    }
  },
  computed: {
    ...mapGetters(['currencyParams']),
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val) // openCardDialog改变的时候通知父组件
      }
    }
  },
  methods: {
    resetForm() {},
    // 点击确定
    async handleConfirm() {
      if (!this.editForm.salePrice) {
        this.$message.warning(this.$t('Lf5yEIWEaqmGp-MdVGn6K'))
        return
      }
      if (!/^\d+(\.\d{1,2})?$/.test(+this.editForm.salePrice)) {
        this.$message.warning(this.$t('PnNbXwUM5AIUvXD55zozb'))
        return
      }
      if (!this.editForm.discount) {
        this.$message.warning(this.$t('_0RCRKXXXIzu2xxfKtnTl'))
        return
      }
      if (!/^100$|^(\d|[1-9]\d)$/.test(+this.editForm.discount)) {
        this.$message.warning(this.$t('8SZSVQ78DZJPp5CIkPmPA'))
        return
      }
      if ((+this.editForm.salePrice * +this.editForm.discount) / 100 < +this.editForm.agentPrice) {
        this.$message.warning(this.$t('ikXI1IxXKIQp8u1h9AA07'))
        return
      }
      if (this.editForm.onSale == 1 && this.editForm.packStatus == 0) {
        this.$message.warning(this.$t('1tG8AEnd8XObHHyr_LJWO'))
        return
      }
      if (this.editForm.onSale == 0 && this.editForm.standard == 1) {
        this.$message.warning(this.$t('hqIAi-yNhcVJJes_Wii8D'))
        return
      }
      if (this.editForm.renewType) {
        this.$confirm('', this.$t('EFUKd-0PFs2kje_Z0c1h9'), {
          message: `<span style="color:red">${this.$t('ADC7SxVEyPAWy5cdn2lKb')}</span>`,
          dangerouslyUseHTMLString: true,
          type: 'warning'
        })
          .then(() => {
            this.handleSubmit()
          })
          .catch(e => {
            console.log(e)
          })
      } else {
        this.handleSubmit()
      }
    },
    async handleSubmit() {
      try {
        // let params = JSON.parse(JSON.stringify(this.editForm))
        // params.discount = params.discount / 100
        let res = await _editPackageList(this.editForm)
        if (res.ret === 1) {
          this.$emit('success')
          this.$message.success(this.$t('lg.success'))
          this.handleClose()
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        throw new Error(error)
      }
    },
    handleClose() {
      this.resetForm()
      this.$emit('update:visible', false)
    },
    // dialog打开
    handleOpen() {
      this.editForm = { ...this.editForm, ...this.info }
      if ([9, 12].includes(this.editForm.packType)) {
        // this.discountDisabled = true //手机定位类-9，好友增加类-12，禁用销售折扣
        // this.editForm.salePrice = this.editForm.guidingPrice
      } else {
        this.discountDisabled = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.package-list-edit {
  .el-form {
    .tip {
      font-size: 12px;
      color: #8d8d8d;
      line-height: 20px;
    }
    .el-form-item.min-margin-bottom {
      margin-bottom: 3px;
    }
  }
}

.width-240 {
  width: 240px;
}
:deep(.el-input-number.is-controls-right .el-input-number__increase) {
  border-radius: 0 8px 0 0;
}
:deep(.el-input-number.is-controls-right .el-input-number__decrease) {
  border-radius: 0 0 8px 0;
}
</style>
