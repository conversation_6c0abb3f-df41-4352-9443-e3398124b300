<template>
  <div class="package-type-manage">
    <search-form @search="handleSearch" @add="handleAdd" @reset="handleReset" />
    <Table ref="packageTable" @edit="handleEdit" />
    <AddEditDailog v-if="dialogVisible" :visible.sync="dialogVisible" :isEdit="isEdit" :editData="currentRecord" @success="handleSuccess" />
  </div>
</template>

<script>
import { isAbroad } from '@/utils/judge'
import Table from './components/Table.vue'
import AddEditDailog from './components/AddEditDailog.vue'
import SearchForm from './components/SearchForm.vue'
import _ from 'lodash'

export default {
  name: 'PackageTypeManage',
  components: {
    Table,
    AddEditDailog,
    SearchForm
  },
  data() {
    return {
      searchConditions: {
        packageTypeName: '',
        machineTypeIds: [],
        enableStatus: ''
      },
      dialogVisible: false,
      isEdit: false,
      currentRecord: {}
    }
  },
  computed: {
    defaultLang() {
      return this.getDefaultLang()
    }
  },
  methods: {
    isAbroad,
    getDefaultLang() {
      return isAbroad() ? 'en' : 'cn'
    },

    /**
     * 处理搜索按钮点击
     */
    handleSearch(searchConditions) {
      this.searchConditions = searchConditions || this.searchConditions
      this.$refs.packageTable.refresh(this.searchConditions)
    },

    /**
     * 处理重置按钮点击
     */
    handleReset() {
      // 调用表格的重置方法，让各组件自己清空条件
      this.$refs.packageTable.reset()
    },

    /**
     * 处理新增按钮点击
     */
    handleAdd() {
      this.isEdit = false
      this.currentRecord = {}
      this.dialogVisible = true
    },

    /**
     * 处理编辑操作
     * @param {Object} record - 要编辑的记录
     */
    handleEdit(record) {
      this.isEdit = true
      this.currentRecord = _.cloneDeep(record)
      this.dialogVisible = true
    },

    /**
     * 处理新增/编辑成功
     */
    handleSuccess() {
      this.$refs.packageTable.refresh()
    }
  }
}
</script>

<style lang="scss" scoped>
.package-type-manage {
  padding: 0 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
