export const dataConfig = [
  {
    label: '套餐类型',
    key: 'packTypeName',
    prop: 'packTypeName',
    width: 200
  },
  {
    label: '所属场景',
    key: 'sceneName',
    prop: 'sceneName',
    width: 200
  },
  {
    label: '关联设备型号',
    key: 'machineTypeList',
    prop: 'machineTypeList',
    width: 600
  },
  {
    label: '增值项',
    key: 'functionList',
    prop: 'functionList'
  },
  {
    label: '排序号',
    key: 'sort',
    prop: 'sort',

    align: 'center'
  },
  {
    label: '会员中心启用状态',
    key: 'enableStatus',
    prop: 'enableStatus',

    align: 'center'
  },
  {
    label: '操作',
    key: 'operation',
    prop: 'operation',
    width: 150,
    fixed: 'right'
  }
]

// 分页配置
export const defaultPaginationConfig = {
  pageIndex: 1,
  pageSize: 15,
  total: 0
}
